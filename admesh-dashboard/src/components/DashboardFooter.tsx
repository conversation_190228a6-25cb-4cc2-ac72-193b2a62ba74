"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mail, Linkedin, MessageCircle } from "lucide-react";

export default function DashboardFooter() {
  const handleEmailClick = () => {
    window.open("mailto:<EMAIL>", "_blank");
  };

  const handleLinkedInClick = () => {
    window.open("https://www.linkedin.com/in/manikumargouni", "_blank");
  };

  return (
    <footer className="mt-12 mb-6 mx-4 sm:mx-6 lg:mx-8">
      <div className="max-w-7xl mx-auto">
        <Card className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50 border-slate-200 dark:border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Question Section */}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <MessageCircle className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-sm">Have a question?</h3>
                <p className="text-xs text-muted-foreground">We&apos;re here to help you succeed</p>
              </div>
            </div>

            {/* Contact Buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleEmailClick}
                className="flex items-center gap-2"
              >
                <Mail className="h-4 w-4" />
                <span className="hidden sm:inline">Email</span>
                <span className="sm:hidden"><EMAIL></span>
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleLinkedInClick}
                className="flex items-center gap-2"
              >
                <Linkedin className="h-4 w-4" />
                <span className="hidden sm:inline">LinkedIn</span>
                <span className="sm:hidden">Connect</span>
              </Button>
            </div>
          </div>

          {/* Mobile Contact Info */}
          <div className="sm:hidden mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
            <div className="flex flex-col gap-2 text-center">
              <p className="text-xs text-muted-foreground">
                Email: <span className="text-foreground font-medium"><EMAIL></span>
              </p>
              <p className="text-xs text-muted-foreground">
                LinkedIn: <span className="text-foreground font-medium">Mani Kumar Gouni</span>
              </p>
            </div>
          </div>
        </CardContent>
        </Card>
      </div>
    </footer>
  );
}
