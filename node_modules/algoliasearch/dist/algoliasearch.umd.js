(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.algoliasearch = {}));
})(this, (function (exports) { 'use strict';

	function v(){function c(h){return new Promise(d=>{let m=new XMLHttpRequest;m.open(h.method,h.url,true),Object.keys(h.headers).forEach(t=>m.setRequestHeader(t,h.headers[t]));let w=(t,e)=>setTimeout(()=>{m.abort(),d({status:0,content:e,isTimedOut:true});},t),q=w(h.connectTimeout,"Connection timeout"),l;m.onreadystatechange=()=>{m.readyState>m.OPENED&&l===void 0&&(clearTimeout(q),l=w(h.responseTimeout,"Socket timeout"));},m.onerror=()=>{m.status===0&&(clearTimeout(q),clearTimeout(l),d({content:m.responseText||"Network request failed",status:m.status,isTimedOut:false}));},m.onload=()=>{clearTimeout(q),clearTimeout(l),d({content:m.responseText,status:m.status,isTimedOut:false});},m.send(h.data);})}return {send:c}}function I(c){let h,d=`algolia-client-js-${c.key}`;function m(){return h===void 0&&(h=c.localStorage||window.localStorage),h}function w(){return JSON.parse(m().getItem(d)||"{}")}function q(t){m().setItem(d,JSON.stringify(t));}function l(){let t=c.timeToLive?c.timeToLive*1e3:null,e=w(),r=Object.fromEntries(Object.entries(e).filter(([,s])=>s.timestamp!==void 0));if(q(r),!t)return;let a=Object.fromEntries(Object.entries(r).filter(([,s])=>{let o=new Date().getTime();return !(s.timestamp+t<o)}));q(a);}return {get(t,e,r={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(l(),w()[JSON.stringify(t)])).then(a=>Promise.all([a?a.value:e(),a!==void 0])).then(([a,s])=>Promise.all([a,s||r.miss(a)])).then(([a])=>a)},set(t,e){return Promise.resolve().then(()=>{let r=w();return r[JSON.stringify(t)]={timestamp:new Date().getTime(),value:e},m().setItem(d,JSON.stringify(r)),e})},delete(t){return Promise.resolve().then(()=>{let e=w();delete e[JSON.stringify(t)],m().setItem(d,JSON.stringify(e));})},clear(){return Promise.resolve().then(()=>{m().removeItem(d);})}}}function ye(){return {get(c,h,d={miss:()=>Promise.resolve()}){return h().then(w=>Promise.all([w,d.miss(w)])).then(([w])=>w)},set(c,h){return Promise.resolve(h)},delete(c){return Promise.resolve()},clear(){return Promise.resolve()}}}function T(c){let h=[...c.caches],d=h.shift();return d===void 0?ye():{get(m,w,q={miss:()=>Promise.resolve()}){return d.get(m,w,q).catch(()=>T({caches:h}).get(m,w,q))},set(m,w){return d.set(m,w).catch(()=>T({caches:h}).set(m,w))},delete(m){return d.delete(m).catch(()=>T({caches:h}).delete(m))},clear(){return d.clear().catch(()=>T({caches:h}).clear())}}}function E(c={serializable:true}){let h={};return {get(d,m,w={miss:()=>Promise.resolve()}){let q=JSON.stringify(d);if(q in h)return Promise.resolve(c.serializable?JSON.parse(h[q]):h[q]);let l=m();return l.then(t=>w.miss(t)).then(()=>l)},set(d,m){return h[JSON.stringify(d)]=c.serializable?JSON.stringify(m):m,Promise.resolve(m)},delete(d){return delete h[JSON.stringify(d)],Promise.resolve()},clear(){return h={},Promise.resolve()}}}function Ee(c){let h={value:`Algolia for JavaScript (${c})`,add(d){let m=`; ${d.segment}${d.version!==void 0?` (${d.version})`:""}`;return h.value.indexOf(m)===-1&&(h.value=`${h.value}${m}`),h}};return h}function R(c,h,d="WithinHeaders"){let m={"x-algolia-api-key":h,"x-algolia-application-id":c};return {headers(){return d==="WithinHeaders"?m:{}},queryParameters(){return d==="WithinQueryParameters"?m:{}}}}function k({func:c,validate:h,aggregator:d,error:m,timeout:w=()=>0}){let q=l=>new Promise((t,e)=>{c(l).then(async r=>(d&&await d(r),await h(r)?t(r):m&&await m.validate(r)?e(new Error(await m.message(r))):setTimeout(()=>{q(r).then(t).catch(e);},await w()))).catch(r=>{e(r);});});return q()}function C({algoliaAgents:c,client:h,version:d}){let m=Ee(d).add({segment:h,version:d});return c.forEach(w=>m.add(w)),m}function b(){return {debug(c,h){return Promise.resolve()},info(c,h){return Promise.resolve()},error(c,h){return Promise.resolve()}}}var V=2*60*1e3;function M(c,h="up"){let d=Date.now();function m(){return h==="up"||Date.now()-d>V}function w(){return h==="timed out"&&Date.now()-d<=V}return {...c,status:h,lastUpdate:d,isUp:m,isTimedOut:w}}var J=class extends Error{name="AlgoliaError";constructor(c,h){super(c),h&&(this.name=h);}};var K=class extends J{stackTrace;constructor(c,h,d){super(c,d),this.stackTrace=h;}},Se=class extends K{constructor(c){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",c,"RetryError");}},N=class extends K{status;constructor(c,h,d,m="ApiError"){super(c,d,m),this.status=h;}},Te=class extends J{response;constructor(c,h){super(c,"DeserializationError"),this.response=h;}},ve=class extends N{error;constructor(c,h,d,m){super(c,h,m,"DetailedApiError"),this.error=d;}};function j(c){let h=c;for(let d=c.length-1;d>0;d--){let m=Math.floor(Math.random()*(d+1)),w=c[d];h[d]=c[m],h[m]=w;}return h}function Ie(c,h,d){let m=Re(d),w=`${c.protocol}://${c.url}${c.port?`:${c.port}`:""}/${h.charAt(0)==="/"?h.substring(1):h}`;return m.length&&(w+=`?${m}`),w}function Re(c){return Object.keys(c).filter(h=>c[h]!==void 0).sort().map(h=>`${h}=${encodeURIComponent(Object.prototype.toString.call(c[h])==="[object Array]"?c[h].join(","):c[h]).replace(/\+/g,"%20")}`).join("&")}function Ce(c,h){if(c.method==="GET"||c.data===void 0&&h.data===void 0)return;let d=Array.isArray(c.data)?c.data:{...c.data,...h.data};return JSON.stringify(d)}function be(c,h,d){let m={Accept:"application/json",...c,...h,...d},w={};return Object.keys(m).forEach(q=>{let l=m[q];w[q.toLowerCase()]=l;}),w}function Ae(c){try{return JSON.parse(c.content)}catch(h){throw new Te(h.message,c)}}function De({content:c,status:h},d){try{let m=JSON.parse(c);return "error"in m?new ve(m.message,h,m.error,d):new N(m.message,h,d)}catch{}return new N(c,h,d)}function xe({isTimedOut:c,status:h}){return !c&&~~h===0}function ke({isTimedOut:c,status:h}){return c||xe({isTimedOut:c,status:h})||~~(h/100)!==2&&~~(h/100)!==4}function Oe({status:c}){return ~~(c/100)===2}function Ue(c){return c.map(h=>X(h))}function X(c){let h=c.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...c,request:{...c.request,headers:{...c.request.headers,...h}}}}function A({hosts:c,hostsCache:h,baseHeaders:d,logger:m,baseQueryParameters:w,algoliaAgent:q,timeouts:l,requester:t,requestsCache:e,responsesCache:r}){async function a(n){let i=await Promise.all(n.map(y=>h.get(y,()=>Promise.resolve(M(y))))),u=i.filter(y=>y.isUp()),g=i.filter(y=>y.isTimedOut()),P=[...u,...g];return {hosts:P.length>0?P:n,getTimeout(y,f){return (g.length===0&&y===0?1:g.length+3+y)*f}}}async function s(n,i,u=true){let g=[],P=Ce(n,i),p=be(d,n.headers,i.headers),y=n.method==="GET"?{...n.data,...i.data}:{},f={...w,...n.queryParameters,...y};if(q.value&&(f["x-algolia-agent"]=q.value),i&&i.queryParameters)for(let x of Object.keys(i.queryParameters))!i.queryParameters[x]||Object.prototype.toString.call(i.queryParameters[x])==="[object Object]"?f[x]=i.queryParameters[x]:f[x]=i.queryParameters[x].toString();let S=0,D=async(x,F)=>{let U=x.pop();if(U===void 0)throw new Se(Ue(g));let B={...l,...i.timeouts},$={data:P,headers:p,method:n.method,url:Ie(U,n.path,f),connectTimeout:F(S,B.connect),responseTimeout:F(S,u?B.read:B.write)},z=L=>{let _={request:$,response:L,host:U,triesLeft:x.length};return g.push(_),_},O=await t.send($);if(ke(O)){let L=z(O);return O.isTimedOut&&S++,m.info("Retryable failure",X(L)),await h.set(U,M(U,O.isTimedOut?"timed out":"down")),D(x,F)}if(Oe(O))return Ae(O);throw z(O),De(O,g)},G=c.filter(x=>x.accept==="readWrite"||(u?x.accept==="read":x.accept==="write")),Q=await a(G);return D([...Q.hosts].reverse(),Q.getTimeout)}function o(n,i={}){let u=n.useReadTransporter||n.method==="GET";if(!u)return s(n,i,u);let g=()=>s(n,i);if((i.cacheable||n.cacheable)!==true)return g();let p={request:n,requestOptions:i,transporter:{queryParameters:w,headers:d}};return r.get(p,()=>e.get(p,()=>e.set(p,g()).then(y=>Promise.all([e.delete(p),y]),y=>Promise.all([e.delete(p),Promise.reject(y)])).then(([y,f])=>f)),{miss:y=>r.set(p,y)})}return {hostsCache:h,requester:t,timeouts:l,logger:m,algoliaAgent:q,baseHeaders:d,baseQueryParameters:w,hosts:c,request:o,requestsCache:e,responsesCache:r}}var Z="5.30.0",Y=["de","us"];function Ne(c){return [{url:c?"analytics.{region}.algolia.com".replace("{region}",c):"analytics.algolia.com",accept:"readWrite",protocol:"https"}]}function je({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:Ne(w),...q,algoliaAgent:C({algoliaAgents:m,client:"Abtesting",version:Z}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},addABTests(e,r){if(!e)throw new Error("Parameter `addABTestsRequest` is required when calling `addABTests`.");if(!e.name)throw new Error("Parameter `addABTestsRequest.name` is required when calling `addABTests`.");if(!e.variants)throw new Error("Parameter `addABTestsRequest.variants` is required when calling `addABTests`.");if(!e.endAt)throw new Error("Parameter `addABTestsRequest.endAt` is required when calling `addABTests`.");let n={method:"POST",path:"/2/abtests",queryParameters:{},headers:{},data:e};return t.request(n,r)},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},deleteABTest({id:e},r){if(!e)throw new Error("Parameter `id` is required when calling `deleteABTest`.");let n={method:"DELETE",path:"/2/abtests/{id}".replace("{id}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},estimateABTest(e,r){if(!e)throw new Error("Parameter `estimateABTestRequest` is required when calling `estimateABTest`.");if(!e.configuration)throw new Error("Parameter `estimateABTestRequest.configuration` is required when calling `estimateABTest`.");if(!e.variants)throw new Error("Parameter `estimateABTestRequest.variants` is required when calling `estimateABTest`.");let n={method:"POST",path:"/2/abtests/estimate",queryParameters:{},headers:{},data:e};return t.request(n,r)},getABTest({id:e},r){if(!e)throw new Error("Parameter `id` is required when calling `getABTest`.");let n={method:"GET",path:"/2/abtests/{id}".replace("{id}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},listABTests({offset:e,limit:r,indexPrefix:a,indexSuffix:s}={},o=void 0){let n="/2/abtests",i={},u={};e!==void 0&&(u.offset=e.toString()),r!==void 0&&(u.limit=r.toString()),a!==void 0&&(u.indexPrefix=a.toString()),s!==void 0&&(u.indexSuffix=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},scheduleABTest(e,r){if(!e)throw new Error("Parameter `scheduleABTestsRequest` is required when calling `scheduleABTest`.");if(!e.name)throw new Error("Parameter `scheduleABTestsRequest.name` is required when calling `scheduleABTest`.");if(!e.variants)throw new Error("Parameter `scheduleABTestsRequest.variants` is required when calling `scheduleABTest`.");if(!e.scheduledAt)throw new Error("Parameter `scheduleABTestsRequest.scheduledAt` is required when calling `scheduleABTest`.");if(!e.endAt)throw new Error("Parameter `scheduleABTestsRequest.endAt` is required when calling `scheduleABTest`.");let n={method:"POST",path:"/2/abtests/schedule",queryParameters:{},headers:{},data:e};return t.request(n,r)},stopABTest({id:e},r){if(!e)throw new Error("Parameter `id` is required when calling `stopABTest`.");let n={method:"POST",path:"/2/abtests/{id}/stop".replace("{id}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)}}}function ee(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(d&&(typeof d!="string"||!Y.includes(d)))throw new Error(`\`region\` must be one of the following: ${Y.join(", ")}`);return je({appId:c,apiKey:h,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${Z}-${c}`}),E()]}),...m})}var te="5.30.0",re=["de","us"];function Ge(c){return [{url:c?"analytics.{region}.algolia.com".replace("{region}",c):"analytics.algolia.com",accept:"readWrite",protocol:"https"}]}function Fe({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:Ge(w),...q,algoliaAgent:C({algoliaAgents:m,client:"Analytics",version:te}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},getAddToCartRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getAddToCartRate`.");let n="/2/conversions/addToCartRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getAverageClickPosition({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getAverageClickPosition`.");let n="/2/clicks/averageClickPosition",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getClickPositions({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getClickPositions`.");let n="/2/clicks/positions",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getClickThroughRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getClickThroughRate`.");let n="/2/clicks/clickThroughRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getConversionRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getConversionRate`.");let n="/2/conversions/conversionRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getNoClickRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getNoClickRate`.");let n="/2/searches/noClickRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getNoResultsRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getNoResultsRate`.");let n="/2/searches/noResultRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getPurchaseRate({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getPurchaseRate`.");let n="/2/conversions/purchaseRate",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getRevenue({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getRevenue`.");let n="/2/conversions/revenue",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getSearchesCount({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesCount`.");let n="/2/searches/count",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},getSearchesNoClicks({index:e,startDate:r,endDate:a,limit:s,offset:o,tags:n},i){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesNoClicks`.");let u="/2/searches/noClicks",g={},P={};e!==void 0&&(P.index=e.toString()),r!==void 0&&(P.startDate=r.toString()),a!==void 0&&(P.endDate=a.toString()),s!==void 0&&(P.limit=s.toString()),o!==void 0&&(P.offset=o.toString()),n!==void 0&&(P.tags=n.toString());let p={method:"GET",path:u,queryParameters:P,headers:g};return t.request(p,i)},getSearchesNoResults({index:e,startDate:r,endDate:a,limit:s,offset:o,tags:n},i){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesNoResults`.");let u="/2/searches/noResults",g={},P={};e!==void 0&&(P.index=e.toString()),r!==void 0&&(P.startDate=r.toString()),a!==void 0&&(P.endDate=a.toString()),s!==void 0&&(P.limit=s.toString()),o!==void 0&&(P.offset=o.toString()),n!==void 0&&(P.tags=n.toString());let p={method:"GET",path:u,queryParameters:P,headers:g};return t.request(p,i)},getStatus({index:e},r){if(!e)throw new Error("Parameter `index` is required when calling `getStatus`.");let a="/2/status",s={},o={};e!==void 0&&(o.index=e.toString());let n={method:"GET",path:a,queryParameters:o,headers:s};return t.request(n,r)},getTopCountries({index:e,startDate:r,endDate:a,limit:s,offset:o,tags:n},i){if(!e)throw new Error("Parameter `index` is required when calling `getTopCountries`.");let u="/2/countries",g={},P={};e!==void 0&&(P.index=e.toString()),r!==void 0&&(P.startDate=r.toString()),a!==void 0&&(P.endDate=a.toString()),s!==void 0&&(P.limit=s.toString()),o!==void 0&&(P.offset=o.toString()),n!==void 0&&(P.tags=n.toString());let p={method:"GET",path:u,queryParameters:P,headers:g};return t.request(p,i)},getTopFilterAttributes({index:e,search:r,startDate:a,endDate:s,limit:o,offset:n,tags:i},u){if(!e)throw new Error("Parameter `index` is required when calling `getTopFilterAttributes`.");let g="/2/filters",P={},p={};e!==void 0&&(p.index=e.toString()),r!==void 0&&(p.search=r.toString()),a!==void 0&&(p.startDate=a.toString()),s!==void 0&&(p.endDate=s.toString()),o!==void 0&&(p.limit=o.toString()),n!==void 0&&(p.offset=n.toString()),i!==void 0&&(p.tags=i.toString());let y={method:"GET",path:g,queryParameters:p,headers:P};return t.request(y,u)},getTopFilterForAttribute({attribute:e,index:r,search:a,startDate:s,endDate:o,limit:n,offset:i,tags:u},g){if(!e)throw new Error("Parameter `attribute` is required when calling `getTopFilterForAttribute`.");if(!r)throw new Error("Parameter `index` is required when calling `getTopFilterForAttribute`.");let P="/2/filters/{attribute}".replace("{attribute}",encodeURIComponent(e)),p={},y={};r!==void 0&&(y.index=r.toString()),a!==void 0&&(y.search=a.toString()),s!==void 0&&(y.startDate=s.toString()),o!==void 0&&(y.endDate=o.toString()),n!==void 0&&(y.limit=n.toString()),i!==void 0&&(y.offset=i.toString()),u!==void 0&&(y.tags=u.toString());let f={method:"GET",path:P,queryParameters:y,headers:p};return t.request(f,g)},getTopFiltersNoResults({index:e,search:r,startDate:a,endDate:s,limit:o,offset:n,tags:i},u){if(!e)throw new Error("Parameter `index` is required when calling `getTopFiltersNoResults`.");let g="/2/filters/noResults",P={},p={};e!==void 0&&(p.index=e.toString()),r!==void 0&&(p.search=r.toString()),a!==void 0&&(p.startDate=a.toString()),s!==void 0&&(p.endDate=s.toString()),o!==void 0&&(p.limit=o.toString()),n!==void 0&&(p.offset=n.toString()),i!==void 0&&(p.tags=i.toString());let y={method:"GET",path:g,queryParameters:p,headers:P};return t.request(y,u)},getTopHits({index:e,search:r,clickAnalytics:a,revenueAnalytics:s,startDate:o,endDate:n,limit:i,offset:u,tags:g},P){if(!e)throw new Error("Parameter `index` is required when calling `getTopHits`.");let p="/2/hits",y={},f={};e!==void 0&&(f.index=e.toString()),r!==void 0&&(f.search=r.toString()),a!==void 0&&(f.clickAnalytics=a.toString()),s!==void 0&&(f.revenueAnalytics=s.toString()),o!==void 0&&(f.startDate=o.toString()),n!==void 0&&(f.endDate=n.toString()),i!==void 0&&(f.limit=i.toString()),u!==void 0&&(f.offset=u.toString()),g!==void 0&&(f.tags=g.toString());let S={method:"GET",path:p,queryParameters:f,headers:y};return t.request(S,P)},getTopSearches({index:e,clickAnalytics:r,revenueAnalytics:a,startDate:s,endDate:o,orderBy:n,direction:i,limit:u,offset:g,tags:P},p){if(!e)throw new Error("Parameter `index` is required when calling `getTopSearches`.");let y="/2/searches",f={},S={};e!==void 0&&(S.index=e.toString()),r!==void 0&&(S.clickAnalytics=r.toString()),a!==void 0&&(S.revenueAnalytics=a.toString()),s!==void 0&&(S.startDate=s.toString()),o!==void 0&&(S.endDate=o.toString()),n!==void 0&&(S.orderBy=n.toString()),i!==void 0&&(S.direction=i.toString()),u!==void 0&&(S.limit=u.toString()),g!==void 0&&(S.offset=g.toString()),P!==void 0&&(S.tags=P.toString());let D={method:"GET",path:y,queryParameters:S,headers:f};return t.request(D,p)},getUsersCount({index:e,startDate:r,endDate:a,tags:s},o){if(!e)throw new Error("Parameter `index` is required when calling `getUsersCount`.");let n="/2/users/count",i={},u={};e!==void 0&&(u.index=e.toString()),r!==void 0&&(u.startDate=r.toString()),a!==void 0&&(u.endDate=a.toString()),s!==void 0&&(u.tags=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)}}}function ae(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(d&&(typeof d!="string"||!re.includes(d)))throw new Error(`\`region\` must be one of the following: ${re.join(", ")}`);return Fe({appId:c,apiKey:h,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${te}-${c}`}),E()]}),...m})}var ne="5.30.0",se=["de","us"];function Be(c){return [{url:c?"insights.{region}.algolia.io".replace("{region}",c):"insights.algolia.io",accept:"readWrite",protocol:"https"}]}function Le({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:Be(w),...q,algoliaAgent:C({algoliaAgents:m,client:"Insights",version:ne}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},deleteUserToken({userToken:e},r){if(!e)throw new Error("Parameter `userToken` is required when calling `deleteUserToken`.");let n={method:"DELETE",path:"/1/usertokens/{userToken}".replace("{userToken}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},pushEvents(e,r){if(!e)throw new Error("Parameter `insightsEvents` is required when calling `pushEvents`.");if(!e.events)throw new Error("Parameter `insightsEvents.events` is required when calling `pushEvents`.");let n={method:"POST",path:"/1/events",queryParameters:{},headers:{},data:e};return t.request(n,r)}}}function oe(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(d&&(typeof d!="string"||!se.includes(d)))throw new Error(`\`region\` must be one of the following: ${se.join(", ")}`);return Le({appId:c,apiKey:h,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${ne}-${c}`}),E()]}),...m})}var ce="5.30.0",ie=["eu","us"];function We(c){return [{url:"personalization.{region}.algolia.com".replace("{region}",c),accept:"readWrite",protocol:"https"}]}function He({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:We(w),...q,algoliaAgent:C({algoliaAgents:m,client:"Personalization",version:ce}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},deleteUserProfile({userToken:e},r){if(!e)throw new Error("Parameter `userToken` is required when calling `deleteUserProfile`.");let n={method:"DELETE",path:"/1/profiles/{userToken}".replace("{userToken}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getPersonalizationStrategy(e){let o={method:"GET",path:"/1/strategies/personalization",queryParameters:{},headers:{}};return t.request(o,e)},getUserTokenProfile({userToken:e},r){if(!e)throw new Error("Parameter `userToken` is required when calling `getUserTokenProfile`.");let n={method:"GET",path:"/1/profiles/personalization/{userToken}".replace("{userToken}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},setPersonalizationStrategy(e,r){if(!e)throw new Error("Parameter `personalizationStrategyParams` is required when calling `setPersonalizationStrategy`.");if(!e.eventsScoring)throw new Error("Parameter `personalizationStrategyParams.eventsScoring` is required when calling `setPersonalizationStrategy`.");if(!e.facetsScoring)throw new Error("Parameter `personalizationStrategyParams.facetsScoring` is required when calling `setPersonalizationStrategy`.");if(!e.personalizationImpact)throw new Error("Parameter `personalizationStrategyParams.personalizationImpact` is required when calling `setPersonalizationStrategy`.");let n={method:"POST",path:"/1/strategies/personalization",queryParameters:{},headers:{},data:e};return t.request(n,r)}}}function ue(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(!d||d&&(typeof d!="string"||!ie.includes(d)))throw new Error(`\`region\` is required and must be one of the following: ${ie.join(", ")}`);return He({appId:c,apiKey:h,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${ce}-${c}`}),E()]}),...m})}var de="5.30.0",he=["eu","us"];function Qe(c){return [{url:"query-suggestions.{region}.algolia.com".replace("{region}",c),accept:"readWrite",protocol:"https"}]}function $e({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:Qe(w),...q,algoliaAgent:C({algoliaAgents:m,client:"QuerySuggestions",version:de}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},createConfig(e,r){if(!e)throw new Error("Parameter `configurationWithIndex` is required when calling `createConfig`.");let n={method:"POST",path:"/1/configs",queryParameters:{},headers:{},data:e};return t.request(n,r)},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},deleteConfig({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteConfig`.");let n={method:"DELETE",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getAllConfigs(e){let o={method:"GET",path:"/1/configs",queryParameters:{},headers:{}};return t.request(o,e)},getConfig({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `getConfig`.");let n={method:"GET",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getConfigStatus({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `getConfigStatus`.");let n={method:"GET",path:"/1/configs/{indexName}/status".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getLogFile({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `getLogFile`.");let n={method:"GET",path:"/1/logs/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},updateConfig({indexName:e,configuration:r},a){if(!e)throw new Error("Parameter `indexName` is required when calling `updateConfig`.");if(!r)throw new Error("Parameter `configuration` is required when calling `updateConfig`.");if(!r.sourceIndices)throw new Error("Parameter `configuration.sourceIndices` is required when calling `updateConfig`.");let i={method:"PUT",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)}}}function le(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(!d||d&&(typeof d!="string"||!he.includes(d)))throw new Error(`\`region\` is required and must be one of the following: ${he.join(", ")}`);return $e({appId:c,apiKey:h,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${de}-${c}`}),E()]}),...m})}var W="5.30.0";function ze(c){return [{url:`${c}-dsn.algolia.net`,accept:"read",protocol:"https"},{url:`${c}.algolia.net`,accept:"write",protocol:"https"}].concat(j([{url:`${c}-1.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${c}-2.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${c}-3.algolianet.com`,accept:"readWrite",protocol:"https"}]))}function _e({appId:c,apiKey:h,authMode:d,algoliaAgents:m,...w}){let q=R(c,h,d),l=A({hosts:ze(c),...w,algoliaAgent:C({algoliaAgents:m,client:"Search",version:W}),baseHeaders:{"content-type":"text/plain",...q.headers(),...w.baseHeaders},baseQueryParameters:{...q.queryParameters(),...w.baseQueryParameters}});return {transporter:l,appId:c,apiKey:h,clearCache(){return Promise.all([l.requestsCache.clear(),l.responsesCache.clear()]).then(()=>{})},get _ua(){return l.algoliaAgent.value},addAlgoliaAgent(t,e){l.algoliaAgent.add({segment:t,version:e});},setClientApiKey({apiKey:t}){!d||d==="WithinHeaders"?l.baseHeaders["x-algolia-api-key"]=t:l.baseQueryParameters["x-algolia-api-key"]=t;},waitForTask({indexName:t,taskID:e,maxRetries:r=50,timeout:a=o=>Math.min(o*200,5e3)},s){let o=0;return k({func:()=>this.getTask({indexName:t,taskID:e},s),validate:n=>n.status==="published",aggregator:()=>o+=1,error:{validate:()=>o>=r,message:()=>`The maximum number of retries exceeded. (${o}/${r})`},timeout:()=>a(o)})},waitForAppTask({taskID:t,maxRetries:e=50,timeout:r=s=>Math.min(s*200,5e3)},a){let s=0;return k({func:()=>this.getAppTask({taskID:t},a),validate:o=>o.status==="published",aggregator:()=>s+=1,error:{validate:()=>s>=e,message:()=>`The maximum number of retries exceeded. (${s}/${e})`},timeout:()=>r(s)})},waitForApiKey({operation:t,key:e,apiKey:r,maxRetries:a=50,timeout:s=n=>Math.min(n*200,5e3)},o){let n=0,i={aggregator:()=>n+=1,error:{validate:()=>n>=a,message:()=>`The maximum number of retries exceeded. (${n}/${a})`},timeout:()=>s(n)};if(t==="update"){if(!r)throw new Error("`apiKey` is required when waiting for an `update` operation.");return k({...i,func:()=>this.getApiKey({key:e},o),validate:u=>{for(let g of Object.keys(r)){let P=r[g],p=u[g];if(Array.isArray(P)&&Array.isArray(p)){if(P.length!==p.length||P.some((y,f)=>y!==p[f]))return  false}else if(P!==p)return  false}return  true}})}return k({...i,func:()=>this.getApiKey({key:e},o).catch(u=>{if(u.status!==404)throw u}),validate:u=>t==="add"?u!==void 0:u===void 0})},browseObjects({indexName:t,browseParams:e,...r},a){return k({func:s=>this.browse({indexName:t,browseParams:{cursor:s?s.cursor:void 0,hitsPerPage:1e3,...e}},a),validate:s=>s.cursor===void 0,...r})},browseRules({indexName:t,searchRulesParams:e,...r},a){let s={...e,hitsPerPage:e?.hitsPerPage||1e3};return k({func:o=>this.searchRules({indexName:t,searchRulesParams:{...s,page:o?o.page+1:s.page||0}},a),validate:o=>o.hits.length<s.hitsPerPage,...r})},browseSynonyms({indexName:t,searchSynonymsParams:e,...r},a){let s={...e,page:e?.page||0,hitsPerPage:1e3};return k({func:o=>{let n=this.searchSynonyms({indexName:t,searchSynonymsParams:{...s,page:s.page}},a);return s.page+=1,n},validate:o=>o.hits.length<s.hitsPerPage,...r})},async chunkedBatch({indexName:t,objects:e,action:r="addObject",waitForTasks:a,batchSize:s=1e3},o){let n=[],i=[],u=e.entries();for(let[g,P]of u)n.push({action:r,body:P}),(n.length===s||g===e.length-1)&&(i.push(await this.batch({indexName:t,batchWriteParams:{requests:n}},o)),n=[]);if(a)for(let g of i)await this.waitForTask({indexName:t,taskID:g.taskID});return i},async saveObjects({indexName:t,objects:e,waitForTasks:r,batchSize:a},s){return await this.chunkedBatch({indexName:t,objects:e,action:"addObject",waitForTasks:r,batchSize:a},s)},async deleteObjects({indexName:t,objectIDs:e,waitForTasks:r,batchSize:a},s){return await this.chunkedBatch({indexName:t,objects:e.map(o=>({objectID:o})),action:"deleteObject",waitForTasks:r,batchSize:a},s)},async partialUpdateObjects({indexName:t,objects:e,createIfNotExists:r,waitForTasks:a,batchSize:s},o){return await this.chunkedBatch({indexName:t,objects:e,action:r?"partialUpdateObject":"partialUpdateObjectNoCreate",batchSize:s,waitForTasks:a},o)},async replaceAllObjects({indexName:t,objects:e,batchSize:r,scopes:a},s){let o=Math.floor(Math.random()*1e6)+1e5,n=`${t}_tmp_${o}`;a===void 0&&(a=["settings","rules","synonyms"]);try{let i=await this.operationIndex({indexName:t,operationIndexParams:{operation:"copy",destination:n,scope:a}},s),u=await this.chunkedBatch({indexName:n,objects:e,waitForTasks:!0,batchSize:r},s);await this.waitForTask({indexName:n,taskID:i.taskID}),i=await this.operationIndex({indexName:t,operationIndexParams:{operation:"copy",destination:n,scope:a}},s),await this.waitForTask({indexName:n,taskID:i.taskID});let g=await this.operationIndex({indexName:n,operationIndexParams:{operation:"move",destination:t}},s);return await this.waitForTask({indexName:n,taskID:g.taskID}),{copyOperationResponse:i,batchResponses:u,moveOperationResponse:g}}catch(i){throw await this.deleteIndex({indexName:n}),i}},async indexExists({indexName:t}){try{await this.getSettings({indexName:t});}catch(e){if(e instanceof N&&e.status===404)return  false;throw e}return  true},searchForHits(t,e){return this.search(t,e)},searchForFacets(t,e){return this.search(t,e)},addApiKey(t,e){if(!t)throw new Error("Parameter `apiKey` is required when calling `addApiKey`.");if(!t.acl)throw new Error("Parameter `apiKey.acl` is required when calling `addApiKey`.");let o={method:"POST",path:"/1/keys",queryParameters:{},headers:{},data:t};return l.request(o,e)},addOrUpdateObject({indexName:t,objectID:e,body:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `addOrUpdateObject`.");if(!e)throw new Error("Parameter `objectID` is required when calling `addOrUpdateObject`.");if(!r)throw new Error("Parameter `body` is required when calling `addOrUpdateObject`.");let i={method:"PUT",path:"/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return l.request(i,a)},appendSource(t,e){if(!t)throw new Error("Parameter `source` is required when calling `appendSource`.");if(!t.source)throw new Error("Parameter `source.source` is required when calling `appendSource`.");let o={method:"POST",path:"/1/security/sources/append",queryParameters:{},headers:{},data:t};return l.request(o,e)},assignUserId({xAlgoliaUserID:t,assignUserIdParams:e},r){if(!t)throw new Error("Parameter `xAlgoliaUserID` is required when calling `assignUserId`.");if(!e)throw new Error("Parameter `assignUserIdParams` is required when calling `assignUserId`.");if(!e.cluster)throw new Error("Parameter `assignUserIdParams.cluster` is required when calling `assignUserId`.");let a="/1/clusters/mapping",s={},o={};t!==void 0&&(s["X-Algolia-User-ID"]=t.toString());let n={method:"POST",path:a,queryParameters:o,headers:s,data:e};return l.request(n,r)},batch({indexName:t,batchWriteParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `batch`.");if(!e)throw new Error("Parameter `batchWriteParams` is required when calling `batch`.");if(!e.requests)throw new Error("Parameter `batchWriteParams.requests` is required when calling `batch`.");let n={method:"POST",path:"/1/indexes/{indexName}/batch".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)},batchAssignUserIds({xAlgoliaUserID:t,batchAssignUserIdsParams:e},r){if(!t)throw new Error("Parameter `xAlgoliaUserID` is required when calling `batchAssignUserIds`.");if(!e)throw new Error("Parameter `batchAssignUserIdsParams` is required when calling `batchAssignUserIds`.");if(!e.cluster)throw new Error("Parameter `batchAssignUserIdsParams.cluster` is required when calling `batchAssignUserIds`.");if(!e.users)throw new Error("Parameter `batchAssignUserIdsParams.users` is required when calling `batchAssignUserIds`.");let a="/1/clusters/mapping/batch",s={},o={};t!==void 0&&(s["X-Algolia-User-ID"]=t.toString());let n={method:"POST",path:a,queryParameters:o,headers:s,data:e};return l.request(n,r)},batchDictionaryEntries({dictionaryName:t,batchDictionaryEntriesParams:e},r){if(!t)throw new Error("Parameter `dictionaryName` is required when calling `batchDictionaryEntries`.");if(!e)throw new Error("Parameter `batchDictionaryEntriesParams` is required when calling `batchDictionaryEntries`.");if(!e.requests)throw new Error("Parameter `batchDictionaryEntriesParams.requests` is required when calling `batchDictionaryEntries`.");let n={method:"POST",path:"/1/dictionaries/{dictionaryName}/batch".replace("{dictionaryName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)},browse({indexName:t,browseParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `browse`.");let n={method:"POST",path:"/1/indexes/{indexName}/browse".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e||{},useReadTransporter:true};return l.request(n,r)},clearObjects({indexName:t},e){if(!t)throw new Error("Parameter `indexName` is required when calling `clearObjects`.");let o={method:"POST",path:"/1/indexes/{indexName}/clear".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},clearRules({indexName:t,forwardToReplicas:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `clearRules`.");let a="/1/indexes/{indexName}/rules/clear".replace("{indexName}",encodeURIComponent(t)),s={},o={};e!==void 0&&(o.forwardToReplicas=e.toString());let n={method:"POST",path:a,queryParameters:o,headers:s};return l.request(n,r)},clearSynonyms({indexName:t,forwardToReplicas:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `clearSynonyms`.");let a="/1/indexes/{indexName}/synonyms/clear".replace("{indexName}",encodeURIComponent(t)),s={},o={};e!==void 0&&(o.forwardToReplicas=e.toString());let n={method:"POST",path:a,queryParameters:o,headers:s};return l.request(n,r)},customDelete({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customDelete`.");let n={method:"DELETE",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customGet({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customGet`.");let n={method:"GET",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customPost({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPost`.");let i={method:"POST",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},customPut({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPut`.");let i={method:"PUT",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},deleteApiKey({key:t},e){if(!t)throw new Error("Parameter `key` is required when calling `deleteApiKey`.");let o={method:"DELETE",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},deleteBy({indexName:t,deleteByParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteBy`.");if(!e)throw new Error("Parameter `deleteByParams` is required when calling `deleteBy`.");let n={method:"POST",path:"/1/indexes/{indexName}/deleteByQuery".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)},deleteIndex({indexName:t},e){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteIndex`.");let o={method:"DELETE",path:"/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},deleteObject({indexName:t,objectID:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteObject`.");if(!e)throw new Error("Parameter `objectID` is required when calling `deleteObject`.");let n={method:"DELETE",path:"/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return l.request(n,r)},deleteRule({indexName:t,objectID:e,forwardToReplicas:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteRule`.");if(!e)throw new Error("Parameter `objectID` is required when calling `deleteRule`.");let s="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),o={},n={};r!==void 0&&(n.forwardToReplicas=r.toString());let i={method:"DELETE",path:s,queryParameters:n,headers:o};return l.request(i,a)},deleteSource({source:t},e){if(!t)throw new Error("Parameter `source` is required when calling `deleteSource`.");let o={method:"DELETE",path:"/1/security/sources/{source}".replace("{source}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},deleteSynonym({indexName:t,objectID:e,forwardToReplicas:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteSynonym`.");if(!e)throw new Error("Parameter `objectID` is required when calling `deleteSynonym`.");let s="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),o={},n={};r!==void 0&&(n.forwardToReplicas=r.toString());let i={method:"DELETE",path:s,queryParameters:n,headers:o};return l.request(i,a)},getApiKey({key:t},e){if(!t)throw new Error("Parameter `key` is required when calling `getApiKey`.");let o={method:"GET",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getAppTask({taskID:t},e){if(!t)throw new Error("Parameter `taskID` is required when calling `getAppTask`.");let o={method:"GET",path:"/1/task/{taskID}".replace("{taskID}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getDictionaryLanguages(t){let s={method:"GET",path:"/1/dictionaries/*/languages",queryParameters:{},headers:{}};return l.request(s,t)},getDictionarySettings(t){let s={method:"GET",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{}};return l.request(s,t)},getLogs({offset:t,length:e,indexName:r,type:a}={},s=void 0){let o="/1/logs",n={},i={};t!==void 0&&(i.offset=t.toString()),e!==void 0&&(i.length=e.toString()),r!==void 0&&(i.indexName=r.toString()),a!==void 0&&(i.type=a.toString());let u={method:"GET",path:o,queryParameters:i,headers:n};return l.request(u,s)},getObject({indexName:t,objectID:e,attributesToRetrieve:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `getObject`.");if(!e)throw new Error("Parameter `objectID` is required when calling `getObject`.");let s="/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),o={},n={};r!==void 0&&(n.attributesToRetrieve=r.toString());let i={method:"GET",path:s,queryParameters:n,headers:o};return l.request(i,a)},getObjects(t,e){if(!t)throw new Error("Parameter `getObjectsParams` is required when calling `getObjects`.");if(!t.requests)throw new Error("Parameter `getObjectsParams.requests` is required when calling `getObjects`.");let o={method:"POST",path:"/1/indexes/*/objects",queryParameters:{},headers:{},data:t,useReadTransporter:true,cacheable:true};return l.request(o,e)},getRule({indexName:t,objectID:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `getRule`.");if(!e)throw new Error("Parameter `objectID` is required when calling `getRule`.");let n={method:"GET",path:"/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return l.request(n,r)},getSettings({indexName:t},e){if(!t)throw new Error("Parameter `indexName` is required when calling `getSettings`.");let o={method:"GET",path:"/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getSources(t){let s={method:"GET",path:"/1/security/sources",queryParameters:{},headers:{}};return l.request(s,t)},getSynonym({indexName:t,objectID:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `getSynonym`.");if(!e)throw new Error("Parameter `objectID` is required when calling `getSynonym`.");let n={method:"GET",path:"/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return l.request(n,r)},getTask({indexName:t,taskID:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `getTask`.");if(!e)throw new Error("Parameter `taskID` is required when calling `getTask`.");let n={method:"GET",path:"/1/indexes/{indexName}/task/{taskID}".replace("{indexName}",encodeURIComponent(t)).replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return l.request(n,r)},getTopUserIds(t){let s={method:"GET",path:"/1/clusters/mapping/top",queryParameters:{},headers:{}};return l.request(s,t)},getUserId({userID:t},e){if(!t)throw new Error("Parameter `userID` is required when calling `getUserId`.");let o={method:"GET",path:"/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},hasPendingMappings({getClusters:t}={},e=void 0){let r="/1/clusters/mapping/pending",a={},s={};t!==void 0&&(s.getClusters=t.toString());let o={method:"GET",path:r,queryParameters:s,headers:a};return l.request(o,e)},listApiKeys(t){let s={method:"GET",path:"/1/keys",queryParameters:{},headers:{}};return l.request(s,t)},listClusters(t){let s={method:"GET",path:"/1/clusters",queryParameters:{},headers:{}};return l.request(s,t)},listIndices({page:t,hitsPerPage:e}={},r=void 0){let a="/1/indexes",s={},o={};t!==void 0&&(o.page=t.toString()),e!==void 0&&(o.hitsPerPage=e.toString());let n={method:"GET",path:a,queryParameters:o,headers:s};return l.request(n,r)},listUserIds({page:t,hitsPerPage:e}={},r=void 0){let a="/1/clusters/mapping",s={},o={};t!==void 0&&(o.page=t.toString()),e!==void 0&&(o.hitsPerPage=e.toString());let n={method:"GET",path:a,queryParameters:o,headers:s};return l.request(n,r)},multipleBatch(t,e){if(!t)throw new Error("Parameter `batchParams` is required when calling `multipleBatch`.");if(!t.requests)throw new Error("Parameter `batchParams.requests` is required when calling `multipleBatch`.");let o={method:"POST",path:"/1/indexes/*/batch",queryParameters:{},headers:{},data:t};return l.request(o,e)},operationIndex({indexName:t,operationIndexParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `operationIndex`.");if(!e)throw new Error("Parameter `operationIndexParams` is required when calling `operationIndex`.");if(!e.operation)throw new Error("Parameter `operationIndexParams.operation` is required when calling `operationIndex`.");if(!e.destination)throw new Error("Parameter `operationIndexParams.destination` is required when calling `operationIndex`.");let n={method:"POST",path:"/1/indexes/{indexName}/operation".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)},partialUpdateObject({indexName:t,objectID:e,attributesToUpdate:r,createIfNotExists:a},s){if(!t)throw new Error("Parameter `indexName` is required when calling `partialUpdateObject`.");if(!e)throw new Error("Parameter `objectID` is required when calling `partialUpdateObject`.");if(!r)throw new Error("Parameter `attributesToUpdate` is required when calling `partialUpdateObject`.");let o="/1/indexes/{indexName}/{objectID}/partial".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),n={},i={};a!==void 0&&(i.createIfNotExists=a.toString());let u={method:"POST",path:o,queryParameters:i,headers:n,data:r};return l.request(u,s)},removeUserId({userID:t},e){if(!t)throw new Error("Parameter `userID` is required when calling `removeUserId`.");let o={method:"DELETE",path:"/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},replaceSources({source:t},e){if(!t)throw new Error("Parameter `source` is required when calling `replaceSources`.");let o={method:"PUT",path:"/1/security/sources",queryParameters:{},headers:{},data:t};return l.request(o,e)},restoreApiKey({key:t},e){if(!t)throw new Error("Parameter `key` is required when calling `restoreApiKey`.");let o={method:"POST",path:"/1/keys/{key}/restore".replace("{key}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},saveObject({indexName:t,body:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `saveObject`.");if(!e)throw new Error("Parameter `body` is required when calling `saveObject`.");let n={method:"POST",path:"/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)},saveRule({indexName:t,objectID:e,rule:r,forwardToReplicas:a},s){if(!t)throw new Error("Parameter `indexName` is required when calling `saveRule`.");if(!e)throw new Error("Parameter `objectID` is required when calling `saveRule`.");if(!r)throw new Error("Parameter `rule` is required when calling `saveRule`.");if(!r.objectID)throw new Error("Parameter `rule.objectID` is required when calling `saveRule`.");if(!r.consequence)throw new Error("Parameter `rule.consequence` is required when calling `saveRule`.");let o="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),n={},i={};a!==void 0&&(i.forwardToReplicas=a.toString());let u={method:"PUT",path:o,queryParameters:i,headers:n,data:r};return l.request(u,s)},saveRules({indexName:t,rules:e,forwardToReplicas:r,clearExistingRules:a},s){if(!t)throw new Error("Parameter `indexName` is required when calling `saveRules`.");if(!e)throw new Error("Parameter `rules` is required when calling `saveRules`.");let o="/1/indexes/{indexName}/rules/batch".replace("{indexName}",encodeURIComponent(t)),n={},i={};r!==void 0&&(i.forwardToReplicas=r.toString()),a!==void 0&&(i.clearExistingRules=a.toString());let u={method:"POST",path:o,queryParameters:i,headers:n,data:e};return l.request(u,s)},saveSynonym({indexName:t,objectID:e,synonymHit:r,forwardToReplicas:a},s){if(!t)throw new Error("Parameter `indexName` is required when calling `saveSynonym`.");if(!e)throw new Error("Parameter `objectID` is required when calling `saveSynonym`.");if(!r)throw new Error("Parameter `synonymHit` is required when calling `saveSynonym`.");if(!r.objectID)throw new Error("Parameter `synonymHit.objectID` is required when calling `saveSynonym`.");if(!r.type)throw new Error("Parameter `synonymHit.type` is required when calling `saveSynonym`.");let o="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{objectID}",encodeURIComponent(e)),n={},i={};a!==void 0&&(i.forwardToReplicas=a.toString());let u={method:"PUT",path:o,queryParameters:i,headers:n,data:r};return l.request(u,s)},saveSynonyms({indexName:t,synonymHit:e,forwardToReplicas:r,replaceExistingSynonyms:a},s){if(!t)throw new Error("Parameter `indexName` is required when calling `saveSynonyms`.");if(!e)throw new Error("Parameter `synonymHit` is required when calling `saveSynonyms`.");let o="/1/indexes/{indexName}/synonyms/batch".replace("{indexName}",encodeURIComponent(t)),n={},i={};r!==void 0&&(i.forwardToReplicas=r.toString()),a!==void 0&&(i.replaceExistingSynonyms=a.toString());let u={method:"POST",path:o,queryParameters:i,headers:n,data:e};return l.request(u,s)},search(t,e){if(t&&Array.isArray(t)&&(t={requests:t.map(({params:i,...u})=>u.type==="facet"?{...u,...i,type:"facet"}:{...u,...i,facet:void 0,maxFacetHits:void 0,facetQuery:void 0})}),!t)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!t.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");let o={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:t,useReadTransporter:true,cacheable:true};return l.request(o,e)},searchDictionaryEntries({dictionaryName:t,searchDictionaryEntriesParams:e},r){if(!t)throw new Error("Parameter `dictionaryName` is required when calling `searchDictionaryEntries`.");if(!e)throw new Error("Parameter `searchDictionaryEntriesParams` is required when calling `searchDictionaryEntries`.");if(!e.query)throw new Error("Parameter `searchDictionaryEntriesParams.query` is required when calling `searchDictionaryEntries`.");let n={method:"POST",path:"/1/dictionaries/{dictionaryName}/search".replace("{dictionaryName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e,useReadTransporter:true,cacheable:true};return l.request(n,r)},searchForFacetValues({indexName:t,facetName:e,searchForFacetValuesRequest:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `searchForFacetValues`.");if(!e)throw new Error("Parameter `facetName` is required when calling `searchForFacetValues`.");let i={method:"POST",path:"/1/indexes/{indexName}/facets/{facetName}/query".replace("{indexName}",encodeURIComponent(t)).replace("{facetName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:true,cacheable:true};return l.request(i,a)},searchRules({indexName:t,searchRulesParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `searchRules`.");let n={method:"POST",path:"/1/indexes/{indexName}/rules/search".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e||{},useReadTransporter:true,cacheable:true};return l.request(n,r)},searchSingleIndex({indexName:t,searchParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `searchSingleIndex`.");let n={method:"POST",path:"/1/indexes/{indexName}/query".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e||{},useReadTransporter:true,cacheable:true};return l.request(n,r)},searchSynonyms({indexName:t,searchSynonymsParams:e},r){if(!t)throw new Error("Parameter `indexName` is required when calling `searchSynonyms`.");let n={method:"POST",path:"/1/indexes/{indexName}/synonyms/search".replace("{indexName}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e||{},useReadTransporter:true,cacheable:true};return l.request(n,r)},searchUserIds(t,e){if(!t)throw new Error("Parameter `searchUserIdsParams` is required when calling `searchUserIds`.");if(!t.query)throw new Error("Parameter `searchUserIdsParams.query` is required when calling `searchUserIds`.");let o={method:"POST",path:"/1/clusters/mapping/search",queryParameters:{},headers:{},data:t,useReadTransporter:true,cacheable:true};return l.request(o,e)},setDictionarySettings(t,e){if(!t)throw new Error("Parameter `dictionarySettingsParams` is required when calling `setDictionarySettings`.");if(!t.disableStandardEntries)throw new Error("Parameter `dictionarySettingsParams.disableStandardEntries` is required when calling `setDictionarySettings`.");let o={method:"PUT",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{},data:t};return l.request(o,e)},setSettings({indexName:t,indexSettings:e,forwardToReplicas:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `setSettings`.");if(!e)throw new Error("Parameter `indexSettings` is required when calling `setSettings`.");let s="/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(t)),o={},n={};r!==void 0&&(n.forwardToReplicas=r.toString());let i={method:"PUT",path:s,queryParameters:n,headers:o,data:e};return l.request(i,a)},updateApiKey({key:t,apiKey:e},r){if(!t)throw new Error("Parameter `key` is required when calling `updateApiKey`.");if(!e)throw new Error("Parameter `apiKey` is required when calling `updateApiKey`.");if(!e.acl)throw new Error("Parameter `apiKey.acl` is required when calling `updateApiKey`.");let n={method:"PUT",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(t)),queryParameters:{},headers:{},data:e};return l.request(n,r)}}}function me(c,h,d){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");return _e({appId:c,apiKey:h,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${W}-${c}`}),E()]}),...d})}var ge="1.30.0",qe=["eu","us"];function Ve(c){return [{url:"data.{region}.algolia.com".replace("{region}",c),accept:"readWrite",protocol:"https"}]}function Ur(c){return c.type==="onDemand"}function Nr(c){return c.type==="schedule"}function jr(c){return c.type==="subscription"}function Me({appId:c,apiKey:h,authMode:d,algoliaAgents:m,region:w,...q}){let l=R(c,h,d),t=A({hosts:Ve(w),...q,algoliaAgent:C({algoliaAgents:m,client:"Ingestion",version:ge}),baseHeaders:{"content-type":"text/plain",...l.headers(),...q.baseHeaders},baseQueryParameters:{...l.queryParameters(),...q.baseQueryParameters}});return {transporter:t,appId:c,apiKey:h,clearCache(){return Promise.all([t.requestsCache.clear(),t.responsesCache.clear()]).then(()=>{})},get _ua(){return t.algoliaAgent.value},addAlgoliaAgent(e,r){t.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?t.baseHeaders["x-algolia-api-key"]=e:t.baseQueryParameters["x-algolia-api-key"]=e;},async chunkedPush({indexName:e,objects:r,action:a="addObject",waitForTasks:s,batchSize:o=1e3,referenceIndexName:n},i){let u=[],g=[],P=r.entries();for(let[y,f]of P)u.push(f),(u.length===o||y===r.length-1)&&(g.push(await this.push({indexName:e,pushTaskPayload:{action:a,records:u},referenceIndexName:n},i)),u=[]);let p=0;if(s)for(let y of g){if(!y.eventID)throw new Error("received unexpected response from the push endpoint, eventID must not be undefined");await k({func:async()=>{if(y.eventID===void 0||!y.eventID)throw new Error("received unexpected response from the push endpoint, eventID must not be undefined");return this.getEvent({runID:y.runID,eventID:y.eventID}).catch(f=>{if(f.status!==404)throw f})},validate:f=>f!==void 0,aggregator:()=>p+=1,error:{validate:()=>p>=50,message:()=>`The maximum number of retries exceeded. (${p}/50)`},timeout:()=>Math.min(p*500,5e3)});}return g},createAuthentication(e,r){if(!e)throw new Error("Parameter `authenticationCreate` is required when calling `createAuthentication`.");if(!e.type)throw new Error("Parameter `authenticationCreate.type` is required when calling `createAuthentication`.");if(!e.name)throw new Error("Parameter `authenticationCreate.name` is required when calling `createAuthentication`.");if(!e.input)throw new Error("Parameter `authenticationCreate.input` is required when calling `createAuthentication`.");let n={method:"POST",path:"/1/authentications",queryParameters:{},headers:{},data:e};return t.request(n,r)},createDestination(e,r){if(!e)throw new Error("Parameter `destinationCreate` is required when calling `createDestination`.");if(!e.type)throw new Error("Parameter `destinationCreate.type` is required when calling `createDestination`.");if(!e.name)throw new Error("Parameter `destinationCreate.name` is required when calling `createDestination`.");if(!e.input)throw new Error("Parameter `destinationCreate.input` is required when calling `createDestination`.");let n={method:"POST",path:"/1/destinations",queryParameters:{},headers:{},data:e};return t.request(n,r)},createSource(e,r){if(!e)throw new Error("Parameter `sourceCreate` is required when calling `createSource`.");if(!e.type)throw new Error("Parameter `sourceCreate.type` is required when calling `createSource`.");if(!e.name)throw new Error("Parameter `sourceCreate.name` is required when calling `createSource`.");let n={method:"POST",path:"/1/sources",queryParameters:{},headers:{},data:e};return t.request(n,r)},createTask(e,r){if(!e)throw new Error("Parameter `taskCreate` is required when calling `createTask`.");if(!e.sourceID)throw new Error("Parameter `taskCreate.sourceID` is required when calling `createTask`.");if(!e.destinationID)throw new Error("Parameter `taskCreate.destinationID` is required when calling `createTask`.");if(!e.action)throw new Error("Parameter `taskCreate.action` is required when calling `createTask`.");let n={method:"POST",path:"/2/tasks",queryParameters:{},headers:{},data:e};return t.request(n,r)},createTaskV1(e,r){if(!e)throw new Error("Parameter `taskCreate` is required when calling `createTaskV1`.");if(!e.sourceID)throw new Error("Parameter `taskCreate.sourceID` is required when calling `createTaskV1`.");if(!e.destinationID)throw new Error("Parameter `taskCreate.destinationID` is required when calling `createTaskV1`.");if(!e.trigger)throw new Error("Parameter `taskCreate.trigger` is required when calling `createTaskV1`.");if(!e.action)throw new Error("Parameter `taskCreate.action` is required when calling `createTaskV1`.");let n={method:"POST",path:"/1/tasks",queryParameters:{},headers:{},data:e};return t.request(n,r)},createTransformation(e,r){if(!e)throw new Error("Parameter `transformationCreate` is required when calling `createTransformation`.");if(!e.name)throw new Error("Parameter `transformationCreate.name` is required when calling `createTransformation`.");let n={method:"POST",path:"/1/transformations",queryParameters:{},headers:{},data:e};return t.request(n,r)},customDelete({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let i={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customGet({path:e,parameters:r},a){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let i={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return t.request(i,a)},customPost({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let u={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},customPut({path:e,parameters:r,body:a},s){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let u={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:a||{}};return t.request(u,s)},deleteAuthentication({authenticationID:e},r){if(!e)throw new Error("Parameter `authenticationID` is required when calling `deleteAuthentication`.");let n={method:"DELETE",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},deleteDestination({destinationID:e},r){if(!e)throw new Error("Parameter `destinationID` is required when calling `deleteDestination`.");let n={method:"DELETE",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},deleteSource({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `deleteSource`.");let n={method:"DELETE",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},deleteTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `deleteTask`.");let n={method:"DELETE",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},deleteTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `deleteTaskV1`.");let n={method:"DELETE",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},deleteTransformation({transformationID:e},r){if(!e)throw new Error("Parameter `transformationID` is required when calling `deleteTransformation`.");let n={method:"DELETE",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},disableTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `disableTask`.");let n={method:"PUT",path:"/2/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},disableTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `disableTaskV1`.");let n={method:"PUT",path:"/1/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},enableTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `enableTask`.");let n={method:"PUT",path:"/2/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},enableTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `enableTaskV1`.");let n={method:"PUT",path:"/1/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getAuthentication({authenticationID:e},r){if(!e)throw new Error("Parameter `authenticationID` is required when calling `getAuthentication`.");let n={method:"GET",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getDestination({destinationID:e},r){if(!e)throw new Error("Parameter `destinationID` is required when calling `getDestination`.");let n={method:"GET",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getEvent({runID:e,eventID:r},a){if(!e)throw new Error("Parameter `runID` is required when calling `getEvent`.");if(!r)throw new Error("Parameter `eventID` is required when calling `getEvent`.");let i={method:"GET",path:"/1/runs/{runID}/events/{eventID}".replace("{runID}",encodeURIComponent(e)).replace("{eventID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return t.request(i,a)},getRun({runID:e},r){if(!e)throw new Error("Parameter `runID` is required when calling `getRun`.");let n={method:"GET",path:"/1/runs/{runID}".replace("{runID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getSource({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `getSource`.");let n={method:"GET",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `getTask`.");let n={method:"GET",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `getTaskV1`.");let n={method:"GET",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},getTransformation({transformationID:e},r){if(!e)throw new Error("Parameter `transformationID` is required when calling `getTransformation`.");let n={method:"GET",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},listAuthentications({itemsPerPage:e,page:r,type:a,platform:s,sort:o,order:n}={},i=void 0){let u="/1/authentications",g={},P={};e!==void 0&&(P.itemsPerPage=e.toString()),r!==void 0&&(P.page=r.toString()),a!==void 0&&(P.type=a.toString()),s!==void 0&&(P.platform=s.toString()),o!==void 0&&(P.sort=o.toString()),n!==void 0&&(P.order=n.toString());let p={method:"GET",path:u,queryParameters:P,headers:g};return t.request(p,i)},listDestinations({itemsPerPage:e,page:r,type:a,authenticationID:s,transformationID:o,sort:n,order:i}={},u=void 0){let g="/1/destinations",P={},p={};e!==void 0&&(p.itemsPerPage=e.toString()),r!==void 0&&(p.page=r.toString()),a!==void 0&&(p.type=a.toString()),s!==void 0&&(p.authenticationID=s.toString()),o!==void 0&&(p.transformationID=o.toString()),n!==void 0&&(p.sort=n.toString()),i!==void 0&&(p.order=i.toString());let y={method:"GET",path:g,queryParameters:p,headers:P};return t.request(y,u)},listEvents({runID:e,itemsPerPage:r,page:a,status:s,type:o,sort:n,order:i,startDate:u,endDate:g},P){if(!e)throw new Error("Parameter `runID` is required when calling `listEvents`.");let p="/1/runs/{runID}/events".replace("{runID}",encodeURIComponent(e)),y={},f={};r!==void 0&&(f.itemsPerPage=r.toString()),a!==void 0&&(f.page=a.toString()),s!==void 0&&(f.status=s.toString()),o!==void 0&&(f.type=o.toString()),n!==void 0&&(f.sort=n.toString()),i!==void 0&&(f.order=i.toString()),u!==void 0&&(f.startDate=u.toString()),g!==void 0&&(f.endDate=g.toString());let S={method:"GET",path:p,queryParameters:f,headers:y};return t.request(S,P)},listRuns({itemsPerPage:e,page:r,status:a,type:s,taskID:o,sort:n,order:i,startDate:u,endDate:g}={},P=void 0){let p="/1/runs",y={},f={};e!==void 0&&(f.itemsPerPage=e.toString()),r!==void 0&&(f.page=r.toString()),a!==void 0&&(f.status=a.toString()),s!==void 0&&(f.type=s.toString()),o!==void 0&&(f.taskID=o.toString()),n!==void 0&&(f.sort=n.toString()),i!==void 0&&(f.order=i.toString()),u!==void 0&&(f.startDate=u.toString()),g!==void 0&&(f.endDate=g.toString());let S={method:"GET",path:p,queryParameters:f,headers:y};return t.request(S,P)},listSources({itemsPerPage:e,page:r,type:a,authenticationID:s,sort:o,order:n}={},i=void 0){let u="/1/sources",g={},P={};e!==void 0&&(P.itemsPerPage=e.toString()),r!==void 0&&(P.page=r.toString()),a!==void 0&&(P.type=a.toString()),s!==void 0&&(P.authenticationID=s.toString()),o!==void 0&&(P.sort=o.toString()),n!==void 0&&(P.order=n.toString());let p={method:"GET",path:u,queryParameters:P,headers:g};return t.request(p,i)},listTasks({itemsPerPage:e,page:r,action:a,enabled:s,sourceID:o,sourceType:n,destinationID:i,triggerType:u,withEmailNotifications:g,sort:P,order:p}={},y=void 0){let f="/2/tasks",S={},D={};e!==void 0&&(D.itemsPerPage=e.toString()),r!==void 0&&(D.page=r.toString()),a!==void 0&&(D.action=a.toString()),s!==void 0&&(D.enabled=s.toString()),o!==void 0&&(D.sourceID=o.toString()),n!==void 0&&(D.sourceType=n.toString()),i!==void 0&&(D.destinationID=i.toString()),u!==void 0&&(D.triggerType=u.toString()),g!==void 0&&(D.withEmailNotifications=g.toString()),P!==void 0&&(D.sort=P.toString()),p!==void 0&&(D.order=p.toString());let G={method:"GET",path:f,queryParameters:D,headers:S};return t.request(G,y)},listTasksV1({itemsPerPage:e,page:r,action:a,enabled:s,sourceID:o,destinationID:n,triggerType:i,sort:u,order:g}={},P=void 0){let p="/1/tasks",y={},f={};e!==void 0&&(f.itemsPerPage=e.toString()),r!==void 0&&(f.page=r.toString()),a!==void 0&&(f.action=a.toString()),s!==void 0&&(f.enabled=s.toString()),o!==void 0&&(f.sourceID=o.toString()),n!==void 0&&(f.destinationID=n.toString()),i!==void 0&&(f.triggerType=i.toString()),u!==void 0&&(f.sort=u.toString()),g!==void 0&&(f.order=g.toString());let S={method:"GET",path:p,queryParameters:f,headers:y};return t.request(S,P)},listTransformations({itemsPerPage:e,page:r,sort:a,order:s}={},o=void 0){let n="/1/transformations",i={},u={};e!==void 0&&(u.itemsPerPage=e.toString()),r!==void 0&&(u.page=r.toString()),a!==void 0&&(u.sort=a.toString()),s!==void 0&&(u.order=s.toString());let g={method:"GET",path:n,queryParameters:u,headers:i};return t.request(g,o)},push({indexName:e,pushTaskPayload:r,watch:a,referenceIndexName:s},o){if(!e)throw new Error("Parameter `indexName` is required when calling `push`.");if(!r)throw new Error("Parameter `pushTaskPayload` is required when calling `push`.");if(!r.action)throw new Error("Parameter `pushTaskPayload.action` is required when calling `push`.");if(!r.records)throw new Error("Parameter `pushTaskPayload.records` is required when calling `push`.");let n="/1/push/{indexName}".replace("{indexName}",encodeURIComponent(e)),i={},u={};a!==void 0&&(u.watch=a.toString()),s!==void 0&&(u.referenceIndexName=s.toString());let g={method:"POST",path:n,queryParameters:u,headers:i,data:r};return o={timeouts:{connect:18e4,read:18e4,write:18e4,...o?.timeouts}},t.request(g,o)},pushTask({taskID:e,pushTaskPayload:r,watch:a},s){if(!e)throw new Error("Parameter `taskID` is required when calling `pushTask`.");if(!r)throw new Error("Parameter `pushTaskPayload` is required when calling `pushTask`.");if(!r.action)throw new Error("Parameter `pushTaskPayload.action` is required when calling `pushTask`.");if(!r.records)throw new Error("Parameter `pushTaskPayload.records` is required when calling `pushTask`.");let o="/2/tasks/{taskID}/push".replace("{taskID}",encodeURIComponent(e)),n={},i={};a!==void 0&&(i.watch=a.toString());let u={method:"POST",path:o,queryParameters:i,headers:n,data:r};return s={timeouts:{connect:18e4,read:18e4,write:18e4,...s?.timeouts}},t.request(u,s)},runSource({sourceID:e,runSourcePayload:r},a){if(!e)throw new Error("Parameter `sourceID` is required when calling `runSource`.");let i={method:"POST",path:"/1/sources/{sourceID}/run".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{}};return t.request(i,a)},runTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `runTask`.");let n={method:"POST",path:"/2/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},runTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `runTaskV1`.");let n={method:"POST",path:"/1/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return t.request(n,r)},searchAuthentications(e,r){if(!e)throw new Error("Parameter `authenticationSearch` is required when calling `searchAuthentications`.");if(!e.authenticationIDs)throw new Error("Parameter `authenticationSearch.authenticationIDs` is required when calling `searchAuthentications`.");let n={method:"POST",path:"/1/authentications/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},searchDestinations(e,r){if(!e)throw new Error("Parameter `destinationSearch` is required when calling `searchDestinations`.");if(!e.destinationIDs)throw new Error("Parameter `destinationSearch.destinationIDs` is required when calling `searchDestinations`.");let n={method:"POST",path:"/1/destinations/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},searchSources(e,r){if(!e)throw new Error("Parameter `sourceSearch` is required when calling `searchSources`.");if(!e.sourceIDs)throw new Error("Parameter `sourceSearch.sourceIDs` is required when calling `searchSources`.");let n={method:"POST",path:"/1/sources/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},searchTasks(e,r){if(!e)throw new Error("Parameter `taskSearch` is required when calling `searchTasks`.");if(!e.taskIDs)throw new Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasks`.");let n={method:"POST",path:"/2/tasks/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},searchTasksV1(e,r){if(!e)throw new Error("Parameter `taskSearch` is required when calling `searchTasksV1`.");if(!e.taskIDs)throw new Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasksV1`.");let n={method:"POST",path:"/1/tasks/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},searchTransformations(e,r){if(!e)throw new Error("Parameter `transformationSearch` is required when calling `searchTransformations`.");if(!e.transformationIDs)throw new Error("Parameter `transformationSearch.transformationIDs` is required when calling `searchTransformations`.");let n={method:"POST",path:"/1/transformations/search",queryParameters:{},headers:{},data:e};return t.request(n,r)},triggerDockerSourceDiscover({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.");let n={method:"POST",path:"/1/sources/{sourceID}/discover".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return r={timeouts:{connect:18e4,read:18e4,write:18e4,...r?.timeouts}},t.request(n,r)},tryTransformation(e,r){if(!e)throw new Error("Parameter `transformationTry` is required when calling `tryTransformation`.");if(!e.sampleRecord)throw new Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformation`.");let n={method:"POST",path:"/1/transformations/try",queryParameters:{},headers:{},data:e};return t.request(n,r)},tryTransformationBeforeUpdate({transformationID:e,transformationTry:r},a){if(!e)throw new Error("Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.");if(!r)throw new Error("Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.");if(!r.sampleRecord)throw new Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformationBeforeUpdate`.");let i={method:"POST",path:"/1/transformations/{transformationID}/try".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateAuthentication({authenticationID:e,authenticationUpdate:r},a){if(!e)throw new Error("Parameter `authenticationID` is required when calling `updateAuthentication`.");if(!r)throw new Error("Parameter `authenticationUpdate` is required when calling `updateAuthentication`.");let i={method:"PATCH",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateDestination({destinationID:e,destinationUpdate:r},a){if(!e)throw new Error("Parameter `destinationID` is required when calling `updateDestination`.");if(!r)throw new Error("Parameter `destinationUpdate` is required when calling `updateDestination`.");let i={method:"PATCH",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateSource({sourceID:e,sourceUpdate:r},a){if(!e)throw new Error("Parameter `sourceID` is required when calling `updateSource`.");if(!r)throw new Error("Parameter `sourceUpdate` is required when calling `updateSource`.");let i={method:"PATCH",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateTask({taskID:e,taskUpdate:r},a){if(!e)throw new Error("Parameter `taskID` is required when calling `updateTask`.");if(!r)throw new Error("Parameter `taskUpdate` is required when calling `updateTask`.");let i={method:"PATCH",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateTaskV1({taskID:e,taskUpdate:r},a){if(!e)throw new Error("Parameter `taskID` is required when calling `updateTaskV1`.");if(!r)throw new Error("Parameter `taskUpdate` is required when calling `updateTaskV1`.");let i={method:"PATCH",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},updateTransformation({transformationID:e,transformationCreate:r},a){if(!e)throw new Error("Parameter `transformationID` is required when calling `updateTransformation`.");if(!r)throw new Error("Parameter `transformationCreate` is required when calling `updateTransformation`.");if(!r.name)throw new Error("Parameter `transformationCreate.name` is required when calling `updateTransformation`.");let i={method:"PUT",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return t.request(i,a)},validateSource(e,r=void 0){let n={method:"POST",path:"/1/sources/validate",queryParameters:{},headers:{},data:e||{}};return r={timeouts:{connect:18e4,read:18e4,write:18e4,...r?.timeouts}},t.request(n,r)},validateSourceBeforeUpdate({sourceID:e,sourceUpdate:r},a){if(!e)throw new Error("Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.");if(!r)throw new Error("Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.");let i={method:"POST",path:"/1/sources/{sourceID}/validate".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return a={timeouts:{connect:18e4,read:18e4,write:18e4,...a?.timeouts}},t.request(i,a)}}}function H(c,h,d,m){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");if(!d||d&&(typeof d!="string"||!qe.includes(d)))throw new Error(`\`region\` is required and must be one of the following: ${qe.join(", ")}`);return Me({appId:c,apiKey:h,region:d,timeouts:{connect:25e3,read:25e3,write:25e3},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${ge}-${c}`}),E()]}),...m})}var Pe="1.30.0";function Je(){return [{url:"status.algolia.com",accept:"readWrite",protocol:"https"}]}function Ke({appId:c,apiKey:h,authMode:d,algoliaAgents:m,...w}){let q=R(c,h,d),l=A({hosts:Je(),...w,algoliaAgent:C({algoliaAgents:m,client:"Monitoring",version:Pe}),baseHeaders:{"content-type":"text/plain",...q.headers(),...w.baseHeaders},baseQueryParameters:{...q.queryParameters(),...w.baseQueryParameters}});return {transporter:l,appId:c,apiKey:h,clearCache(){return Promise.all([l.requestsCache.clear(),l.responsesCache.clear()]).then(()=>{})},get _ua(){return l.algoliaAgent.value},addAlgoliaAgent(t,e){l.algoliaAgent.add({segment:t,version:e});},setClientApiKey({apiKey:t}){!d||d==="WithinHeaders"?l.baseHeaders["x-algolia-api-key"]=t:l.baseQueryParameters["x-algolia-api-key"]=t;},customDelete({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customDelete`.");let n={method:"DELETE",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customGet({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customGet`.");let n={method:"GET",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customPost({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPost`.");let i={method:"POST",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},customPut({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPut`.");let i={method:"PUT",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},getClusterIncidents({clusters:t},e){if(!t)throw new Error("Parameter `clusters` is required when calling `getClusterIncidents`.");let o={method:"GET",path:"/1/incidents/{clusters}".replace("{clusters}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getClusterStatus({clusters:t},e){if(!t)throw new Error("Parameter `clusters` is required when calling `getClusterStatus`.");let o={method:"GET",path:"/1/status/{clusters}".replace("{clusters}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getIncidents(t){let s={method:"GET",path:"/1/incidents",queryParameters:{},headers:{}};return l.request(s,t)},getIndexingTime({clusters:t},e){if(!t)throw new Error("Parameter `clusters` is required when calling `getIndexingTime`.");let o={method:"GET",path:"/1/indexing/{clusters}".replace("{clusters}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getLatency({clusters:t},e){if(!t)throw new Error("Parameter `clusters` is required when calling `getLatency`.");let o={method:"GET",path:"/1/latency/{clusters}".replace("{clusters}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getMetrics({metric:t,period:e},r){if(!t)throw new Error("Parameter `metric` is required when calling `getMetrics`.");if(!e)throw new Error("Parameter `period` is required when calling `getMetrics`.");let n={method:"GET",path:"/1/infrastructure/{metric}/period/{period}".replace("{metric}",encodeURIComponent(t)).replace("{period}",encodeURIComponent(e)),queryParameters:{},headers:{}};return l.request(n,r)},getReachability({clusters:t},e){if(!t)throw new Error("Parameter `clusters` is required when calling `getReachability`.");let o={method:"GET",path:"/1/reachability/{clusters}/probes".replace("{clusters}",encodeURIComponent(t)),queryParameters:{},headers:{}};return l.request(o,e)},getServers(t){let s={method:"GET",path:"/1/inventory/servers",queryParameters:{},headers:{}};return l.request(s,t)},getStatus(t){let s={method:"GET",path:"/1/status",queryParameters:{},headers:{}};return l.request(s,t)}}}function pe(c,h,d){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");return Ke({appId:c,apiKey:h,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${Pe}-${c}`}),E()]}),...d})}var we="5.30.0";function Xe(c){return [{url:`${c}-dsn.algolia.net`,accept:"read",protocol:"https"},{url:`${c}.algolia.net`,accept:"write",protocol:"https"}].concat(j([{url:`${c}-1.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${c}-2.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${c}-3.algolianet.com`,accept:"readWrite",protocol:"https"}]))}function Ye({appId:c,apiKey:h,authMode:d,algoliaAgents:m,...w}){let q=R(c,h,d),l=A({hosts:Xe(c),...w,algoliaAgent:C({algoliaAgents:m,client:"Recommend",version:we}),baseHeaders:{"content-type":"text/plain",...q.headers(),...w.baseHeaders},baseQueryParameters:{...q.queryParameters(),...w.baseQueryParameters}});return {transporter:l,appId:c,apiKey:h,clearCache(){return Promise.all([l.requestsCache.clear(),l.responsesCache.clear()]).then(()=>{})},get _ua(){return l.algoliaAgent.value},addAlgoliaAgent(t,e){l.algoliaAgent.add({segment:t,version:e});},setClientApiKey({apiKey:t}){!d||d==="WithinHeaders"?l.baseHeaders["x-algolia-api-key"]=t:l.baseQueryParameters["x-algolia-api-key"]=t;},batchRecommendRules({indexName:t,model:e,recommendRule:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `batchRecommendRules`.");if(!e)throw new Error("Parameter `model` is required when calling `batchRecommendRules`.");let i={method:"POST",path:"/1/indexes/{indexName}/{model}/recommend/rules/batch".replace("{indexName}",encodeURIComponent(t)).replace("{model}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{}};return l.request(i,a)},customDelete({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customDelete`.");let n={method:"DELETE",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customGet({path:t,parameters:e},r){if(!t)throw new Error("Parameter `path` is required when calling `customGet`.");let n={method:"GET",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{}};return l.request(n,r)},customPost({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPost`.");let i={method:"POST",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},customPut({path:t,parameters:e,body:r},a){if(!t)throw new Error("Parameter `path` is required when calling `customPut`.");let i={method:"PUT",path:"/{path}".replace("{path}",t),queryParameters:e||{},headers:{},data:r||{}};return l.request(i,a)},deleteRecommendRule({indexName:t,model:e,objectID:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `deleteRecommendRule`.");if(!e)throw new Error("Parameter `model` is required when calling `deleteRecommendRule`.");if(!r)throw new Error("Parameter `objectID` is required when calling `deleteRecommendRule`.");let i={method:"DELETE",path:"/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{model}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return l.request(i,a)},getRecommendRule({indexName:t,model:e,objectID:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `getRecommendRule`.");if(!e)throw new Error("Parameter `model` is required when calling `getRecommendRule`.");if(!r)throw new Error("Parameter `objectID` is required when calling `getRecommendRule`.");let i={method:"GET",path:"/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(t)).replace("{model}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return l.request(i,a)},getRecommendStatus({indexName:t,model:e,taskID:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `getRecommendStatus`.");if(!e)throw new Error("Parameter `model` is required when calling `getRecommendStatus`.");if(!r)throw new Error("Parameter `taskID` is required when calling `getRecommendStatus`.");let i={method:"GET",path:"/1/indexes/{indexName}/{model}/task/{taskID}".replace("{indexName}",encodeURIComponent(t)).replace("{model}",encodeURIComponent(e)).replace("{taskID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return l.request(i,a)},getRecommendations(t,e){if(t&&Array.isArray(t)&&(t={requests:t}),!t)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!t.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");let o={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:t,useReadTransporter:true,cacheable:true};return l.request(o,e)},searchRecommendRules({indexName:t,model:e,searchRecommendRulesParams:r},a){if(!t)throw new Error("Parameter `indexName` is required when calling `searchRecommendRules`.");if(!e)throw new Error("Parameter `model` is required when calling `searchRecommendRules`.");let i={method:"POST",path:"/1/indexes/{indexName}/{model}/recommend/rules/search".replace("{indexName}",encodeURIComponent(t)).replace("{model}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:true,cacheable:true};return l.request(i,a)}}}function fe(c,h,d){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");return Ye({appId:c,apiKey:h,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:v(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:E(),requestsCache:E({serializable:false}),hostsCache:T({caches:[I({key:`${we}-${c}`}),E()]}),...d})}function lt(c,h,d){if(!c||typeof c!="string")throw new Error("`appId` is missing.");if(!h||typeof h!="string")throw new Error("`apiKey` is missing.");let m=me(c,h,d),w;if(d?.transformation){if(!d.transformation.region)throw new Error("`region` must be provided when leveraging the transformation pipeline");w=H(c,h,d.transformation.region,d);}return {...m,async saveObjectsWithTransformation({indexName:q,objects:l,waitForTasks:t},e){if(!w)throw new Error("`transformation.region` must be provided at client instantiation before calling this method.");if(!d?.transformation?.region)throw new Error("`region` must be provided when leveraging the transformation pipeline");return w.chunkedPush({indexName:q,objects:l,action:"addObject",waitForTasks:t},e)},async partialUpdateObjectsWithTransformation({indexName:q,objects:l,createIfNotExists:t,waitForTasks:e},r){if(!w)throw new Error("`transformation.region` must be provided at client instantiation before calling this method.");if(!d?.transformation?.region)throw new Error("`region` must be provided when leveraging the transformation pipeline");return w.chunkedPush({indexName:q,objects:l,action:t?"partialUpdateObject":"partialUpdateObjectNoCreate",waitForTasks:e},r)},async replaceAllObjectsWithTransformation({indexName:q,objects:l,batchSize:t,scopes:e},r){if(!w)throw new Error("`transformation.region` must be provided at client instantiation before calling this method.");if(!d?.transformation?.region)throw new Error("`region` must be provided when leveraging the transformation pipeline");let a=Math.floor(Math.random()*1e6)+1e5,s=`${q}_tmp_${a}`;e===void 0&&(e=["settings","rules","synonyms"]);try{let o=await this.operationIndex({indexName:q,operationIndexParams:{operation:"copy",destination:s,scope:e}},r),n=await w.chunkedPush({indexName:s,objects:l,waitForTasks:!0,batchSize:t,referenceIndexName:q},r);await this.waitForTask({indexName:s,taskID:o.taskID}),o=await this.operationIndex({indexName:q,operationIndexParams:{operation:"copy",destination:s,scope:e}},r),await this.waitForTask({indexName:s,taskID:o.taskID});let i=await this.operationIndex({indexName:s,operationIndexParams:{operation:"move",destination:q}},r);return await this.waitForTask({indexName:s,taskID:i.taskID}),{copyOperationResponse:o,watchResponses:n,moveOperationResponse:i}}catch(o){throw await this.deleteIndex({indexName:s}),o}},get _ua(){return m.transporter.algoliaAgent.value},initAbtesting:q=>ee(q.appId||c,q.apiKey||h,q.region,q.options),initAnalytics:q=>ae(q.appId||c,q.apiKey||h,q.region,q.options),initIngestion:q=>H(q.appId||c,q.apiKey||h,q.region,q.options),initInsights:q=>oe(q.appId||c,q.apiKey||h,q.region,q.options),initMonitoring:(q={})=>pe(q.appId||c,q.apiKey||h,q.options),initPersonalization:q=>ue(q.appId||c,q.apiKey||h,q.region,q.options),initQuerySuggestions:q=>le(q.appId||c,q.apiKey||h,q.region,q.options),initRecommend:(q={})=>fe(q.appId||c,q.apiKey||h,q.options)}}

	exports.abtestingClient = ee;
	exports.algoliasearch = lt;
	exports.analyticsClient = ae;
	exports.apiClientVersion = W;
	exports.ingestionClient = H;
	exports.insightsClient = oe;
	exports.isOnDemandTrigger = Ur;
	exports.isScheduleTrigger = Nr;
	exports.isSubscriptionTrigger = jr;
	exports.monitoringClient = pe;
	exports.personalizationClient = ue;
	exports.querySuggestionsClient = le;
	exports.recommendClient = fe;
	exports.searchClient = me;

}));
