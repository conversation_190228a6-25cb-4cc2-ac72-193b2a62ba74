{"version": 3, "sources": ["../../builds/browser.ts", "../../src/recommendClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport { createXhrRequester } from '@algolia/requester-browser-xhr';\n\nimport {\n  createBrowserLocalStorageCache,\n  createFallbackableCache,\n  createMemoryCache,\n  createNullLogger,\n} from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { apiClientVersion, createRecommendClient } from '../src/recommendClient';\n\nexport { apiClientVersion } from '../src/recommendClient';\n\nexport * from '../model';\n\nexport function recommendClient(appId: string, apiKey: string, options?: ClientOptions | undefined): RecommendClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  return createRecommendClient({\n    appId,\n    apiKey,\n    timeouts: {\n      connect: 1000,\n      read: 2000,\n      write: 30000,\n    },\n    logger: createNullLogger(),\n    requester: createXhrRequester(),\n    algoliaAgents: [{ segment: 'Browser' }],\n    authMode: 'WithinQueryParameters',\n    responsesCache: createMemoryCache(),\n    requestsCache: createMemoryCache({ serializable: false }),\n    hostsCache: createFallbackableCache({\n      caches: [createBrowserLocalStorageCache({ key: `${apiClientVersion}-${appId}` }), createMemoryCache()],\n    }),\n    ...options,\n  });\n}\n\nexport type RecommendClient = ReturnType<typeof createRecommendClient>;\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent, shuffle } from '@algolia/client-common';\n\nimport type { DeletedAtResponse } from '../model/deletedAtResponse';\n\nimport type { GetRecommendTaskResponse } from '../model/getRecommendTaskResponse';\nimport type { GetRecommendationsParams } from '../model/getRecommendationsParams';\nimport type { GetRecommendationsResponse } from '../model/getRecommendationsResponse';\n\nimport type { RecommendRule } from '../model/recommendRule';\nimport type { RecommendUpdatedAtResponse } from '../model/recommendUpdatedAtResponse';\n\nimport type { SearchRecommendRulesResponse } from '../model/searchRecommendRulesResponse';\n\nimport type {\n  BatchRecommendRulesProps,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteRecommendRuleProps,\n  GetRecommendRuleProps,\n  GetRecommendStatusProps,\n  LegacyGetRecommendationsParams,\n  SearchRecommendRulesProps,\n} from '../model/clientMethodProps';\n\nexport const apiClientVersion = '5.30.0';\n\nfunction getDefaultHosts(appId: string): Host[] {\n  return (\n    [\n      {\n        url: `${appId}-dsn.algolia.net`,\n        accept: 'read',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}.algolia.net`,\n        accept: 'write',\n        protocol: 'https',\n      },\n    ] as Host[]\n  ).concat(\n    shuffle([\n      {\n        url: `${appId}-1.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-2.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-3.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n    ]),\n  );\n}\n\nexport function createRecommendClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  ...options\n}: CreateClientOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(appIdOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Recommend',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Create or update a batch of Recommend Rules  Each Recommend Rule is created or updated, depending on whether a Recommend Rule with the same `objectID` already exists. You may also specify `true` for `clearExistingRules`, in which case the batch will atomically replace all the existing Recommend Rules.  Recommend Rules are similar to Search Rules, except that the conditions and consequences apply to a [source item](/doc/guides/algolia-recommend/overview/#recommend-models) instead of a query. The main differences are the following: - Conditions `pattern` and `anchoring` are unavailable. - Condition `filters` triggers if the source item matches the specified filters. - Condition `filters` accepts numeric filters. - Consequence `params` only covers filtering parameters. - Consequence `automaticFacetFilters` doesn\\'t require a facet value placeholder (it tries to match the data source item\\'s attributes instead).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param batchRecommendRules - The batchRecommendRules object.\n     * @param batchRecommendRules.indexName - Name of the index on which to perform the operation.\n     * @param batchRecommendRules.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).\n     * @param batchRecommendRules.recommendRule - The recommendRule object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batchRecommendRules(\n      { indexName, model, recommendRule }: BatchRecommendRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<RecommendUpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `batchRecommendRules`.');\n      }\n\n      if (!model) {\n        throw new Error('Parameter `model` is required when calling `batchRecommendRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{model}/recommend/rules/batch'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{model}', encodeURIComponent(model));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: recommendRule ? recommendRule : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a Recommend rule from a recommendation scenario.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteRecommendRule - The deleteRecommendRule object.\n     * @param deleteRecommendRule.indexName - Name of the index on which to perform the operation.\n     * @param deleteRecommendRule.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).\n     * @param deleteRecommendRule.objectID - Unique record identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteRecommendRule(\n      { indexName, model, objectID }: DeleteRecommendRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteRecommendRule`.');\n      }\n\n      if (!model) {\n        throw new Error('Parameter `model` is required when calling `deleteRecommendRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteRecommendRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{model}/recommend/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{model}', encodeURIComponent(model))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a Recommend rule that you previously created in the Algolia dashboard.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getRecommendRule - The getRecommendRule object.\n     * @param getRecommendRule.indexName - Name of the index on which to perform the operation.\n     * @param getRecommendRule.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).\n     * @param getRecommendRule.objectID - Unique record identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRecommendRule(\n      { indexName, model, objectID }: GetRecommendRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<RecommendRule> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getRecommendRule`.');\n      }\n\n      if (!model) {\n        throw new Error('Parameter `model` is required when calling `getRecommendRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getRecommendRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{model}/recommend/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{model}', encodeURIComponent(model))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Checks the status of a given task.  Deleting a Recommend rule is asynchronous. When you delete a rule, a task is created on a queue and completed depending on the load on the server. The API response includes a task ID that you can use to check the status.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param getRecommendStatus - The getRecommendStatus object.\n     * @param getRecommendStatus.indexName - Name of the index on which to perform the operation.\n     * @param getRecommendStatus.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).\n     * @param getRecommendStatus.taskID - Unique task identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRecommendStatus(\n      { indexName, model, taskID }: GetRecommendStatusProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetRecommendTaskResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getRecommendStatus`.');\n      }\n\n      if (!model) {\n        throw new Error('Parameter `model` is required when calling `getRecommendStatus`.');\n      }\n\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getRecommendStatus`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{model}/task/{taskID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{model}', encodeURIComponent(model))\n        .replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves recommendations from selected AI models.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getRecommendationsParams - The getRecommendationsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRecommendations(\n      getRecommendationsParams: GetRecommendationsParams | LegacyGetRecommendationsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<GetRecommendationsResponse> {\n      if (getRecommendationsParams && Array.isArray(getRecommendationsParams)) {\n        const newSignatureRequest: GetRecommendationsParams = {\n          requests: getRecommendationsParams,\n        };\n\n        getRecommendationsParams = newSignatureRequest;\n      }\n\n      if (!getRecommendationsParams) {\n        throw new Error('Parameter `getRecommendationsParams` is required when calling `getRecommendations`.');\n      }\n\n      if (!getRecommendationsParams.requests) {\n        throw new Error('Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.');\n      }\n\n      const requestPath = '/1/indexes/*/recommendations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: getRecommendationsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for Recommend rules.  Use an empty query to list all rules for this recommendation scenario.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchRecommendRules - The searchRecommendRules object.\n     * @param searchRecommendRules.indexName - Name of the index on which to perform the operation.\n     * @param searchRecommendRules.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).\n     * @param searchRecommendRules.searchRecommendRulesParams - The searchRecommendRulesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchRecommendRules(\n      { indexName, model, searchRecommendRulesParams }: SearchRecommendRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchRecommendRulesResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchRecommendRules`.');\n      }\n\n      if (!model) {\n        throw new Error('Parameter `model` is required when calling `searchRecommendRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{model}/recommend/rules/search'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{model}', encodeURIComponent(model));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchRecommendRulesParams ? searchRecommendRulesParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";AAEA,SAAS,0BAA0B;AAEnC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACCP,SAAS,YAAY,mBAAmB,iBAAiB,eAAe;AA0BjE,IAAM,mBAAmB;AAEhC,SAAS,gBAAgB,OAAuB;AAC9C,SACE;AAAA,IACE;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF,EACA;AAAA,IACA,QAAQ;AAAA,MACN;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,sBAAsB;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAwB;AACtB,QAAM,OAAO,WAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,cAAc,kBAAkB;AAAA,IACpC,OAAO,gBAAgB,WAAW;AAAA,IAClC,GAAG;AAAA,IACH,cAAc,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,oBACE,EAAE,WAAW,OAAO,cAAc,GAClC,gBACqC;AACrC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,uDACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,WAAW,mBAAmB,KAAK,CAAC;AAC/C,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,gBAAgB,gBAAgB,CAAC;AAAA,MACzC;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,oBACE,EAAE,WAAW,OAAO,SAAS,GAC7B,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc,4DACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,WAAW,mBAAmB,KAAK,CAAC,EAC5C,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,iBACE,EAAE,WAAW,OAAO,SAAS,GAC7B,gBACwB;AACxB,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,4DACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,WAAW,mBAAmB,KAAK,CAAC,EAC5C,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,mBACE,EAAE,WAAW,OAAO,OAAO,GAC3B,gBACmC;AACnC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACpF;AAEA,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,+CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,WAAW,mBAAmB,KAAK,CAAC,EAC5C,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACjD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,mBACE,0BACA,gBACqC;AACrC,UAAI,4BAA4B,MAAM,QAAQ,wBAAwB,GAAG;AACvE,cAAM,sBAAgD;AAAA,UACpD,UAAU;AAAA,QACZ;AAEA,mCAA2B;AAAA,MAC7B;AAEA,UAAI,CAAC,0BAA0B;AAC7B,cAAM,IAAI,MAAM,qFAAqF;AAAA,MACvG;AAEA,UAAI,CAAC,yBAAyB,UAAU;AACtC,cAAM,IAAI,MAAM,8FAA8F;AAAA,MAChH;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,qBACE,EAAE,WAAW,OAAO,2BAA2B,GAC/C,gBACuC;AACvC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,YAAM,cAAc,wDACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,WAAW,mBAAmB,KAAK,CAAC;AAC/C,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,6BAA6B,6BAA6B,CAAC;AAAA,QACjE,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;AD5fO,SAAS,gBAAgB,OAAe,QAAgB,SAAsD;AACnH,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,SAAO,sBAAsB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ,iBAAiB;AAAA,IACzB,WAAW,mBAAmB;AAAA,IAC9B,eAAe,CAAC,EAAE,SAAS,UAAU,CAAC;AAAA,IACtC,UAAU;AAAA,IACV,gBAAgB,kBAAkB;AAAA,IAClC,eAAe,kBAAkB,EAAE,cAAc,MAAM,CAAC;AAAA,IACxD,YAAY,wBAAwB;AAAA,MAClC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,GAAG,gBAAgB,IAAI,KAAK,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,IACvG,CAAC;AAAA,IACD,GAAG;AAAA,EACL,CAAC;AACH;", "names": []}