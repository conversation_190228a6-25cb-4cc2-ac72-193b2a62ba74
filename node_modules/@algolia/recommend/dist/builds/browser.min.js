function H(){function r(e){return new Promise(o=>{let t=new XMLHttpRequest;t.open(e.method,e.url,!0),Object.keys(e.headers).forEach(s=>t.setRequestHeader(s,e.headers[s]));let a=(s,n)=>setTimeout(()=>{t.abort(),o({status:0,content:n,isTimedOut:!0})},s),d=a(e.connectTimeout,"Connection timeout"),c;t.onreadystatechange=()=>{t.readyState>t.OPENED&&c===void 0&&(clearTimeout(d),c=a(e.responseTimeout,"Socket timeout"))},t.onerror=()=>{t.status===0&&(clearTimeout(d),clearTimeout(c),o({content:t.responseText||"Network request failed",status:t.status,isTimedOut:!1}))},t.onload=()=>{clearTimeout(d),clearTimeout(c),o({content:t.responseText,status:t.status,isTimedOut:!1})},t.send(e.data)})}return{send:r}}function L(r){let e,o=`algolia-client-js-${r.key}`;function t(){return e===void 0&&(e=r.localStorage||window.localStorage),e}function a(){return JSON.parse(t().getItem(o)||"{}")}function d(s){t().setItem(o,JSON.stringify(s))}function c(){let s=r.timeToLive?r.timeToLive*1e3:null,n=a(),i=Object.fromEntries(Object.entries(n).filter(([,p])=>p.timestamp!==void 0));if(d(i),!s)return;let l=Object.fromEntries(Object.entries(i).filter(([,p])=>{let h=new Date().getTime();return!(p.timestamp+s<h)}));d(l)}return{get(s,n,i={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(c(),a()[JSON.stringify(s)])).then(l=>Promise.all([l?l.value:n(),l!==void 0])).then(([l,p])=>Promise.all([l,p||i.miss(l)])).then(([l])=>l)},set(s,n){return Promise.resolve().then(()=>{let i=a();return i[JSON.stringify(s)]={timestamp:new Date().getTime(),value:n},t().setItem(o,JSON.stringify(i)),n})},delete(s){return Promise.resolve().then(()=>{let n=a();delete n[JSON.stringify(s)],t().setItem(o,JSON.stringify(n))})},clear(){return Promise.resolve().then(()=>{t().removeItem(o)})}}}function V(){return{get(r,e,o={miss:()=>Promise.resolve()}){return e().then(a=>Promise.all([a,o.miss(a)])).then(([a])=>a)},set(r,e){return Promise.resolve(e)},delete(r){return Promise.resolve()},clear(){return Promise.resolve()}}}function q(r){let e=[...r.caches],o=e.shift();return o===void 0?V():{get(t,a,d={miss:()=>Promise.resolve()}){return o.get(t,a,d).catch(()=>q({caches:e}).get(t,a,d))},set(t,a){return o.set(t,a).catch(()=>q({caches:e}).set(t,a))},delete(t){return o.delete(t).catch(()=>q({caches:e}).delete(t))},clear(){return o.clear().catch(()=>q({caches:e}).clear())}}}function A(r={serializable:!0}){let e={};return{get(o,t,a={miss:()=>Promise.resolve()}){let d=JSON.stringify(o);if(d in e)return Promise.resolve(r.serializable?JSON.parse(e[d]):e[d]);let c=t();return c.then(s=>a.miss(s)).then(()=>c)},set(o,t){return e[JSON.stringify(o)]=r.serializable?JSON.stringify(t):t,Promise.resolve(t)},delete(o){return delete e[JSON.stringify(o)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function Y(r){let e={value:`Algolia for JavaScript (${r})`,add(o){let t=`; ${o.segment}${o.version!==void 0?` (${o.version})`:""}`;return e.value.indexOf(t)===-1&&(e.value=`${e.value}${t}`),e}};return e}function j(r,e,o="WithinHeaders"){let t={"x-algolia-api-key":e,"x-algolia-application-id":r};return{headers(){return o==="WithinHeaders"?t:{}},queryParameters(){return o==="WithinQueryParameters"?t:{}}}}function W({algoliaAgents:r,client:e,version:o}){let t=Y(o).add({segment:e,version:o});return r.forEach(a=>t.add(a)),t}function J(){return{debug(r,e){return Promise.resolve()},info(r,e){return Promise.resolve()},error(r,e){return Promise.resolve()}}}var $=2*60*1e3;function G(r,e="up"){let o=Date.now();function t(){return e==="up"||Date.now()-o>$}function a(){return e==="timed out"&&Date.now()-o<=$}return{...r,status:e,lastUpdate:o,isUp:t,isTimedOut:a}}var Q=class extends Error{name="AlgoliaError";constructor(r,e){super(r),e&&(this.name=e)}};var z=class extends Q{stackTrace;constructor(r,e,o){super(r,o),this.stackTrace=e}},Z=class extends z{constructor(r){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",r,"RetryError")}},I=class extends z{status;constructor(r,e,o,t="ApiError"){super(r,o,t),this.status=e}},ee=class extends Q{response;constructor(r,e){super(r,"DeserializationError"),this.response=e}},re=class extends I{error;constructor(r,e,o,t){super(r,e,t,"DetailedApiError"),this.error=o}};function M(r){let e=r;for(let o=r.length-1;o>0;o--){let t=Math.floor(Math.random()*(o+1)),a=r[o];e[o]=r[t],e[t]=a}return e}function te(r,e,o){let t=oe(o),a=`${r.protocol}://${r.url}${r.port?`:${r.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return t.length&&(a+=`?${t}`),a}function oe(r){return Object.keys(r).filter(e=>r[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(r[e])==="[object Array]"?r[e].join(","):r[e]).replace(/\+/g,"%20")}`).join("&")}function se(r,e){if(r.method==="GET"||r.data===void 0&&e.data===void 0)return;let o=Array.isArray(r.data)?r.data:{...r.data,...e.data};return JSON.stringify(o)}function ne(r,e,o){let t={Accept:"application/json",...r,...e,...o},a={};return Object.keys(t).forEach(d=>{let c=t[d];a[d.toLowerCase()]=c}),a}function ae(r){try{return JSON.parse(r.content)}catch(e){throw new ee(e.message,r)}}function ie({content:r,status:e},o){try{let t=JSON.parse(r);return"error"in t?new re(t.message,e,t.error,o):new I(t.message,e,o)}catch{}return new I(r,e,o)}function me({isTimedOut:r,status:e}){return!r&&~~e===0}function ce({isTimedOut:r,status:e}){return r||me({isTimedOut:r,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function ue({status:r}){return~~(r/100)===2}function le(r){return r.map(e=>F(e))}function F(r){let e=r.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...r,request:{...r.request,headers:{...r.request.headers,...e}}}}function B({hosts:r,hostsCache:e,baseHeaders:o,logger:t,baseQueryParameters:a,algoliaAgent:d,timeouts:c,requester:s,requestsCache:n,responsesCache:i}){async function l(u){let m=await Promise.all(u.map(f=>e.get(f,()=>Promise.resolve(G(f))))),w=m.filter(f=>f.isUp()),R=m.filter(f=>f.isTimedOut()),E=[...w,...R];return{hosts:E.length>0?E:u,getTimeout(f,g){return(R.length===0&&f===0?1:R.length+3+f)*g}}}async function p(u,m,w=!0){let R=[],E=se(u,m),x=ne(o,u.headers,m.headers),f=u.method==="GET"?{...u.data,...m.data}:{},g={...a,...u.queryParameters,...f};if(d.value&&(g["x-algolia-agent"]=d.value),m&&m.queryParameters)for(let P of Object.keys(m.queryParameters))!m.queryParameters[P]||Object.prototype.toString.call(m.queryParameters[P])==="[object Object]"?g[P]=m.queryParameters[P]:g[P]=m.queryParameters[P].toString();let v=0,N=async(P,S)=>{let T=P.pop();if(T===void 0)throw new Z(le(R));let C={...c,...m.timeouts},U={data:E,headers:x,method:u.method,url:te(T,u.path,g),connectTimeout:S(v,C.connect),responseTimeout:S(v,w?C.read:C.write)},k=b=>{let _={request:U,response:b,host:T,triesLeft:P.length};return R.push(_),_},y=await s.send(U);if(ce(y)){let b=k(y);return y.isTimedOut&&v++,t.info("Retryable failure",F(b)),await e.set(T,G(T,y.isTimedOut?"timed out":"down")),N(P,S)}if(ue(y))return ae(y);throw k(y),ie(y,R)},K=r.filter(P=>P.accept==="readWrite"||(w?P.accept==="read":P.accept==="write")),D=await l(K);return N([...D.hosts].reverse(),D.getTimeout)}function h(u,m={}){let w=u.useReadTransporter||u.method==="GET";if(!w)return p(u,m,w);let R=()=>p(u,m);if((m.cacheable||u.cacheable)!==!0)return R();let x={request:u,requestOptions:m,transporter:{queryParameters:a,headers:o}};return i.get(x,()=>n.get(x,()=>n.set(x,R()).then(f=>Promise.all([n.delete(x),f]),f=>Promise.all([n.delete(x),Promise.reject(f)])).then(([f,g])=>g)),{miss:f=>i.set(x,f)})}return{hostsCache:e,requester:s,timeouts:c,logger:t,algoliaAgent:d,baseHeaders:o,baseQueryParameters:a,hosts:r,request:h,requestsCache:n,responsesCache:i}}var O="5.30.0";function de(r){return[{url:`${r}-dsn.algolia.net`,accept:"read",protocol:"https"},{url:`${r}.algolia.net`,accept:"write",protocol:"https"}].concat(M([{url:`${r}-1.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${r}-2.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${r}-3.algolianet.com`,accept:"readWrite",protocol:"https"}]))}function X({appId:r,apiKey:e,authMode:o,algoliaAgents:t,...a}){let d=j(r,e,o),c=B({hosts:de(r),...a,algoliaAgent:W({algoliaAgents:t,client:"Recommend",version:O}),baseHeaders:{"content-type":"text/plain",...d.headers(),...a.baseHeaders},baseQueryParameters:{...d.queryParameters(),...a.baseQueryParameters}});return{transporter:c,appId:r,apiKey:e,clearCache(){return Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>{})},get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(s,n){c.algoliaAgent.add({segment:s,version:n})},setClientApiKey({apiKey:s}){!o||o==="WithinHeaders"?c.baseHeaders["x-algolia-api-key"]=s:c.baseQueryParameters["x-algolia-api-key"]=s},batchRecommendRules({indexName:s,model:n,recommendRule:i},l){if(!s)throw new Error("Parameter `indexName` is required when calling `batchRecommendRules`.");if(!n)throw new Error("Parameter `model` is required when calling `batchRecommendRules`.");let m={method:"POST",path:"/1/indexes/{indexName}/{model}/recommend/rules/batch".replace("{indexName}",encodeURIComponent(s)).replace("{model}",encodeURIComponent(n)),queryParameters:{},headers:{},data:i||{}};return c.request(m,l)},customDelete({path:s,parameters:n},i){if(!s)throw new Error("Parameter `path` is required when calling `customDelete`.");let u={method:"DELETE",path:"/{path}".replace("{path}",s),queryParameters:n||{},headers:{}};return c.request(u,i)},customGet({path:s,parameters:n},i){if(!s)throw new Error("Parameter `path` is required when calling `customGet`.");let u={method:"GET",path:"/{path}".replace("{path}",s),queryParameters:n||{},headers:{}};return c.request(u,i)},customPost({path:s,parameters:n,body:i},l){if(!s)throw new Error("Parameter `path` is required when calling `customPost`.");let m={method:"POST",path:"/{path}".replace("{path}",s),queryParameters:n||{},headers:{},data:i||{}};return c.request(m,l)},customPut({path:s,parameters:n,body:i},l){if(!s)throw new Error("Parameter `path` is required when calling `customPut`.");let m={method:"PUT",path:"/{path}".replace("{path}",s),queryParameters:n||{},headers:{},data:i||{}};return c.request(m,l)},deleteRecommendRule({indexName:s,model:n,objectID:i},l){if(!s)throw new Error("Parameter `indexName` is required when calling `deleteRecommendRule`.");if(!n)throw new Error("Parameter `model` is required when calling `deleteRecommendRule`.");if(!i)throw new Error("Parameter `objectID` is required when calling `deleteRecommendRule`.");let m={method:"DELETE",path:"/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(s)).replace("{model}",encodeURIComponent(n)).replace("{objectID}",encodeURIComponent(i)),queryParameters:{},headers:{}};return c.request(m,l)},getRecommendRule({indexName:s,model:n,objectID:i},l){if(!s)throw new Error("Parameter `indexName` is required when calling `getRecommendRule`.");if(!n)throw new Error("Parameter `model` is required when calling `getRecommendRule`.");if(!i)throw new Error("Parameter `objectID` is required when calling `getRecommendRule`.");let m={method:"GET",path:"/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(s)).replace("{model}",encodeURIComponent(n)).replace("{objectID}",encodeURIComponent(i)),queryParameters:{},headers:{}};return c.request(m,l)},getRecommendStatus({indexName:s,model:n,taskID:i},l){if(!s)throw new Error("Parameter `indexName` is required when calling `getRecommendStatus`.");if(!n)throw new Error("Parameter `model` is required when calling `getRecommendStatus`.");if(!i)throw new Error("Parameter `taskID` is required when calling `getRecommendStatus`.");let m={method:"GET",path:"/1/indexes/{indexName}/{model}/task/{taskID}".replace("{indexName}",encodeURIComponent(s)).replace("{model}",encodeURIComponent(n)).replace("{taskID}",encodeURIComponent(i)),queryParameters:{},headers:{}};return c.request(m,l)},getRecommendations(s,n){if(s&&Array.isArray(s)&&(s={requests:s}),!s)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!s.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");let h={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:s,useReadTransporter:!0,cacheable:!0};return c.request(h,n)},searchRecommendRules({indexName:s,model:n,searchRecommendRulesParams:i},l){if(!s)throw new Error("Parameter `indexName` is required when calling `searchRecommendRules`.");if(!n)throw new Error("Parameter `model` is required when calling `searchRecommendRules`.");let m={method:"POST",path:"/1/indexes/{indexName}/{model}/recommend/rules/search".replace("{indexName}",encodeURIComponent(s)).replace("{model}",encodeURIComponent(n)),queryParameters:{},headers:{},data:i||{},useReadTransporter:!0,cacheable:!0};return c.request(m,l)}}}function Pt(r,e,o){if(!r||typeof r!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");return X({appId:r,apiKey:e,timeouts:{connect:1e3,read:2e3,write:3e4},logger:J(),requester:H(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:A(),requestsCache:A({serializable:!1}),hostsCache:q({caches:[L({key:`${O}-${r}`}),A()]}),...o})}export{O as apiClientVersion,Pt as recommendClient};
//# sourceMappingURL=browser.min.js.map