(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@algolia/client-query-suggestions"] = {}));
})(this, (function (exports) { 'use strict';

	function k(){function r(e){return new Promise(s=>{let t=new XMLHttpRequest;t.open(e.method,e.url,true),Object.keys(e.headers).forEach(a=>t.setRequestHeader(a,e.headers[a]));let i=(a,o)=>setTimeout(()=>{t.abort(),s({status:0,content:o,isTimedOut:true});},a),m=i(e.connectTimeout,"Connection timeout"),g;t.onreadystatechange=()=>{t.readyState>t.OPENED&&g===void 0&&(clearTimeout(m),g=i(e.responseTimeout,"Socket timeout"));},t.onerror=()=>{t.status===0&&(clearTimeout(m),clearTimeout(g),s({content:t.responseText||"Network request failed",status:t.status,isTimedOut:false}));},t.onload=()=>{clearTimeout(m),clearTimeout(g),s({content:t.responseText,status:t.status,isTimedOut:false});},t.send(e.data);})}return {send:r}}function W(r){let e,s=`algolia-client-js-${r.key}`;function t(){return e===void 0&&(e=r.localStorage||window.localStorage),e}function i(){return JSON.parse(t().getItem(s)||"{}")}function m(a){t().setItem(s,JSON.stringify(a));}function g(){let a=r.timeToLive?r.timeToLive*1e3:null,o=i(),n=Object.fromEntries(Object.entries(o).filter(([,d])=>d.timestamp!==void 0));if(m(n),!a)return;let c=Object.fromEntries(Object.entries(n).filter(([,d])=>{let h=new Date().getTime();return !(d.timestamp+a<h)}));m(c);}return {get(a,o,n={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(g(),i()[JSON.stringify(a)])).then(c=>Promise.all([c?c.value:o(),c!==void 0])).then(([c,d])=>Promise.all([c,d||n.miss(c)])).then(([c])=>c)},set(a,o){return Promise.resolve().then(()=>{let n=i();return n[JSON.stringify(a)]={timestamp:new Date().getTime(),value:o},t().setItem(s,JSON.stringify(n)),o})},delete(a){return Promise.resolve().then(()=>{let o=i();delete o[JSON.stringify(a)],t().setItem(s,JSON.stringify(o));})},clear(){return Promise.resolve().then(()=>{t().removeItem(s);})}}}function V(){return {get(r,e,s={miss:()=>Promise.resolve()}){return e().then(i=>Promise.all([i,s.miss(i)])).then(([i])=>i)},set(r,e){return Promise.resolve(e)},delete(r){return Promise.resolve()},clear(){return Promise.resolve()}}}function R(r){let e=[...r.caches],s=e.shift();return s===void 0?V():{get(t,i,m={miss:()=>Promise.resolve()}){return s.get(t,i,m).catch(()=>R({caches:e}).get(t,i,m))},set(t,i){return s.set(t,i).catch(()=>R({caches:e}).set(t,i))},delete(t){return s.delete(t).catch(()=>R({caches:e}).delete(t))},clear(){return s.clear().catch(()=>R({caches:e}).clear())}}}function v(r={serializable:true}){let e={};return {get(s,t,i={miss:()=>Promise.resolve()}){let m=JSON.stringify(s);if(m in e)return Promise.resolve(r.serializable?JSON.parse(e[m]):e[m]);let g=t();return g.then(a=>i.miss(a)).then(()=>g)},set(s,t){return e[JSON.stringify(s)]=r.serializable?JSON.stringify(t):t,Promise.resolve(t)},delete(s){return delete e[JSON.stringify(s)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function Y(r){let e={value:`Algolia for JavaScript (${r})`,add(s){let t=`; ${s.segment}${s.version!==void 0?` (${s.version})`:""}`;return e.value.indexOf(t)===-1&&(e.value=`${e.value}${t}`),e}};return e}function $(r,e,s="WithinHeaders"){let t={"x-algolia-api-key":e,"x-algolia-application-id":r};return {headers(){return s==="WithinHeaders"?t:{}},queryParameters(){return s==="WithinQueryParameters"?t:{}}}}function j({algoliaAgents:r,client:e,version:s}){let t=Y(s).add({segment:e,version:s});return r.forEach(i=>t.add(i)),t}function F(){return {debug(r,e){return Promise.resolve()},info(r,e){return Promise.resolve()},error(r,e){return Promise.resolve()}}}var Q=2*60*1e3;function G(r,e="up"){let s=Date.now();function t(){return e==="up"||Date.now()-s>Q}function i(){return e==="timed out"&&Date.now()-s<=Q}return {...r,status:e,lastUpdate:s,isUp:t,isTimedOut:i}}var J=class extends Error{name="AlgoliaError";constructor(r,e){super(r),e&&(this.name=e);}};var z=class extends J{stackTrace;constructor(r,e,s){super(r,s),this.stackTrace=e;}},Z=class extends z{constructor(r){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",r,"RetryError");}},I=class extends z{status;constructor(r,e,s,t="ApiError"){super(r,s,t),this.status=e;}},ee=class extends J{response;constructor(r,e){super(r,"DeserializationError"),this.response=e;}},re=class extends I{error;constructor(r,e,s,t){super(r,e,t,"DetailedApiError"),this.error=s;}};function te(r,e,s){let t=se(s),i=`${r.protocol}://${r.url}${r.port?`:${r.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return t.length&&(i+=`?${t}`),i}function se(r){return Object.keys(r).filter(e=>r[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(r[e])==="[object Array]"?r[e].join(","):r[e]).replace(/\+/g,"%20")}`).join("&")}function oe(r,e){if(r.method==="GET"||r.data===void 0&&e.data===void 0)return;let s=Array.isArray(r.data)?r.data:{...r.data,...e.data};return JSON.stringify(s)}function ne(r,e,s){let t={Accept:"application/json",...r,...e,...s},i={};return Object.keys(t).forEach(m=>{let g=t[m];i[m.toLowerCase()]=g;}),i}function ae(r){try{return JSON.parse(r.content)}catch(e){throw new ee(e.message,r)}}function ie({content:r,status:e},s){try{let t=JSON.parse(r);return "error"in t?new re(t.message,e,t.error,s):new I(t.message,e,s)}catch{}return new I(r,e,s)}function ue({isTimedOut:r,status:e}){return !r&&~~e===0}function ce({isTimedOut:r,status:e}){return r||ue({isTimedOut:r,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function le({status:r}){return ~~(r/100)===2}function me(r){return r.map(e=>M(e))}function M(r){let e=r.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...r,request:{...r.request,headers:{...r.request.headers,...e}}}}function B({hosts:r,hostsCache:e,baseHeaders:s,logger:t,baseQueryParameters:i,algoliaAgent:m,timeouts:g,requester:a,requestsCache:o,responsesCache:n}){async function c(u){let l=await Promise.all(u.map(p=>e.get(p,()=>Promise.resolve(G(p))))),P=l.filter(p=>p.isUp()),y=l.filter(p=>p.isTimedOut()),C=[...P,...y];return {hosts:C.length>0?C:u,getTimeout(p,E){return (y.length===0&&p===0?1:y.length+3+p)*E}}}async function d(u,l,P=true){let y=[],C=oe(u,l),q=ne(s,u.headers,l.headers),p=u.method==="GET"?{...u.data,...l.data}:{},E={...i,...u.queryParameters,...p};if(m.value&&(E["x-algolia-agent"]=m.value),l&&l.queryParameters)for(let f of Object.keys(l.queryParameters))!l.queryParameters[f]||Object.prototype.toString.call(l.queryParameters[f])==="[object Object]"?E[f]=l.queryParameters[f]:E[f]=l.queryParameters[f].toString();let x=0,D=async(f,S)=>{let T=f.pop();if(T===void 0)throw new Z(me(y));let A={...g,...l.timeouts},U={data:C,headers:q,method:u.method,url:te(T,u.path,E),connectTimeout:S(x,A.connect),responseTimeout:S(x,P?A.read:A.write)},L=N=>{let H={request:U,response:N,host:T,triesLeft:f.length};return y.push(H),H},w=await a.send(U);if(ce(w)){let N=L(w);return w.isTimedOut&&x++,t.info("Retryable failure",M(N)),await e.set(T,G(T,w.isTimedOut?"timed out":"down")),D(f,S)}if(le(w))return ae(w);throw L(w),ie(w,y)},K=r.filter(f=>f.accept==="readWrite"||(P?f.accept==="read":f.accept==="write")),_=await c(K);return D([..._.hosts].reverse(),_.getTimeout)}function h(u,l={}){let P=u.useReadTransporter||u.method==="GET";if(!P)return d(u,l,P);let y=()=>d(u,l);if((l.cacheable||u.cacheable)!==true)return y();let q={request:u,requestOptions:l,transporter:{queryParameters:i,headers:s}};return n.get(q,()=>o.get(q,()=>o.set(q,y()).then(p=>Promise.all([o.delete(q),p]),p=>Promise.all([o.delete(q),Promise.reject(p)])).then(([p,E])=>E)),{miss:p=>n.set(q,p)})}return {hostsCache:e,requester:a,timeouts:g,logger:t,algoliaAgent:m,baseHeaders:s,baseQueryParameters:i,hosts:r,request:h,requestsCache:o,responsesCache:n}}var O="5.30.0",b=["eu","us"];function de(r){return [{url:"query-suggestions.{region}.algolia.com".replace("{region}",r),accept:"readWrite",protocol:"https"}]}function X({appId:r,apiKey:e,authMode:s,algoliaAgents:t,region:i,...m}){let g=$(r,e,s),a=B({hosts:de(i),...m,algoliaAgent:j({algoliaAgents:t,client:"QuerySuggestions",version:O}),baseHeaders:{"content-type":"text/plain",...g.headers(),...m.baseHeaders},baseQueryParameters:{...g.queryParameters(),...m.baseQueryParameters}});return {transporter:a,appId:r,apiKey:e,clearCache(){return Promise.all([a.requestsCache.clear(),a.responsesCache.clear()]).then(()=>{})},get _ua(){return a.algoliaAgent.value},addAlgoliaAgent(o,n){a.algoliaAgent.add({segment:o,version:n});},setClientApiKey({apiKey:o}){!s||s==="WithinHeaders"?a.baseHeaders["x-algolia-api-key"]=o:a.baseQueryParameters["x-algolia-api-key"]=o;},createConfig(o,n){if(!o)throw new Error("Parameter `configurationWithIndex` is required when calling `createConfig`.");let u={method:"POST",path:"/1/configs",queryParameters:{},headers:{},data:o};return a.request(u,n)},customDelete({path:o,parameters:n},c){if(!o)throw new Error("Parameter `path` is required when calling `customDelete`.");let l={method:"DELETE",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{}};return a.request(l,c)},customGet({path:o,parameters:n},c){if(!o)throw new Error("Parameter `path` is required when calling `customGet`.");let l={method:"GET",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{}};return a.request(l,c)},customPost({path:o,parameters:n,body:c},d){if(!o)throw new Error("Parameter `path` is required when calling `customPost`.");let P={method:"POST",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{},data:c||{}};return a.request(P,d)},customPut({path:o,parameters:n,body:c},d){if(!o)throw new Error("Parameter `path` is required when calling `customPut`.");let P={method:"PUT",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{},data:c||{}};return a.request(P,d)},deleteConfig({indexName:o},n){if(!o)throw new Error("Parameter `indexName` is required when calling `deleteConfig`.");let u={method:"DELETE",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(o)),queryParameters:{},headers:{}};return a.request(u,n)},getAllConfigs(o){let h={method:"GET",path:"/1/configs",queryParameters:{},headers:{}};return a.request(h,o)},getConfig({indexName:o},n){if(!o)throw new Error("Parameter `indexName` is required when calling `getConfig`.");let u={method:"GET",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(o)),queryParameters:{},headers:{}};return a.request(u,n)},getConfigStatus({indexName:o},n){if(!o)throw new Error("Parameter `indexName` is required when calling `getConfigStatus`.");let u={method:"GET",path:"/1/configs/{indexName}/status".replace("{indexName}",encodeURIComponent(o)),queryParameters:{},headers:{}};return a.request(u,n)},getLogFile({indexName:o},n){if(!o)throw new Error("Parameter `indexName` is required when calling `getLogFile`.");let u={method:"GET",path:"/1/logs/{indexName}".replace("{indexName}",encodeURIComponent(o)),queryParameters:{},headers:{}};return a.request(u,n)},updateConfig({indexName:o,configuration:n},c){if(!o)throw new Error("Parameter `indexName` is required when calling `updateConfig`.");if(!n)throw new Error("Parameter `configuration` is required when calling `updateConfig`.");if(!n.sourceIndices)throw new Error("Parameter `configuration.sourceIndices` is required when calling `updateConfig`.");let l={method:"PUT",path:"/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(o)),queryParameters:{},headers:{},data:n};return a.request(l,c)}}}function Ge(r,e,s,t){if(!r||typeof r!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");if(!s||s&&(typeof s!="string"||!b.includes(s)))throw new Error(`\`region\` is required and must be one of the following: ${b.join(", ")}`);return X({appId:r,apiKey:e,region:s,timeouts:{connect:1e3,read:2e3,write:3e4},logger:F(),requester:k(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:v(),requestsCache:v({serializable:false}),hostsCache:R({caches:[W({key:`${O}-${r}`}),v()]}),...t})}

	exports.apiClientVersion = O;
	exports.querySuggestionsClient = Ge;

}));
