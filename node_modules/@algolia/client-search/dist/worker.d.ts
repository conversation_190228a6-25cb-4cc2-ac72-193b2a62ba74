import * as _algolia_client_common from '@algolia/client-common';
import { CreateIterablePromise, CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

type AddApiKeyResponse = {
    /**
     * API key.
     */
    key: string;
    /**
     * Date and time when the object was created, in RFC 3339 format.
     */
    createdAt: string;
};

/**
 * Access control list permissions.
 */
type Acl = 'addObject' | 'analytics' | 'browse' | 'deleteObject' | 'deleteIndex' | 'editSettings' | 'inference' | 'listIndexes' | 'logs' | 'personalization' | 'recommendation' | 'search' | 'seeUnretrievableAttributes' | 'settings' | 'usage';

/**
 * API key object.
 */
type ApiKey = {
    /**
     * Permissions that determine the type of API requests this key can make. The required ACL is listed in each endpoint\'s reference. For more information, see [access control list](https://www.algolia.com/doc/guides/security/api-keys/#access-control-list-acl).
     */
    acl: Array<Acl>;
    /**
     * Description of an API key to help you identify this API key.
     */
    description?: string | undefined;
    /**
     * Index names or patterns that this API key can access. By default, an API key can access all indices in the same application.  You can use leading and trailing wildcard characters (`*`):  - `dev_*` matches all indices starting with \"dev_\". - `*_dev` matches all indices ending with \"_dev\". - `*_products_*` matches all indices containing \"_products_\".
     */
    indexes?: Array<string> | undefined;
    /**
     * Maximum number of results this API key can retrieve in one query. By default, there\'s no limit.
     */
    maxHitsPerQuery?: number | undefined;
    /**
     * Maximum number of API requests allowed per IP address or [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/) per hour.  If this limit is reached, the API returns an error with status code `429`. By default, there\'s no limit.
     */
    maxQueriesPerIPPerHour?: number | undefined;
    /**
     * Query parameters to add when making API requests with this API key.  To restrict this API key to specific IP addresses, add the `restrictSources` parameter. You can only add a single source, but you can provide a range of IP addresses.  Creating an API key fails if the request is made from an IP address outside the restricted range.
     */
    queryParameters?: string | undefined;
    /**
     * Allowed HTTP referrers for this API key.  By default, all referrers are allowed. You can use leading and trailing wildcard characters (`*`):  - `https://algolia.com/_*` allows all referrers starting with \"https://algolia.com/\" - `*.algolia.com` allows all referrers ending with \".algolia.com\" - `*algolia.com*` allows all referrers in the domain \"algolia.com\".  Like all HTTP headers, referrers can be spoofed. Don\'t rely on them to secure your data. For more information, see [HTTP referrer restrictions](https://www.algolia.com/doc/guides/security/security-best-practices/#http-referrers-restrictions).
     */
    referers?: Array<string> | undefined;
    /**
     * Duration (in seconds) after which the API key expires. By default, API keys don\'t expire.
     */
    validity?: number | undefined;
};

/**
 * Type of indexing operation.
 */
type Action = 'addObject' | 'updateObject' | 'partialUpdateObject' | 'partialUpdateObjectNoCreate' | 'deleteObject' | 'delete' | 'clear';

type MultipleBatchRequest = {
    action: Action;
    /**
     * Operation arguments (varies with specified `action`).
     */
    body?: Record<string, unknown> | undefined;
    /**
     * Index name (case-sensitive).
     */
    indexName: string;
};

/**
 * Batch parameters.
 */
type BatchParams = {
    requests: Array<MultipleBatchRequest>;
};

type BatchResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Unique record identifiers.
     */
    objectIDs: Array<string>;
};

/**
 * Whether certain properties of the search response are calculated exhaustive (exact) or approximated.
 */
type Exhaustive = {
    /**
     * Whether the facet count is exhaustive (`true`) or approximate (`false`). See the [related discussion](https://support.algolia.com/hc/en-us/articles/4406975248145-Why-are-my-facet-and-hit-counts-not-accurate-).
     */
    facetsCount?: boolean | undefined;
    /**
     * The value is `false` if not all facet values are retrieved.
     */
    facetValues?: boolean | undefined;
    /**
     * Whether the `nbHits` is exhaustive (`true`) or approximate (`false`). When the query takes more than 50ms to be processed, the engine makes an approximation. This can happen when using complex filters on millions of records, when typo-tolerance was not exhaustive, or when enough hits have been retrieved (for example, after the engine finds 10,000 exact matches). `nbHits` is reported as non-exhaustive whenever an approximation is made, even if the approximation didn’t, in the end, impact the exhaustivity of the query.
     */
    nbHits?: boolean | undefined;
    /**
     * Rules matching exhaustivity. The value is `false` if rules were enable for this query, and could not be fully processed due a timeout. This is generally caused by the number of alternatives (such as typos) which is too large.
     */
    rulesMatch?: boolean | undefined;
    /**
     * Whether the typo search was exhaustive (`true`) or approximate (`false`). An approximation is done when the typo search query part takes more than 10% of the query budget (ie. 5ms by default) to be processed (this can happen when a lot of typo alternatives exist for the query). This field will not be included when typo-tolerance is entirely disabled.
     */
    typo?: boolean | undefined;
};

type FacetStats = {
    /**
     * Minimum value in the results.
     */
    min?: number | undefined;
    /**
     * Maximum value in the results.
     */
    max?: number | undefined;
    /**
     * Average facet value in the results.
     */
    avg?: number | undefined;
    /**
     * Sum of all values in the results.
     */
    sum?: number | undefined;
};

/**
 * Redirect rule data.
 */
type RedirectRuleIndexData = {
    ruleObjectID: string;
};

type RedirectRuleIndexMetadata = {
    /**
     * Source index for the redirect rule.
     */
    source: string;
    /**
     * Destination index for the redirect rule.
     */
    dest: string;
    /**
     * Reason for the redirect rule.
     */
    reason: string;
    /**
     * Redirect rule status.
     */
    succeed: boolean;
    data: RedirectRuleIndexData;
};

/**
 * [Redirect results to a URL](https://www.algolia.com/doc/guides/managing-results/rules/merchandising-and-promoting/how-to/redirects/), this this parameter is for internal use only.
 */
type Redirect = {
    index?: Array<RedirectRuleIndexMetadata> | undefined;
};

/**
 * Order of facet names.
 */
type Facets = {
    /**
     * Explicit order of facets or facet values.  This setting lets you always show specific facets or facet values at the top of the list.
     */
    order?: Array<string> | undefined;
};

/**
 * Order of facet values that aren\'t explicitly positioned with the `order` setting.  - `count`.   Order remaining facet values by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Sort facet values alphabetically.  - `hidden`.   Don\'t show facet values that aren\'t explicitly positioned.
 */
type SortRemainingBy = 'count' | 'alpha' | 'hidden';

type Value = {
    /**
     * Explicit order of facets or facet values.  This setting lets you always show specific facets or facet values at the top of the list.
     */
    order?: Array<string> | undefined;
    sortRemainingBy?: SortRemainingBy | undefined;
    /**
     * Hide facet values.
     */
    hide?: Array<string> | undefined;
};

/**
 * Order of facet names and facet values in your UI.
 */
type FacetOrdering = {
    facets?: Facets | undefined;
    /**
     * Order of facet values. One object for each facet.
     */
    values?: {
        [key: string]: Value;
    } | undefined;
};

/**
 * The redirect rule container.
 */
type RedirectURL = {
    url?: string | undefined;
};

/**
 * URL for an image to show inside a banner.
 */
type BannerImageUrl = {
    url?: string | undefined;
};

/**
 * Image to show inside a banner.
 */
type BannerImage = {
    urls?: Array<BannerImageUrl> | undefined;
    title?: string | undefined;
};

/**
 * Link for a banner defined in the Merchandising Studio.
 */
type BannerLink = {
    url?: string | undefined;
};

/**
 * Banner with image and link to redirect users.
 */
type Banner = {
    image?: BannerImage | undefined;
    link?: BannerLink | undefined;
};

/**
 * Widgets returned from any rules that are applied to the current search.
 */
type Widgets = {
    /**
     * Banners defined in the Merchandising Studio for a given search.
     */
    banners?: Array<Banner> | undefined;
};

/**
 * Extra data that can be used in the search UI.  You can use this to control aspects of your search UI, such as the order of facet names and values without changing your frontend code.
 */
type RenderingContent = {
    facetOrdering?: FacetOrdering | undefined;
    redirect?: RedirectURL | undefined;
    widgets?: Widgets | undefined;
};

type BaseSearchResponse = Record<string, any> & {
    /**
     * A/B test ID. This is only included in the response for indices that are part of an A/B test.
     */
    abTestID?: number | undefined;
    /**
     * Variant ID. This is only included in the response for indices that are part of an A/B test.
     */
    abTestVariantID?: number | undefined;
    /**
     * Computed geographical location.
     */
    aroundLatLng?: string | undefined;
    /**
     * Distance from a central coordinate provided by `aroundLatLng`.
     */
    automaticRadius?: string | undefined;
    exhaustive?: Exhaustive | undefined;
    /**
     * Rules applied to the query.
     */
    appliedRules?: Array<Record<string, unknown>> | undefined;
    /**
     * See the `facetsCount` field of the `exhaustive` object in the response.
     */
    exhaustiveFacetsCount?: boolean | undefined;
    /**
     * See the `nbHits` field of the `exhaustive` object in the response.
     */
    exhaustiveNbHits?: boolean | undefined;
    /**
     * See the `typo` field of the `exhaustive` object in the response.
     */
    exhaustiveTypo?: boolean | undefined;
    /**
     * Facet counts.
     */
    facets?: {
        [key: string]: {
            [key: string]: number;
        };
    } | undefined;
    /**
     * Statistics for numerical facets.
     */
    facets_stats?: {
        [key: string]: FacetStats;
    } | undefined;
    /**
     * Index name used for the query.
     */
    index?: string | undefined;
    /**
     * Index name used for the query. During A/B testing, the targeted index isn\'t always the index used by the query.
     */
    indexUsed?: string | undefined;
    /**
     * Warnings about the query.
     */
    message?: string | undefined;
    /**
     * Number of hits selected and sorted by the relevant sort algorithm.
     */
    nbSortedHits?: number | undefined;
    /**
     * Post-[normalization](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/#what-does-normalization-mean) query string that will be searched.
     */
    parsedQuery?: string | undefined;
    /**
     * Time the server took to process the request, in milliseconds.
     */
    processingTimeMS?: number | undefined;
    /**
     * Experimental. List of processing steps and their times, in milliseconds. You can use this list to investigate performance issues.
     */
    processingTimingsMS?: Record<string, unknown> | undefined;
    /**
     * Markup text indicating which parts of the original query have been removed to retrieve a non-empty result set.
     */
    queryAfterRemoval?: string | undefined;
    redirect?: Redirect | undefined;
    renderingContent?: RenderingContent | undefined;
    /**
     * Time the server took to process the request, in milliseconds.
     */
    serverTimeMS?: number | undefined;
    /**
     * Host name of the server that processed the request.
     */
    serverUsed?: string | undefined;
    /**
     * An object with custom data.  You can store up to 32kB as custom data.
     */
    userData?: any | null | undefined;
    /**
     * Unique identifier for the query. This is used for [click analytics](https://www.algolia.com/doc/guides/analytics/click-analytics/).
     */
    queryID?: string | undefined;
    /**
     * Whether automatic events collection is enabled for the application.
     */
    _automaticInsights?: boolean | undefined;
};

type BrowsePagination = {
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of results (hits).
     */
    nbHits?: number | undefined;
    /**
     * Number of pages of results.
     */
    nbPages?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};

type Cursor = {
    /**
     * Cursor to get the next page of the response.  The parameter must match the value returned in the response of a previous request. The last page of the response does not return a `cursor` attribute.
     */
    cursor?: string | undefined;
};

/**
 * Whether the whole query string matches or only a part.
 */
type MatchLevel = 'none' | 'partial' | 'full';

/**
 * Surround words that match the query with HTML tags for highlighting.
 */
type HighlightResultOption = {
    /**
     * Highlighted attribute value, including HTML tags.
     */
    value: string;
    matchLevel: MatchLevel;
    /**
     * List of matched words from the search query.
     */
    matchedWords: Array<string>;
    /**
     * Whether the entire attribute value is highlighted.
     */
    fullyHighlighted?: boolean | undefined;
};

type HighlightResult = HighlightResultOption | {
    [key: string]: HighlightResult;
} | Array<HighlightResult>;

type MatchedGeoLocation = {
    /**
     * Latitude of the matched location.
     */
    lat?: number | undefined;
    /**
     * Longitude of the matched location.
     */
    lng?: number | undefined;
    /**
     * Distance between the matched location and the search location (in meters).
     */
    distance?: number | undefined;
};

type Personalization = {
    /**
     * The score of the filters.
     */
    filtersScore?: number | undefined;
    /**
     * The score of the ranking.
     */
    rankingScore?: number | undefined;
    /**
     * The score of the event.
     */
    score?: number | undefined;
};

/**
 * Object with detailed information about the record\'s ranking.
 */
type RankingInfo = {
    /**
     * Whether a filter matched the query.
     */
    filters?: number | undefined;
    /**
     * Position of the first matched word in the best matching attribute of the record.
     */
    firstMatchedWord: number;
    /**
     * Distance between the geo location in the search query and the best matching geo location in the record, divided by the geo precision (in meters).
     */
    geoDistance: number;
    /**
     * Precision used when computing the geo distance, in meters.
     */
    geoPrecision?: number | undefined;
    matchedGeoLocation?: MatchedGeoLocation | undefined;
    personalization?: Personalization | undefined;
    /**
     * Number of exactly matched words.
     */
    nbExactWords: number;
    /**
     * Number of typos encountered when matching the record.
     */
    nbTypos: number;
    /**
     * Whether the record was promoted by a rule.
     */
    promoted?: boolean | undefined;
    /**
     * Number of words between multiple matches in the query plus 1. For single word queries, `proximityDistance` is 0.
     */
    proximityDistance?: number | undefined;
    /**
     * Overall ranking of the record, expressed as a single integer. This attribute is internal.
     */
    userScore: number;
    /**
     * Number of matched words.
     */
    words?: number | undefined;
    /**
     * Whether the record is re-ranked.
     */
    promotedByReRanking?: boolean | undefined;
};

/**
 * Snippets that show the context around a matching search query.
 */
type SnippetResultOption = {
    /**
     * Highlighted attribute value, including HTML tags.
     */
    value: string;
    matchLevel: MatchLevel;
};

type SnippetResult = SnippetResultOption | {
    [key: string]: SnippetResult;
} | Array<SnippetResult>;

/**
 * Search result.  A hit is a record from your index, augmented with special attributes for highlighting, snippeting, and ranking.
 */
type Hit<T = Record<string, unknown>> = T & {
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * Surround words that match the query with HTML tags for highlighting.
     */
    _highlightResult?: {
        [key: string]: HighlightResult;
    } | undefined;
    /**
     * Snippets that show the context around a matching search query.
     */
    _snippetResult?: {
        [key: string]: SnippetResult;
    } | undefined;
    _rankingInfo?: RankingInfo | undefined;
    _distinctSeqID?: number | undefined;
};

type SearchHits<T = Record<string, unknown>> = Record<string, any> & {
    /**
     * Search results (hits).  Hits are records from your index that match the search criteria, augmented with additional attributes, such as, for highlighting.
     */
    hits: Hit<T>[];
    /**
     * Search query.
     */
    query: string;
    /**
     * URL-encoded string of all search parameters.
     */
    params: string;
};

type BrowseResponse<T = Record<string, unknown>> = BaseSearchResponse & BrowsePagination & SearchHits<T> & Cursor;

/**
 * Response and creation timestamp.
 */
type CreatedAtResponse = {
    /**
     * Date and time when the object was created, in RFC 3339 format.
     */
    createdAt: string;
};

type DeleteApiKeyResponse = {
    /**
     * Date and time when the object was deleted, in RFC 3339 format.
     */
    deletedAt: string;
};

type DeleteSourceResponse = {
    /**
     * Date and time when the object was deleted, in RFC 3339 format.
     */
    deletedAt: string;
};

/**
 * Response, taskID, and deletion timestamp.
 */
type DeletedAtResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Date and time when the object was deleted, in RFC 3339 format.
     */
    deletedAt: string;
};

/**
 * Key-value pairs of [supported language ISO codes](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/) and boolean values.
 */
type StandardEntries = {
    /**
     * Key-value pair of a language ISO code and a boolean value.
     */
    plurals?: {
        [key: string]: boolean;
    } | null | undefined;
    /**
     * Key-value pair of a language ISO code and a boolean value.
     */
    stopwords?: {
        [key: string]: boolean;
    } | null | undefined;
    /**
     * Key-value pair of a language ISO code and a boolean value.
     */
    compounds?: {
        [key: string]: boolean;
    } | null | undefined;
};

/**
 * Turn on or off the built-in Algolia stop words for a specific language.
 */
type DictionarySettingsParams = {
    disableStandardEntries: StandardEntries;
};

type BaseGetApiKeyResponse = {
    /**
     * API key.
     */
    value: string;
    /**
     * Timestamp when the object was created, in milliseconds since the Unix epoch.
     */
    createdAt: number;
};

type GetApiKeyResponse = BaseGetApiKeyResponse & ApiKey;

type GetDictionarySettingsResponse = {
    disableStandardEntries: StandardEntries;
};

type LogQuery = {
    /**
     * Index targeted by the query.
     */
    index_name?: string | undefined;
    /**
     * A user identifier.
     */
    user_token?: string | undefined;
    /**
     * Unique query identifier.
     */
    query_id?: string | undefined;
};

type Log = {
    /**
     * Date and time of the API request, in RFC 3339 format.
     */
    timestamp: string;
    /**
     * HTTP method of the request.
     */
    method: string;
    /**
     * HTTP status code of the response.
     */
    answer_code: string;
    /**
     * Request body.
     */
    query_body: string;
    /**
     * Response body.
     */
    answer: string;
    /**
     * URL of the API endpoint.
     */
    url: string;
    /**
     * IP address of the client that performed the request.
     */
    ip: string;
    /**
     * Request headers (API keys are obfuscated).
     */
    query_headers: string;
    /**
     * SHA1 signature of the log entry.
     */
    sha1: string;
    /**
     * Number of API requests.
     */
    nb_api_calls?: string | undefined;
    /**
     * Processing time for the query in milliseconds. This doesn\'t include latency due to the network.
     */
    processing_time_ms: string;
    /**
     * Index targeted by the query.
     */
    index?: string | undefined;
    /**
     * Query parameters sent with the request.
     */
    query_params?: string | undefined;
    /**
     * Number of search results (hits) returned for the query.
     */
    query_nb_hits?: string | undefined;
    /**
     * Queries performed for the given request.
     */
    inner_queries?: Array<LogQuery> | undefined;
};

type GetLogsResponse = {
    logs: Array<Log>;
};

/**
 * Request body for retrieving records.
 */
type GetObjectsRequest = {
    /**
     * Attributes to retrieve. If not specified, all retrievable attributes are returned.
     */
    attributesToRetrieve?: Array<string> | undefined;
    /**
     * Object ID for the record to retrieve.
     */
    objectID: string;
    /**
     * Index from which to retrieve the records.
     */
    indexName: string;
};

/**
 * Request parameters.
 */
type GetObjectsParams = {
    requests: Array<GetObjectsRequest>;
};

type GetObjectsResponse<T = Record<string, unknown>> = {
    /**
     * An optional status message.
     */
    message?: string | undefined;
    /**
     * Retrieved records.
     */
    results: T[];
};

/**
 * Task status, `published` if the task is completed, `notPublished` otherwise.
 */
type TaskStatus = 'published' | 'notPublished';

type GetTaskResponse = {
    status: TaskStatus;
};

/**
 * Unique user ID.
 */
type UserId = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    userID: string;
    /**
     * Cluster to which the user is assigned.
     */
    clusterName: string;
    /**
     * Number of records belonging to the user.
     */
    nbRecords: number;
    /**
     * Data size used by the user.
     */
    dataSize: number;
};

/**
 * User IDs and clusters.
 */
type GetTopUserIdsResponse = {
    /**
     * Key-value pairs with cluster names as keys and lists of users with the highest number of records per cluster as values.
     */
    topUsers: Array<{
        [key: string]: Array<UserId>;
    }>;
};

type HasPendingMappingsResponse = {
    /**
     * Whether there are clusters undergoing migration, creation, or deletion.
     */
    pending: boolean;
    /**
     * Cluster pending mapping state: migrating, creating, deleting.
     */
    clusters?: {
        [key: string]: Array<string>;
    } | undefined;
};

/**
 * Dictionary type. If `null`, this dictionary type isn\'t supported for the language.
 */
type DictionaryLanguage = {
    /**
     * Number of custom dictionary entries.
     */
    nbCustomEntries?: number | undefined;
};

/**
 * Dictionary language.
 */
type Languages = {
    plurals: DictionaryLanguage | null;
    stopwords: DictionaryLanguage | null;
    compounds: DictionaryLanguage | null;
};

type ListApiKeysResponse = {
    /**
     * API keys.
     */
    keys: Array<GetApiKeyResponse>;
};

/**
 * Clusters.
 */
type ListClustersResponse = {
    /**
     * Key-value pairs with cluster names as keys and lists of users with the highest number of records per cluster as values.
     */
    topUsers: Array<string>;
};

type FetchedIndex = {
    /**
     * Index name.
     */
    name: string;
    /**
     * Index creation date. An empty string means that the index has no records.
     */
    createdAt: string;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
    /**
     * Number of records contained in the index.
     */
    entries: number;
    /**
     * Number of bytes of the index in minified format.
     */
    dataSize: number;
    /**
     * Number of bytes of the index binary file.
     */
    fileSize: number;
    /**
     * Last build time.
     */
    lastBuildTimeS: number;
    /**
     * Number of pending indexing operations. This value is deprecated and should not be used.
     */
    numberOfPendingTasks: number;
    /**
     * A boolean which says whether the index has pending tasks. This value is deprecated and should not be used.
     */
    pendingTask: boolean;
    /**
     * Only present if the index is a replica. Contains the name of the related primary index.
     */
    primary?: string | undefined;
    /**
     * Only present if the index is a primary index with replicas. Contains the names of all linked replicas.
     */
    replicas?: Array<string> | undefined;
    /**
     * Only present if the index is a [virtual replica](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/sort-an-index-alphabetically/#virtual-replicas).
     */
    virtual?: boolean | undefined;
};

type ListIndicesResponse = {
    /**
     * All indices in your Algolia application.
     */
    items: Array<FetchedIndex>;
    /**
     * Number of pages.
     */
    nbPages?: number | undefined;
};

/**
 * User ID data.
 */
type ListUserIdsResponse = {
    /**
     * User IDs.
     */
    userIDs: Array<UserId>;
};

type MultipleBatchResponse = {
    /**
     * Task IDs. One for each index.
     */
    taskID: {
        [key: string]: number;
    };
    /**
     * Unique record identifiers.
     */
    objectIDs: Array<string>;
};

type RemoveUserIdResponse = {
    /**
     * Date and time when the object was deleted, in RFC 3339 format.
     */
    deletedAt: string;
};

/**
 * Response, taskID, and update timestamp.
 */
type UpdatedAtResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
};

type ReplaceAllObjectsResponse = {
    copyOperationResponse: UpdatedAtResponse;
    /**
     * The response of the `batch` request(s).
     */
    batchResponses: Array<BatchResponse>;
    moveOperationResponse: UpdatedAtResponse;
};

type ReplaceSourceResponse = {
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
};

/**
 * Which part of the search query the pattern should match:  - `startsWith`. The pattern must match the beginning of the query. - `endsWith`. The pattern must match the end of the query. - `is`. The pattern must match the query exactly. - `contains`. The pattern must match anywhere in the query.  Empty queries are only allowed as patterns with `anchoring: is`.
 */
type Anchoring = 'is' | 'startsWith' | 'endsWith' | 'contains';

type Condition = {
    /**
     * Query pattern that triggers the rule.  You can use either a literal string, or a special pattern `{facet:ATTRIBUTE}`, where `ATTRIBUTE` is a facet name. The rule is triggered if the query matches the literal string or a value of the specified facet. For example, with `pattern: {facet:genre}`, the rule is triggered when users search for a genre, such as \"comedy\".
     */
    pattern?: string | undefined;
    anchoring?: Anchoring | undefined;
    /**
     * Whether the pattern should match plurals, synonyms, and typos.
     */
    alternatives?: boolean | undefined;
    /**
     * An additional restriction that only triggers the rule, when the search has the same value as `ruleContexts` parameter. For example, if `context: mobile`, the rule is only triggered when the search request has a matching `ruleContexts: mobile`. A rule context must only contain alphanumeric characters.
     */
    context?: string | undefined;
    /**
     * Filters that trigger the rule.  You can add filters using the syntax `facet:value` so that the rule is triggered, when the specific filter is selected. You can use `filters` on its own or combine it with the `pattern` parameter. You can\'t combine multiple filters with `OR` and you can\'t use numeric filters.
     */
    filters?: string | undefined;
};

/**
 * Object ID of the record to hide.
 */
type ConsequenceHide = {
    /**
     * Unique record identifier.
     */
    objectID: string;
};

/**
 * Range object with lower and upper values in meters to define custom ranges.
 */
type Range = {
    /**
     * Lower boundary of a range in meters. The Geo ranking criterion considers all records within the range to be equal.
     */
    from?: number | undefined;
    /**
     * Upper boundary of a range in meters. The Geo ranking criterion considers all records within the range to be equal.
     */
    value?: number | undefined;
};

/**
 * Precision of a coordinate-based search in meters to group results with similar distances.  The Geo ranking criterion considers all matches within the same range of distances to be equal.
 */
type AroundPrecision = number | Array<Range>;

/**
 * Return all records with a valid `_geoloc` attribute. Don\'t filter by distance.
 */
type AroundRadiusAll = 'all';

/**
 * Maximum radius for a search around a central location.  This parameter works in combination with the `aroundLatLng` and `aroundLatLngViaIP` parameters. By default, the search radius is determined automatically from the density of hits around the central location. The search radius is small if there are many hits close to the central coordinates.
 */
type AroundRadius = number | AroundRadiusAll;

/**
 * Filter the search by facet values, so that only records with the same facet values are retrieved.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  - `[filter1, filter2]` is interpreted as `filter1 AND filter2`. - `[[filter1, filter2], filter3]` is interpreted as `filter1 OR filter2 AND filter3`. - `facet:-value` is interpreted as `NOT facet:value`.  While it\'s best to avoid attributes that start with a `-`, you can still filter them by escaping with a backslash: `facet:\\-value`.
 */
type FacetFilters = Array<FacetFilters> | string;

type InsideBoundingBox = string | Array<Array<number>>;

/**
 * Filter by numeric facets.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  You can use numeric comparison operators: `<`, `<=`, `=`, `!=`, `>`, `>=`. Comparisons are precise up to 3 decimals. You can also provide ranges: `facet:<lower> TO <upper>`. The range includes the lower and upper boundaries. The same combination rules apply as for `facetFilters`.
 */
type NumericFilters = Array<NumericFilters> | string;

/**
 * Filters to promote or demote records in the search results.  Optional filters work like facet filters, but they don\'t exclude records from the search results. Records that match the optional filter rank before records that don\'t match. If you\'re using a negative filter `facet:-value`, matching records rank after records that don\'t match.  - Optional filters don\'t work on virtual replicas. - Optional filters are applied _after_ sort-by attributes. - Optional filters are applied _before_ custom ranking attributes (in the default [ranking](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/)). - Optional filters don\'t work with numeric attributes.
 */
type OptionalFilters = Array<OptionalFilters> | string;

/**
 * ISO code for a supported language.
 */
type SupportedLanguage = 'af' | 'ar' | 'az' | 'bg' | 'bn' | 'ca' | 'cs' | 'cy' | 'da' | 'de' | 'el' | 'en' | 'eo' | 'es' | 'et' | 'eu' | 'fa' | 'fi' | 'fo' | 'fr' | 'ga' | 'gl' | 'he' | 'hi' | 'hu' | 'hy' | 'id' | 'is' | 'it' | 'ja' | 'ka' | 'kk' | 'ko' | 'ku' | 'ky' | 'lt' | 'lv' | 'mi' | 'mn' | 'mr' | 'ms' | 'mt' | 'nb' | 'nl' | 'no' | 'ns' | 'pl' | 'ps' | 'pt' | 'pt-br' | 'qu' | 'ro' | 'ru' | 'sk' | 'sq' | 'sv' | 'sw' | 'ta' | 'te' | 'th' | 'tl' | 'tn' | 'tr' | 'tt' | 'uk' | 'ur' | 'uz' | 'zh';

/**
 * Filter the search by values of the special `_tags` attribute.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  Different from regular facets, `_tags` can only be used for filtering (including or excluding records). You won\'t get a facet count. The same combination and escaping rules apply as for `facetFilters`.
 */
type TagFilters = Array<TagFilters> | string;

type BaseSearchParamsWithoutQuery = {
    /**
     * Keywords to be used instead of the search query to conduct a more broader search.  Using the `similarQuery` parameter changes other settings:  - `queryType` is set to `prefixNone`. - `removeStopWords` is set to true. - `words` is set as the first ranking criterion. - All remaining words are treated as `optionalWords`.  Since the `similarQuery` is supposed to do a broad search, they usually return many results. Combine it with `filters` to narrow down the list of results.
     */
    similarQuery?: string | undefined;
    /**
     * Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can\'t use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can\'t combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     */
    filters?: string | undefined;
    facetFilters?: FacetFilters | undefined;
    optionalFilters?: OptionalFilters | undefined;
    numericFilters?: NumericFilters | undefined;
    tagFilters?: TagFilters | undefined;
    /**
     * Whether to sum all filter scores.  If true, all filter scores are summed. Otherwise, the maximum filter score is kept. For more information, see [filter scores](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/in-depth/filter-scoring/#accumulating-scores-with-sumorfiltersscores).
     */
    sumOrFiltersScores?: boolean | undefined;
    /**
     * Restricts a search to a subset of your searchable attributes. Attribute names are case-sensitive.
     */
    restrictSearchableAttributes?: Array<string> | undefined;
    /**
     * Facets for which to retrieve facet values that match the search criteria and the number of matching facet values.  To retrieve all facets, use the wildcard character `*`. For more information, see [facets](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#contextual-facet-values-and-counts).
     */
    facets?: Array<string> | undefined;
    /**
     * Whether faceting should be applied after deduplication with `distinct`.  This leads to accurate facet counts when using faceting in combination with `distinct`. It\'s usually better to use `afterDistinct` modifiers in the `attributesForFaceting` setting, as `facetingAfterDistinct` only computes correct facet counts if all records have the same facet values for the `attributeForDistinct`.
     */
    facetingAfterDistinct?: boolean | undefined;
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Position of the first hit to retrieve.
     */
    offset?: number | undefined;
    /**
     * Number of hits to retrieve (used in combination with `offset`).
     */
    length?: number | undefined;
    /**
     * Coordinates for the center of a circle, expressed as a comma-separated string of latitude and longitude.  Only records included within a circle around this central location are included in the results. The radius of the circle is determined by the `aroundRadius` and `minimumAroundRadius` settings. This parameter is ignored if you also specify `insidePolygon` or `insideBoundingBox`.
     */
    aroundLatLng?: string | undefined;
    /**
     * Whether to obtain the coordinates from the request\'s IP address.
     */
    aroundLatLngViaIP?: boolean | undefined;
    aroundRadius?: AroundRadius | undefined;
    aroundPrecision?: AroundPrecision | undefined;
    /**
     * Minimum radius (in meters) for a search around a location when `aroundRadius` isn\'t set.
     */
    minimumAroundRadius?: number | undefined;
    insideBoundingBox?: InsideBoundingBox | null | undefined;
    /**
     * Coordinates of a polygon in which to search.  Polygons are defined by 3 to 10,000 points. Each point is represented by its latitude and longitude. Provide multiple polygons as nested arrays. For more information, see [filtering inside polygons](https://www.algolia.com/doc/guides/managing-results/refine-results/geolocation/#filtering-inside-rectangular-or-polygonal-areas). This parameter is ignored if you also specify `insideBoundingBox`.
     */
    insidePolygon?: Array<Array<number>> | undefined;
    /**
     * ISO language codes that adjust settings that are useful for processing natural language queries (as opposed to keyword searches):  - Sets `removeStopWords` and `ignorePlurals` to the list of provided languages. - Sets `removeWordsIfNoResults` to `allOptional`. - Adds a `natural_language` attribute to `ruleContexts` and `analyticsTags`.
     */
    naturalLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Assigns a rule context to the search query.  [Rule contexts](https://www.algolia.com/doc/guides/managing-results/rules/rules-overview/how-to/customize-search-results-by-platform/#whats-a-context) are strings that you can use to trigger matching rules.
     */
    ruleContexts?: Array<string> | undefined;
    /**
     * Impact that Personalization should have on this search.  The higher this value is, the more Personalization determines the ranking compared to other factors. For more information, see [Understanding Personalization impact](https://www.algolia.com/doc/guides/personalization/personalizing-results/in-depth/configuring-personalization/#understanding-personalization-impact).
     */
    personalizationImpact?: number | undefined;
    /**
     * Unique pseudonymous or anonymous user identifier.  This helps with analytics and click and conversion events. For more information, see [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken?: string | undefined;
    /**
     * Whether the search response should include detailed ranking information.
     */
    getRankingInfo?: boolean | undefined;
    /**
     * Whether to take into account an index\'s synonyms for this search.
     */
    synonyms?: boolean | undefined;
    /**
     * Whether to include a `queryID` attribute in the response.  The query ID is a unique identifier for a search query and is required for tracking [click and conversion events](https://www.algolia.com/guides/sending-events/getting-started/).
     */
    clickAnalytics?: boolean | undefined;
    /**
     * Whether this search will be included in Analytics.
     */
    analytics?: boolean | undefined;
    /**
     * Tags to apply to the query for [segmenting analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    analyticsTags?: Array<string> | undefined;
    /**
     * Whether to include this search when calculating processing-time percentiles.
     */
    percentileComputation?: boolean | undefined;
    /**
     * Whether to enable A/B testing for this search.
     */
    enableABTest?: boolean | undefined;
};

type AdvancedSyntaxFeatures = 'exactPhrase' | 'excludeWords';

type AlternativesAsExact = 'ignorePlurals' | 'singleWordSynonym' | 'multiWordsSynonym' | 'ignoreConjugations';

/**
 * Determines how many records of a group are included in the search results.  Records with the same value for the `attributeForDistinct` attribute are considered a group. The `distinct` setting controls how many members of the group are returned. This is useful for [deduplication and grouping](https://www.algolia.com/doc/guides/managing-results/refine-results/grouping/#introducing-algolias-distinct-feature).  The `distinct` setting is ignored if `attributeForDistinct` is not set.
 */
type Distinct = boolean | number;

/**
 * Determines how the [Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes) is computed when the search query has only one word.  - `attribute`.   The Exact ranking criterion is 1 if the query word and attribute value are the same.   For example, a search for \"road\" will match the value \"road\", but not \"road trip\".  - `none`.   The Exact ranking criterion is ignored on single-word searches.  - `word`.   The Exact ranking criterion is 1 if the query word is found in the attribute value.   The query word must have at least 3 characters and must not be a stop word.   Only exact matches will be highlighted,   partial and prefix matches won\'t.
 */
type ExactOnSingleWordQuery = 'attribute' | 'none' | 'word';

type BooleanString = 'true' | 'false';

/**
 * Treat singular, plurals, and other forms of declensions as equivalent. You should only use this feature for the languages used in your index.
 */
type IgnorePlurals = Array<SupportedLanguage> | BooleanString | boolean;

/**
 * Search mode the index will use to query for results.  This setting only applies to indices, for which Algolia enabled NeuralSearch for you.
 */
type Mode = 'neuralSearch' | 'keywordSearch';

/**
 * Words that should be considered optional when found in the query.  By default, records must match all words in the search query to be included in the search results. Adding optional words can help to increase the number of search results by running an additional search query that doesn\'t include the optional words. For example, if the search query is \"action video\" and \"video\" is an optional word, the search engine runs two queries. One for \"action video\" and one for \"action\". Records that match all words are ranked higher.  For a search query with 4 or more words **and** all its words are optional, the number of matched words required for a record to be included in the search results increases for every 1,000 records:  - If `optionalWords` has less than 10 words, the required number of matched words increases by 1:   results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 2 matched words. - If `optionalWords` has 10 or more words, the number of required matched words increases by the number of optional words divided by 5 (rounded down).   For example, with 18 optional words: results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 4 matched words.  For more information, see [Optional words](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/#creating-a-list-of-optional-words).
 */
type OptionalWords = string | Array<string>;

/**
 * Determines if and how query words are interpreted as prefixes.  By default, only the last query word is treated as a prefix (`prefixLast`). To turn off prefix search, use `prefixNone`. Avoid `prefixAll`, which treats all query words as prefixes. This might lead to counterintuitive results and makes your search slower.  For more information, see [Prefix searching](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/prefix-searching/).
 */
type QueryType = 'prefixLast' | 'prefixAll' | 'prefixNone';

/**
 * Restrict [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/) to records that match these filters.
 */
type ReRankingApplyFilter = Array<ReRankingApplyFilter> | string;

/**
 * Removes stop words from the search query.  Stop words are common words like articles, conjunctions, prepositions, or pronouns that have little or no meaning on their own. In English, \"the\", \"a\", or \"and\" are stop words.  You should only use this feature for the languages used in your index.
 */
type RemoveStopWords = Array<SupportedLanguage> | boolean;

/**
 * Strategy for removing words from the query when it doesn\'t return any results. This helps to avoid returning empty search results.  - `none`.   No words are removed when a query doesn\'t return results.  - `lastWords`.   Treat the last (then second to last, then third to last) word as optional,   until there are results or at most 5 words have been removed.  - `firstWords`.   Treat the first (then second, then third) word as optional,   until there are results or at most 5 words have been removed.  - `allOptional`.   Treat all words as optional.  For more information, see [Remove words to improve results](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/in-depth/why-use-remove-words-if-no-results/).
 */
type RemoveWordsIfNoResults = 'none' | 'lastWords' | 'firstWords' | 'allOptional';

/**
 * Settings for the semantic search part of NeuralSearch. Only used when `mode` is `neuralSearch`.
 */
type SemanticSearch = {
    /**
     * Indices from which to collect click and conversion events.  If null, the current index and all its replicas are used.
     */
    eventSources?: Array<string> | null | undefined;
};

/**
 * - `min`. Return matches with the lowest number of typos.   For example, if you have matches without typos, only include those.   But if there are no matches without typos (with 1 typo), include matches with 1 typo (2 typos). - `strict`. Return matches with the two lowest numbers of typos.   With `strict`, the Typo ranking criterion is applied first in the `ranking` setting.
 */
type TypoToleranceEnum = 'min' | 'strict' | 'true' | 'false';

/**
 * Whether [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/) is enabled and how it is applied.  If typo tolerance is true, `min`, or `strict`, [word splitting and concatenation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/splitting-and-concatenation/) are also active.
 */
type TypoTolerance = boolean | TypoToleranceEnum;

type IndexSettingsAsSearchParams = {
    /**
     * Attributes to include in the API response.  To reduce the size of your response, you can retrieve only some of the attributes. Attribute names are case-sensitive.  - `*` retrieves all attributes, except attributes included in the `customRanking` and `unretrievableAttributes` settings. - To retrieve all attributes except a specific one, prefix the attribute with a dash and combine it with the `*`: `[\"*\", \"-ATTRIBUTE\"]`. - The `objectID` attribute is always included.
     */
    attributesToRetrieve?: Array<string> | undefined;
    /**
     * Determines the order in which Algolia returns your results.  By default, each entry corresponds to a [ranking criteria](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/). The tie-breaking algorithm sequentially applies each criterion in the order they\'re specified. If you configure a replica index for [sorting by an attribute](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/sort-by-attribute/), you put the sorting attribute at the top of the list.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order. - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  Before you modify the default setting, you should test your changes in the dashboard, and by [A/B testing](https://www.algolia.com/doc/guides/ab-testing/what-is-ab-testing/).
     */
    ranking?: Array<string> | undefined;
    /**
     * Relevancy threshold below which less relevant results aren\'t included in the results.  You can only set `relevancyStrictness` on [virtual replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/#what-are-virtual-replicas). Use this setting to strike a balance between the relevance and number of returned results.
     */
    relevancyStrictness?: number | undefined;
    /**
     * Attributes to highlight.  By default, all searchable attributes are highlighted. Use `*` to highlight all attributes or use an empty array `[]` to turn off highlighting. Attribute names are case-sensitive.  With highlighting, strings that match the search query are surrounded by HTML tags defined by `highlightPreTag` and `highlightPostTag`. You can use this to visually highlight matching parts of a search query in your UI.  For more information, see [Highlighting and snippeting](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/highlighting-snippeting/js/).
     */
    attributesToHighlight?: Array<string> | undefined;
    /**
     * Attributes for which to enable snippets. Attribute names are case-sensitive.  Snippets provide additional context to matched words. If you enable snippets, they include 10 words, including the matched word. The matched word will also be wrapped by HTML tags for highlighting. You can adjust the number of words with the following notation: `ATTRIBUTE:NUMBER`, where `NUMBER` is the number of words to be extracted.
     */
    attributesToSnippet?: Array<string> | undefined;
    /**
     * HTML tag to insert before the highlighted parts in all highlighted results and snippets.
     */
    highlightPreTag?: string | undefined;
    /**
     * HTML tag to insert after the highlighted parts in all highlighted results and snippets.
     */
    highlightPostTag?: string | undefined;
    /**
     * String used as an ellipsis indicator when a snippet is truncated.
     */
    snippetEllipsisText?: string | undefined;
    /**
     * Whether to restrict highlighting and snippeting to items that at least partially matched the search query. By default, all items are highlighted and snippeted.
     */
    restrictHighlightAndSnippetArrays?: boolean | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
    /**
     * Minimum number of characters a word in the search query must contain to accept matches with [one typo](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     */
    minWordSizefor1Typo?: number | undefined;
    /**
     * Minimum number of characters a word in the search query must contain to accept matches with [two typos](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     */
    minWordSizefor2Typos?: number | undefined;
    typoTolerance?: TypoTolerance | undefined;
    /**
     * Whether to allow typos on numbers in the search query.  Turn off this setting to reduce the number of irrelevant matches when searching in large sets of similar numbers.
     */
    allowTyposOnNumericTokens?: boolean | undefined;
    /**
     * Attributes for which you want to turn off [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/). Attribute names are case-sensitive.  Returning only exact matches can help when:  - [Searching in hyphenated attributes](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/). - Reducing the number of matches when you have too many.   This can happen with attributes that are long blocks of text, such as product descriptions.  Consider alternatives such as `disableTypoToleranceOnWords` or adding synonyms if your attributes have intentional unusual spellings that might look like typos.
     */
    disableTypoToleranceOnAttributes?: Array<string> | undefined;
    ignorePlurals?: IgnorePlurals | undefined;
    removeStopWords?: RemoveStopWords | undefined;
    /**
     * Languages for language-specific query processing steps such as plurals, stop-word removal, and word-detection dictionaries.  This setting sets a default list of languages used by the `removeStopWords` and `ignorePlurals` settings. This setting also sets a dictionary for word detection in the logogram-based [CJK](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/#normalization-for-logogram-based-languages-cjk) languages. To support this, you must place the CJK language **first**.  **You should always specify a query language.** If you don\'t specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     */
    queryLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Whether to split compound words in the query into their building blocks.  For more information, see [Word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#splitting-compound-words). Word segmentation is supported for these languages: German, Dutch, Finnish, Swedish, and Norwegian. Decompounding doesn\'t work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won\'t be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     */
    decompoundQuery?: boolean | undefined;
    /**
     * Whether to enable rules.
     */
    enableRules?: boolean | undefined;
    /**
     * Whether to enable Personalization.
     */
    enablePersonalization?: boolean | undefined;
    queryType?: QueryType | undefined;
    removeWordsIfNoResults?: RemoveWordsIfNoResults | undefined;
    mode?: Mode | undefined;
    semanticSearch?: SemanticSearch | undefined;
    /**
     * Whether to support phrase matching and excluding words from search queries.  Use the `advancedSyntaxFeatures` parameter to control which feature is supported.
     */
    advancedSyntax?: boolean | undefined;
    optionalWords?: OptionalWords | null | undefined;
    /**
     * Searchable attributes for which you want to [turn off the Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes). Attribute names are case-sensitive.  This can be useful for attributes with long values, where the likelihood of an exact match is high, such as product descriptions. Turning off the Exact ranking criterion for these attributes favors exact matching on other attributes. This reduces the impact of individual attributes with a lot of content on ranking.
     */
    disableExactOnAttributes?: Array<string> | undefined;
    exactOnSingleWordQuery?: ExactOnSingleWordQuery | undefined;
    /**
     * Determine which plurals and synonyms should be considered an exact matches.  By default, Algolia treats singular and plural forms of a word, and single-word synonyms, as [exact](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#exact) matches when searching. For example:  - \"swimsuit\" and \"swimsuits\" are treated the same - \"swimsuit\" and \"swimwear\" are treated the same (if they are [synonyms](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/#regular-synonyms)).  - `ignorePlurals`.   Plurals and similar declensions added by the `ignorePlurals` setting are considered exact matches.  - `singleWordSynonym`.   Single-word synonyms, such as \"NY\" = \"NYC\", are considered exact matches.  - `multiWordsSynonym`.   Multi-word synonyms, such as \"NY\" = \"New York\", are considered exact matches.
     */
    alternativesAsExact?: Array<AlternativesAsExact> | undefined;
    /**
     * Advanced search syntax features you want to support.  - `exactPhrase`.   Phrases in quotes must match exactly.   For example, `sparkly blue \"iPhone case\"` only returns records with the exact string \"iPhone case\".  - `excludeWords`.   Query words prefixed with a `-` must not occur in a record.   For example, `search -engine` matches records that contain \"search\" but not \"engine\".  This setting only has an effect if `advancedSyntax` is true.
     */
    advancedSyntaxFeatures?: Array<AdvancedSyntaxFeatures> | undefined;
    distinct?: Distinct | undefined;
    /**
     * Whether to replace a highlighted word with the matched synonym.  By default, the original words are highlighted even if a synonym matches. For example, with `home` as a synonym for `house` and a search for `home`, records matching either \"home\" or \"house\" are included in the search results, and either \"home\" or \"house\" are highlighted.  With `replaceSynonymsInHighlight` set to `true`, a search for `home` still matches the same records, but all occurrences of \"house\" are replaced by \"home\" in the highlighted response.
     */
    replaceSynonymsInHighlight?: boolean | undefined;
    /**
     * Minimum proximity score for two matching words.  This adjusts the [Proximity ranking criterion](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#proximity) by equally scoring matches that are farther apart.  For example, if `minProximity` is 2, neighboring matches and matches with one word between them would have the same score.
     */
    minProximity?: number | undefined;
    /**
     * Properties to include in the API response of search and browse requests.  By default, all response properties are included. To reduce the response size, you can select which properties should be included.  An empty list may lead to an empty API response (except properties you can\'t exclude).  You can\'t exclude these properties: `message`, `warning`, `cursor`, `abTestVariantID`, or any property added by setting `getRankingInfo` to true.  Your search depends on the `hits` field. If you omit this field, searches won\'t return any results. Your UI might also depend on other properties, for example, for pagination. Before restricting the response size, check the impact on your search experience.
     */
    responseFields?: Array<string> | undefined;
    /**
     * Maximum number of facet values to return for each facet.
     */
    maxValuesPerFacet?: number | undefined;
    /**
     * Order in which to retrieve facet values.  - `count`.   Facet values are retrieved by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Retrieve facet values alphabetically.  This setting doesn\'t influence how facet values are displayed in your UI (see `renderingContent`). For more information, see [facet value display](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/facet-display/js/).
     */
    sortFacetValuesBy?: string | undefined;
    /**
     * Whether the best matching attribute should be determined by minimum proximity.  This setting only affects ranking if the Attribute ranking criterion comes before Proximity in the `ranking` setting. If true, the best matching attribute is selected based on the minimum proximity of multiple matches. Otherwise, the best matching attribute is determined by the order in the `searchableAttributes` setting.
     */
    attributeCriteriaComputedByMinProximity?: boolean | undefined;
    renderingContent?: RenderingContent | undefined;
    /**
     * Whether this search will use [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/).  This setting only has an effect if you activated Dynamic Re-Ranking for this index in the Algolia dashboard.
     */
    enableReRanking?: boolean | undefined;
    reRankingApplyFilter?: ReRankingApplyFilter | null | undefined;
};

/**
 * Filter or optional filter to be applied to the search.
 */
type AutomaticFacetFilter = {
    /**
     * Facet name to be applied as filter. The name must match placeholders in the `pattern` parameter. For example, with `pattern: {facet:genre}`, `automaticFacetFilters` must be `genre`.
     */
    facet: string;
    /**
     * Filter scores to give different weights to individual filters.
     */
    score?: number | undefined;
    /**
     * Whether the filter is disjunctive or conjunctive.  If true the filter has multiple matches, multiple occurences are combined with the logical `OR` operation. If false, multiple occurences are combined with the logical `AND` operation.
     */
    disjunctive?: boolean | undefined;
};

/**
 * Filter to be applied to the search.  You can use this to respond to search queries that match a facet value. For example, if users search for \"comedy\", which matches a facet value of the \"genre\" facet, you can filter the results to show the top-ranked comedy movies.
 */
type AutomaticFacetFilters = Array<AutomaticFacetFilter> | Array<string>;

/**
 * Type of edit.
 */
type EditType = 'remove' | 'replace';

type Edit = {
    type?: EditType | undefined;
    /**
     * Text or patterns to remove from the query string.
     */
    delete?: string | undefined;
    /**
     * Text to be added in place of the deleted text inside the query string.
     */
    insert?: string | undefined;
};

type ConsequenceQueryObject = {
    /**
     * Words to remove from the search query.
     */
    remove?: Array<string> | undefined;
    /**
     * Changes to make to the search query.
     */
    edits?: Array<Edit> | undefined;
};

/**
 * Replace or edit the search query.  If `consequenceQuery` is a string, the entire search query is replaced with that string. If `consequenceQuery` is an object, it describes incremental edits made to the query.
 */
type ConsequenceQuery = ConsequenceQueryObject | string;

/**
 * Parameters to apply to this search.  You can use all search parameters, plus special `automaticFacetFilters`, `automaticOptionalFacetFilters`, and `query`.
 */
type Params = {
    query?: ConsequenceQuery | undefined;
    automaticFacetFilters?: AutomaticFacetFilters | undefined;
    automaticOptionalFacetFilters?: AutomaticFacetFilters | undefined;
    renderingContent?: RenderingContent | undefined;
};

type ConsequenceParams = BaseSearchParamsWithoutQuery & IndexSettingsAsSearchParams & Params;

/**
 * Record to promote.
 */
type PromoteObjectID = {
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * Position in the search results where you want to show the promoted records.
     */
    position: number;
};

/**
 * Records to promote.
 */
type PromoteObjectIDs = {
    /**
     * Object IDs of the records you want to promote.  The records are placed as a group at the `position`. For example, if you want to promote four records to position `0`, they will be the first four search results.
     */
    objectIDs: Array<string>;
    /**
     * Position in the search results where you want to show the promoted records.
     */
    position: number;
};

type Promote = PromoteObjectIDs | PromoteObjectID;

/**
 * Effect of the rule.  For more information, see [Consequences](https://www.algolia.com/doc/guides/managing-results/rules/rules-overview/#consequences).
 */
type Consequence = {
    params?: ConsequenceParams | undefined;
    /**
     * Records you want to pin to a specific position in the search results.  You can promote up to 300 records, either individually, or as groups of up to 100 records each.
     */
    promote?: Array<Promote> | undefined;
    /**
     * Whether promoted records must match an active filter for the consequence to be applied.  This ensures that user actions (filtering the search) are given a higher precendence. For example, if you promote a record with the `color: red` attribute, and the user filters the search for `color: blue`, the \"red\" record won\'t be shown.
     */
    filterPromotes?: boolean | undefined;
    /**
     * Records you want to hide from the search results.
     */
    hide?: Array<ConsequenceHide> | undefined;
    /**
     * A JSON object with custom data that will be appended to the `userData` array in the response. This object isn\'t interpreted by the API and is limited to 1&nbsp;kB of minified JSON.
     */
    userData?: Record<string, unknown> | undefined;
};

type TimeRange = {
    /**
     * When the rule should start to be active, in Unix epoch time.
     */
    from: number;
    /**
     * When the rule should stop to be active, in Unix epoch time.
     */
    until: number;
};

/**
 * Rule object.
 */
type Rule = {
    /**
     * Unique identifier of a rule object.
     */
    objectID: string;
    /**
     * Conditions that trigger a rule.  Some consequences require specific conditions or don\'t require any condition. For more information, see [Conditions](https://www.algolia.com/doc/guides/managing-results/rules/rules-overview/#conditions).
     */
    conditions?: Array<Condition> | undefined;
    consequence: Consequence;
    /**
     * Description of the rule\'s purpose to help you distinguish between different rules.
     */
    description?: string | undefined;
    /**
     * Whether the rule is active.
     */
    enabled?: boolean | undefined;
    /**
     * Time periods when the rule is active.
     */
    validity?: Array<TimeRange> | undefined;
};

type SaveObjectResponse = {
    /**
     * Date and time when the object was created, in RFC 3339 format.
     */
    createdAt: string;
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Unique record identifier.
     */
    objectID?: string | undefined;
};

type SaveSynonymResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
    /**
     * Unique identifier of a synonym object.
     */
    id: string;
};

/**
 * Whether a dictionary entry is active.
 */
type DictionaryEntryState = 'enabled' | 'disabled';

/**
 * Whether a dictionary entry is provided by Algolia (standard), or has been added by you (custom).
 */
type DictionaryEntryType = 'custom' | 'standard';

/**
 * Dictionary entry.
 */
type DictionaryEntry = Record<string, any> & {
    /**
     * Unique identifier for the dictionary entry.
     */
    objectID: string;
    language?: SupportedLanguage | undefined;
    /**
     * Matching dictionary word for `stopwords` and `compounds` dictionaries.
     */
    word?: string | undefined;
    /**
     * Matching words in the `plurals` dictionary including declensions.
     */
    words?: Array<string> | undefined;
    /**
     * Invividual components of a compound word in the `compounds` dictionary.
     */
    decomposition?: Array<string> | undefined;
    state?: DictionaryEntryState | undefined;
    type?: DictionaryEntryType | undefined;
};

type SearchDictionaryEntriesResponse = {
    /**
     * Dictionary entries matching the search criteria.
     */
    hits: Array<DictionaryEntry>;
    /**
     * Requested page of the API response.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    page: number;
    /**
     * Number of results (hits).
     */
    nbHits: number;
    /**
     * Number of pages of results.
     */
    nbPages: number;
};

type FacetHits = {
    /**
     * Facet value.
     */
    value: string;
    /**
     * Highlighted attribute value, including HTML tags.
     */
    highlighted: string;
    /**
     * Number of records with this facet value. [The count may be approximated](https://support.algolia.com/hc/en-us/articles/4406975248145-Why-are-my-facet-and-hit-counts-not-accurate-).
     */
    count: number;
};

type SearchForFacetValuesResponse = {
    /**
     * Matching facet values.
     */
    facetHits: Array<FacetHits>;
    /**
     * Whether the facet count is exhaustive (true) or approximate (false). For more information, see [Why are my facet and hit counts not accurate](https://support.algolia.com/hc/en-us/articles/4406975248145-Why-are-my-facet-and-hit-counts-not-accurate-).
     */
    exhaustiveFacetsCount: boolean;
    /**
     * Time the server took to process the request, in milliseconds.
     */
    processingTimeMS?: number | undefined;
};

/**
 * - `default`: perform a search query - `facet` [searches for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
 */
type SearchTypeFacet = 'facet';

type SearchForFacetsOptions = {
    /**
     * Facet name.
     */
    facet: string;
    /**
     * Index name (case-sensitive).
     */
    indexName: string;
    /**
     * Text to search inside the facet\'s values.
     */
    facetQuery?: string | undefined;
    /**
     * Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     */
    maxFacetHits?: number | undefined;
    type: SearchTypeFacet;
};

type SearchParamsQuery = {
    /**
     * Search query.
     */
    query?: string | undefined;
};

type BaseSearchParams = SearchParamsQuery & BaseSearchParamsWithoutQuery;

/**
 * Each parameter value, including the `query` must not be larger than 512 bytes.
 */
type SearchParamsObject = BaseSearchParams & IndexSettingsAsSearchParams;

/**
 * Search parameters as query string.
 */
type SearchParamsString = {
    /**
     * Search parameters as a URL-encoded query string.
     */
    params?: string | undefined;
};

type SearchParams = SearchParamsString | SearchParamsObject;

type SearchForFacets = SearchParams & SearchForFacetsOptions;

/**
 * - `default`: perform a search query - `facet` [searches for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
 */
type SearchTypeDefault = 'default';

type SearchForHitsOptions = {
    /**
     * Index name (case-sensitive).
     */
    indexName: string;
    type?: SearchTypeDefault | undefined;
} & {
    facet?: never | undefined;
    maxFacetHits?: never | undefined;
    facetQuery?: never | undefined;
};

type SearchForHits = SearchParams & SearchForHitsOptions;

type SearchQuery = SearchForHits | SearchForFacets;

/**
 * Strategy for multiple search queries:  - `none`. Run all queries. - `stopIfEnoughMatches`. Run the queries one by one, stopping as soon as a query matches at least the `hitsPerPage` number of results.
 */
type SearchStrategy = 'none' | 'stopIfEnoughMatches';

type SearchMethodParams = {
    requests: Array<SearchQuery>;
    strategy?: SearchStrategy | undefined;
};

type SearchPagination = {
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of results (hits).
     */
    nbHits?: number | undefined;
    /**
     * Number of pages of results.
     */
    nbPages?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};

type SearchResponse<T = Record<string, unknown>> = BaseSearchResponse & SearchPagination & SearchHits<T>;

type SearchResult<T = Record<string, unknown>> = SearchResponse<T> | SearchForFacetValuesResponse;

type SearchResponses<T = Record<string, unknown>> = {
    results: SearchResult<T>[];
};

type SearchRulesResponse = {
    /**
     * Rules that matched the search criteria.
     */
    hits: Array<Rule>;
    /**
     * Number of rules that matched the search criteria.
     */
    nbHits: number;
    /**
     * Current page.
     */
    page: number;
    /**
     * Number of pages.
     */
    nbPages: number;
};

/**
 * Synonym type.
 */
type SynonymType = 'synonym' | 'onewaysynonym' | 'altcorrection1' | 'altcorrection2' | 'placeholder' | 'oneWaySynonym' | 'altCorrection1' | 'altCorrection2';

/**
 * Synonym object.
 */
type SynonymHit = {
    /**
     * Unique identifier of a synonym object.
     */
    objectID: string;
    type: SynonymType;
    /**
     * Words or phrases considered equivalent.
     */
    synonyms?: Array<string> | undefined;
    /**
     * Word or phrase to appear in query strings (for [`onewaysynonym`s](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/one-way-synonyms/)).
     */
    input?: string | undefined;
    /**
     * Word or phrase to appear in query strings (for [`altcorrection1` and `altcorrection2`](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-alternative-corrections/)).
     */
    word?: string | undefined;
    /**
     * Words to be matched in records.
     */
    corrections?: Array<string> | undefined;
    /**
     * [Placeholder token](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-placeholders/) to be put inside records.
     */
    placeholder?: string | undefined;
    /**
     * Query words that will match the [placeholder token](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-placeholders/).
     */
    replacements?: Array<string> | undefined;
};

type SearchSynonymsResponse = Record<string, any> & {
    /**
     * Matching synonyms.
     */
    hits: Array<SynonymHit>;
    /**
     * Number of results (hits).
     */
    nbHits: number;
};

/**
 * OK
 */
type SearchUserIdsParams = {
    /**
     * Query to search. The search is a prefix search with [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/) enabled. An empty query will retrieve all users.
     */
    query: string;
    /**
     * Cluster name.
     */
    clusterName?: string | undefined;
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};

type UserHighlightResult = {
    userID: HighlightResult;
    clusterName: HighlightResult;
};

type UserHit = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    userID: string;
    /**
     * Cluster name.
     */
    clusterName: string;
    /**
     * Number of records in the cluster.
     */
    nbRecords: number;
    /**
     * Data size taken by all the users assigned to the cluster.
     */
    dataSize: number;
    /**
     * userID of the requested user. Same as userID.
     */
    objectID: string;
    _highlightResult: UserHighlightResult;
};

/**
 * userIDs data.
 */
type SearchUserIdsResponse = {
    /**
     * User objects that match the query.
     */
    hits: Array<UserHit>;
    /**
     * Number of results (hits).
     */
    nbHits: number;
    /**
     * Page of search results to retrieve.
     */
    page: number;
    /**
     * Maximum number of hits per page.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    hitsPerPage: number;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
};

type BaseIndexSettings = {
    /**
     * Attributes used for [faceting](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/).  Facets are attributes that let you categorize search results. They can be used for filtering search results. By default, no attribute is used for faceting. Attribute names are case-sensitive.  **Modifiers**  - `filterOnly(\"ATTRIBUTE\")`.   Allows the attribute to be used as a filter but doesn\'t evaluate the facet values.  - `searchable(\"ATTRIBUTE\")`.   Allows searching for facet values.  - `afterDistinct(\"ATTRIBUTE\")`.   Evaluates the facet count _after_ deduplication with `distinct`.   This ensures accurate facet counts.   You can apply this modifier to searchable facets: `afterDistinct(searchable(ATTRIBUTE))`.
     */
    attributesForFaceting?: Array<string> | undefined;
    /**
     * Creates [replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/).  Replicas are copies of a primary index with the same records but different settings, synonyms, or rules. If you want to offer a different ranking or sorting of your search results, you\'ll use replica indices. All index operations on a primary index are automatically forwarded to its replicas. To add a replica index, you must provide the complete set of replicas to this parameter. If you omit a replica from this list, the replica turns into a regular, standalone index that will no longer be synced with the primary index.  **Modifier**  - `virtual(\"REPLICA\")`.   Create a virtual replica,   Virtual replicas don\'t increase the number of records and are optimized for [Relevant sorting](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/relevant-sort/).
     */
    replicas?: Array<string> | undefined;
    /**
     * Maximum number of search results that can be obtained through pagination.  Higher pagination limits might slow down your search. For pagination limits above 1,000, the sorting of results beyond the 1,000th hit can\'t be guaranteed.
     */
    paginationLimitedTo?: number | undefined;
    /**
     * Attributes that can\'t be retrieved at query time.  This can be useful if you want to use an attribute for ranking or to [restrict access](https://www.algolia.com/doc/guides/security/api-keys/how-to/user-restricted-access-to-data/), but don\'t want to include it in the search results. Attribute names are case-sensitive.
     */
    unretrievableAttributes?: Array<string> | undefined;
    /**
     * Creates a list of [words which require exact matches](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#turn-off-typo-tolerance-for-certain-words). This also turns off [word splitting and concatenation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/splitting-and-concatenation/) for the specified words.
     */
    disableTypoToleranceOnWords?: Array<string> | undefined;
    /**
     * Attributes, for which you want to support [Japanese transliteration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#japanese-transliteration-and-type-ahead).  Transliteration supports searching in any of the Japanese writing systems. To support transliteration, you must set the indexing language to Japanese. Attribute names are case-sensitive.
     */
    attributesToTransliterate?: Array<string> | undefined;
    /**
     * Attributes for which to split [camel case](https://wikipedia.org/wiki/Camel_case) words. Attribute names are case-sensitive.
     */
    camelCaseAttributes?: Array<string> | undefined;
    /**
     * Searchable attributes to which Algolia should apply [word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/how-to/customize-segmentation/) (decompounding). Attribute names are case-sensitive.  Compound words are formed by combining two or more individual words, and are particularly prevalent in Germanic languages—for example, \"firefighter\". With decompounding, the individual components are indexed separately.  You can specify different lists for different languages. Decompounding is supported for these languages: Dutch (`nl`), German (`de`), Finnish (`fi`), Danish (`da`), Swedish (`sv`), and Norwegian (`no`). Decompounding doesn\'t work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won\'t be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     */
    decompoundedAttributes?: Record<string, unknown> | undefined;
    /**
     * Languages for language-specific processing steps, such as word detection and dictionary settings.  **You should always specify an indexing language.** If you don\'t specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     */
    indexLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Searchable attributes for which you want to turn off [prefix matching](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/#adjusting-prefix-search). Attribute names are case-sensitive.
     */
    disablePrefixOnAttributes?: Array<string> | undefined;
    /**
     * Whether arrays with exclusively non-negative integers should be compressed for better performance. If true, the compressed arrays may be reordered.
     */
    allowCompressionOfIntegerArray?: boolean | undefined;
    /**
     * Numeric attributes that can be used as [numerical filters](https://www.algolia.com/doc/guides/managing-results/rules/detecting-intent/how-to/applying-a-custom-filter-for-a-specific-query/#numerical-filters). Attribute names are case-sensitive.  By default, all numeric attributes are available as numerical filters. For faster indexing, reduce the number of numeric attributes.  To turn off filtering for all numeric attributes, specify an attribute that doesn\'t exist in your index, such as `NO_NUMERIC_FILTERING`.  **Modifier**  - `equalOnly(\"ATTRIBUTE\")`.   Support only filtering based on equality comparisons `=` and `!=`.
     */
    numericAttributesForFiltering?: Array<string> | undefined;
    /**
     * Control which non-alphanumeric characters are indexed.  By default, Algolia ignores [non-alphanumeric characters](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/#handling-non-alphanumeric-characters) like hyphen (`-`), plus (`+`), and parentheses (`(`,`)`). To include such characters, define them with `separatorsToIndex`.  Separators are all non-letter characters except spaces and currency characters, such as $€£¥.  With `separatorsToIndex`, Algolia treats separator characters as separate words. For example, in a search for \"Disney+\", Algolia considers \"Disney\" and \"+\" as two separate words.
     */
    separatorsToIndex?: string | undefined;
    /**
     * Attributes used for searching. Attribute names are case-sensitive.  By default, all attributes are searchable and the [Attribute](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#attribute) ranking criterion is turned off. With a non-empty list, Algolia only returns results with matches in the selected attributes. In addition, the Attribute ranking criterion is turned on: matches in attributes that are higher in the list of `searchableAttributes` rank first. To make matches in two attributes rank equally, include them in a comma-separated string, such as `\"title,alternate_title\"`. Attributes with the same priority are always unordered.  For more information, see [Searchable attributes](https://www.algolia.com/doc/guides/sending-and-managing-data/prepare-your-data/how-to/setting-searchable-attributes/).  **Modifier**  - `unordered(\"ATTRIBUTE\")`.   Ignore the position of a match within the attribute.  Without a modifier, matches at the beginning of an attribute rank higher than matches at the end.
     */
    searchableAttributes?: Array<string> | undefined;
    /**
     * An object with custom data.  You can store up to 32kB as custom data.
     */
    userData?: any | null | undefined;
    /**
     * Characters and their normalized replacements. This overrides Algolia\'s default [normalization](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/).
     */
    customNormalization?: {
        [key: string]: {
            [key: string]: string;
        };
    } | undefined;
    /**
     * Attribute that should be used to establish groups of results. Attribute names are case-sensitive.  All records with the same value for this attribute are considered a group. You can combine `attributeForDistinct` with the `distinct` search parameter to control how many items per group are included in the search results.  If you want to use the same attribute also for faceting, use the `afterDistinct` modifier of the `attributesForFaceting` setting. This applies faceting _after_ deduplication, which will result in accurate facet counts.
     */
    attributeForDistinct?: string | undefined;
    /**
     * Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     */
    maxFacetHits?: number | undefined;
    /**
     * Characters for which diacritics should be preserved.  By default, Algolia removes diacritics from letters. For example, `é` becomes `e`. If this causes issues in your search, you can specify characters that should keep their diacritics.
     */
    keepDiacriticsOnCharacters?: string | undefined;
    /**
     * Attributes to use as [custom ranking](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/). Attribute names are case-sensitive.  The custom ranking attributes decide which items are shown first if the other ranking criteria are equal.  Records with missing values for your selected custom ranking attributes are always sorted last. Boolean attributes are sorted based on their alphabetical order.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order.  - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  If you use two or more custom ranking attributes, [reduce the precision](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/how-to/controlling-custom-ranking-metrics-precision/) of your first attributes, or the other attributes will never be applied.
     */
    customRanking?: Array<string> | undefined;
};

/**
 * Index settings.
 */
type IndexSettings = BaseIndexSettings & IndexSettingsAsSearchParams;

type WithPrimary = {
    /**
     * Replica indices only: the name of the primary index for this replica.
     */
    primary?: string | undefined;
};

type SettingsResponse = IndexSettings & WithPrimary;

/**
 * Source.
 */
type Source = {
    /**
     * IP address range of the source.
     */
    source: string;
    /**
     * Source description.
     */
    description?: string | undefined;
};

type UpdateApiKeyResponse = {
    /**
     * API key.
     */
    key: string;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
};

/**
 * Response, taskID, unique object identifier, and an update timestamp.
 */
type UpdatedAtWithObjectIdResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID?: number | undefined;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt?: string | undefined;
    /**
     * Unique record identifier.
     */
    objectID?: string | undefined;
};

type ApiKeyOperation = 'add' | 'delete' | 'update';

/**
 * Assign userID parameters.
 */
type AssignUserIdParams = {
    /**
     * Cluster name.
     */
    cluster: string;
};

/**
 * Assign userID parameters.
 */
type BatchAssignUserIdsParams = {
    /**
     * Cluster name.
     */
    cluster: string;
    /**
     * User IDs to assign.
     */
    users: Array<string>;
};

/**
 * Actions to perform.
 */
type DictionaryAction = 'addEntry' | 'deleteEntry';

type BatchDictionaryEntriesRequest = {
    action: DictionaryAction;
    body: DictionaryEntry;
};

/**
 * Request body for updating dictionary entries.
 */
type BatchDictionaryEntriesParams = {
    /**
     * Whether to replace all custom entries in the dictionary with the ones sent with this request.
     */
    clearExistingDictionaryEntries?: boolean | undefined;
    /**
     * List of additions and deletions to your dictionaries.
     */
    requests: Array<BatchDictionaryEntriesRequest>;
};

type BatchRequest = {
    action: Action;
    /**
     * Operation arguments (varies with specified `action`).
     */
    body: Record<string, unknown>;
};

/**
 * Batch parameters.
 */
type BatchWriteParams = {
    requests: Array<BatchRequest>;
};

type BrowseParamsObject = SearchParamsObject & Cursor;

type BrowseParams = SearchParamsString | BrowseParamsObject;

type DeleteByParams = {
    facetFilters?: FacetFilters | undefined;
    /**
     * Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can\'t use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can\'t combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     */
    filters?: string | undefined;
    numericFilters?: NumericFilters | undefined;
    tagFilters?: TagFilters | undefined;
    /**
     * Coordinates for the center of a circle, expressed as a comma-separated string of latitude and longitude.  Only records included within a circle around this central location are included in the results. The radius of the circle is determined by the `aroundRadius` and `minimumAroundRadius` settings. This parameter is ignored if you also specify `insidePolygon` or `insideBoundingBox`.
     */
    aroundLatLng?: string | undefined;
    aroundRadius?: AroundRadius | undefined;
    insideBoundingBox?: InsideBoundingBox | null | undefined;
    /**
     * Coordinates of a polygon in which to search.  Polygons are defined by 3 to 10,000 points. Each point is represented by its latitude and longitude. Provide multiple polygons as nested arrays. For more information, see [filtering inside polygons](https://www.algolia.com/doc/guides/managing-results/refine-results/geolocation/#filtering-inside-rectangular-or-polygonal-areas). This parameter is ignored if you also specify `insideBoundingBox`.
     */
    insidePolygon?: Array<Array<number>> | undefined;
};

type DictionaryType = 'plurals' | 'stopwords' | 'compounds';

type LogType = 'all' | 'query' | 'build' | 'error';

/**
 * Operation to perform on the index.
 */
type OperationType = 'move' | 'copy';

type ScopeType = 'settings' | 'synonyms' | 'rules';

type OperationIndexParams = {
    operation: OperationType;
    /**
     * Index name (case-sensitive).
     */
    destination: string;
    /**
     * **Only for copying.**  If you specify a scope, only the selected scopes are copied. Records and the other scopes are left unchanged. If you omit the `scope` parameter, everything is copied: records, settings, synonyms, and rules.
     */
    scope?: Array<ScopeType> | undefined;
};

/**
 * Search parameter.
 */
type SearchDictionaryEntriesParams = {
    /**
     * Search query.
     */
    query: string;
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
    language?: SupportedLanguage | undefined;
};

type SearchForFacetValuesRequest = {
    /**
     * Search parameters as a URL-encoded query string.
     */
    params?: string | undefined;
    /**
     * Text to search inside the facet\'s values.
     */
    facetQuery?: string | undefined;
    /**
     * Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     */
    maxFacetHits?: number | undefined;
};

/**
 * Rules search parameters.
 */
type SearchRulesParams = {
    /**
     * Search query for rules.
     */
    query?: string | undefined;
    anchoring?: Anchoring | undefined;
    /**
     * Only return rules that match the context (exact match).
     */
    context?: string | undefined;
    /**
     * Requested page of the API response.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    page?: number | undefined;
    /**
     * Maximum number of hits per page.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    hitsPerPage?: number | undefined;
    /**
     * If `true`, return only enabled rules. If `false`, return only inactive rules. By default, _all_ rules are returned.
     */
    enabled?: boolean | null | undefined;
};

type SearchSynonymsParams = {
    /**
     * Search query.
     */
    query?: string | undefined;
    type?: SynonymType | undefined;
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};

type SecuredApiKeyRestrictions = {
    searchParams?: SearchParamsObject | undefined;
    /**
     * Filters that apply to every search made with the secured API key. Extra filters added at search time will be combined with `AND`. For example, if you set `group:admin` as fixed filter on your generated API key, and add `groups:visitors` to the search query, the complete set of filters will be `group:admin AND groups:visitors`.
     */
    filters?: string | undefined;
    /**
     * Timestamp when the secured API key expires, measured in seconds since the Unix epoch.
     */
    validUntil?: number | undefined;
    /**
     * Index names or patterns that this API key can access. By default, an API key can access all indices in the same application.  You can use leading and trailing wildcard characters (`*`):  - `dev_*` matches all indices starting with \"dev_\". - `*_dev` matches all indices ending with \"_dev\". - `*_products_*` matches all indices containing \"_products_\".
     */
    restrictIndices?: Array<string> | undefined;
    /**
     * IP network that are allowed to use this key.  You can only add a single source, but you can provide a range of IP addresses. Use this to protect against API key leaking and reuse.
     */
    restrictSources?: string | undefined;
    /**
     * Pseudonymous user identifier to restrict usage of this API key to specific users.  By default, rate limits are set based on IP addresses. This can be an issue if many users search from the same IP address. To avoid this, add a user token to each generated API key.
     */
    userToken?: string | undefined;
};

/**
 * Properties for the `addOrUpdateObject` method.
 */
type AddOrUpdateObjectProps<T extends object> = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * The record. A schemaless object with attributes that are useful in the context of search and discovery.
     */
    body: T;
};
/**
 * Properties for the `assignUserId` method.
 */
type AssignUserIdProps = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    xAlgoliaUserID: string;
    assignUserIdParams: AssignUserIdParams;
};
/**
 * Properties for the `batch` method.
 */
type BatchProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    batchWriteParams: BatchWriteParams;
};
/**
 * Properties for the `batchAssignUserIds` method.
 */
type BatchAssignUserIdsProps = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    xAlgoliaUserID: string;
    batchAssignUserIdsParams: BatchAssignUserIdsParams;
};
/**
 * Properties for the `batchDictionaryEntries` method.
 */
type BatchDictionaryEntriesProps = {
    /**
     * Dictionary type in which to search.
     */
    dictionaryName: DictionaryType;
    batchDictionaryEntriesParams: BatchDictionaryEntriesParams;
};
/**
 * Properties for the `browse` method.
 */
type BrowseProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    browseParams?: BrowseParams | undefined;
};
/**
 * Properties for the `clearObjects` method.
 */
type ClearObjectsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
};
/**
 * Properties for the `clearRules` method.
 */
type ClearRulesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `clearSynonyms` method.
 */
type ClearSynonymsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `deleteApiKey` method.
 */
type DeleteApiKeyProps = {
    /**
     * API key.
     */
    key: string;
};
/**
 * Properties for the `deleteBy` method.
 */
type DeleteByProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    deleteByParams: DeleteByParams;
};
/**
 * Properties for the `deleteIndex` method.
 */
type DeleteIndexProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
};
/**
 * Properties for the `deleteObject` method.
 */
type DeleteObjectProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique record identifier.
     */
    objectID: string;
};
/**
 * Properties for the `deleteRule` method.
 */
type DeleteRuleProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a rule object.
     */
    objectID: string;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `deleteSource` method.
 */
type DeleteSourceProps = {
    /**
     * IP address range of the source.
     */
    source: string;
};
/**
 * Properties for the `deleteSynonym` method.
 */
type DeleteSynonymProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a synonym object.
     */
    objectID: string;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `getApiKey` method.
 */
type GetApiKeyProps = {
    /**
     * API key.
     */
    key: string;
};
/**
 * Properties for the `getAppTask` method.
 */
type GetAppTaskProps = {
    /**
     * Unique task identifier.
     */
    taskID: number;
};
/**
 * Properties for the `getLogs` method.
 */
type GetLogsProps = {
    /**
     * First log entry to retrieve. The most recent entries are listed first.
     */
    offset?: number | undefined;
    /**
     * Maximum number of entries to retrieve.
     */
    length?: number | undefined;
    /**
     * Index for which to retrieve log entries. By default, log entries are retrieved for all indices.
     */
    indexName?: string | undefined;
    /**
     * Type of log entries to retrieve. By default, all log entries are retrieved.
     */
    type?: LogType | undefined;
};
/**
 * Properties for the `getObject` method.
 */
type GetObjectProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * Attributes to include with the records in the response. This is useful to reduce the size of the API response. By default, all retrievable attributes are returned.  `objectID` is always retrieved.  Attributes included in `unretrievableAttributes` won\'t be retrieved unless the request is authenticated with the admin API key.
     */
    attributesToRetrieve?: Array<string> | undefined;
};
/**
 * Properties for the `getRule` method.
 */
type GetRuleProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a rule object.
     */
    objectID: string;
};
/**
 * Properties for the `getSettings` method.
 */
type GetSettingsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
};
/**
 * Properties for the `getSynonym` method.
 */
type GetSynonymProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a synonym object.
     */
    objectID: string;
};
/**
 * Properties for the `getTask` method.
 */
type GetTaskProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique task identifier.
     */
    taskID: number;
};
/**
 * Properties for the `getUserId` method.
 */
type GetUserIdProps = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    userID: string;
};
/**
 * Properties for the `hasPendingMappings` method.
 */
type HasPendingMappingsProps = {
    /**
     * Whether to include the cluster\'s pending mapping state in the response.
     */
    getClusters?: boolean | undefined;
};
/**
 * Properties for the `listIndices` method.
 */
type ListIndicesProps = {
    /**
     * Requested page of the API response. If `null`, the API response is not paginated.
     */
    page?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};
/**
 * Properties for the `listUserIds` method.
 */
type ListUserIdsProps = {
    /**
     * Requested page of the API response. If `null`, the API response is not paginated.
     */
    page?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};
/**
 * Properties for the `operationIndex` method.
 */
type OperationIndexProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    operationIndexParams: OperationIndexParams;
};
/**
 * Properties for the `partialUpdateObject` method.
 */
type PartialUpdateObjectProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * Attributes with their values.
     */
    attributesToUpdate: Record<string, unknown>;
    /**
     * Whether to create a new record if it doesn\'t exist.
     */
    createIfNotExists?: boolean | undefined;
};
/**
 * Properties for the `removeUserId` method.
 */
type RemoveUserIdProps = {
    /**
     * Unique identifier of the user who makes the search request.
     */
    userID: string;
};
/**
 * Properties for the `replaceSources` method.
 */
type ReplaceSourcesProps = {
    /**
     * Allowed sources.
     */
    source: Array<Source>;
};
/**
 * Properties for the `restoreApiKey` method.
 */
type RestoreApiKeyProps = {
    /**
     * API key.
     */
    key: string;
};
/**
 * Properties for the `saveObject` method.
 */
type SaveObjectProps<T extends object> = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * The record. A schemaless object with attributes that are useful in the context of search and discovery.
     */
    body: T;
};
/**
 * Properties for the `saveRule` method.
 */
type SaveRuleProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a rule object.
     */
    objectID: string;
    rule: Rule;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `saveRules` method.
 */
type SaveRulesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    rules: Array<Rule>;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
    /**
     * Whether existing rules should be deleted before adding this batch.
     */
    clearExistingRules?: boolean | undefined;
};
/**
 * Properties for the `saveSynonym` method.
 */
type SaveSynonymProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Unique identifier of a synonym object.
     */
    objectID: string;
    synonymHit: SynonymHit;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `saveSynonyms` method.
 */
type SaveSynonymsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    synonymHit: Array<SynonymHit>;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
    /**
     * Whether to replace all synonyms in the index with the ones sent with this request.
     */
    replaceExistingSynonyms?: boolean | undefined;
};
/**
 * In v4, the search parameters are wrapped in a `params` parameter.
 *
 * @deprecated The `search` method now accepts flat `searchParams` at the root of the method.
 */
type LegacySearchParams = {
    params?: SearchParamsObject | undefined;
};
/**
 * In v4, the search parameters are wrapped in a `params` parameter.
 *
 * @deprecated The `search` method now accepts flat `searchParams` at the root of the method.
 */
type LegacySearchForFacets = LegacySearchParams & SearchForFacetsOptions;
/**
 * In v4, the search parameters are wrapped in a `params` parameter.
 *
 * @deprecated The `search` method now accepts flat `searchParams` at the root of the method.
 */
type LegacySearchForHits = LegacySearchParams & SearchForHitsOptions;
type LegacySearchQuery = LegacySearchForFacets | LegacySearchForHits;
/**
 * Search method signature compatible with the `algoliasearch` v4 package. When using this signature, extra computation will be required to make it match the new signature.
 *
 * @deprecated This signature will be removed from the next major version, we recommend using the `SearchMethodParams` type for performances and future proof reasons.
 */
type LegacySearchMethodProps = LegacySearchQuery[];
/**
 * Properties for the `searchDictionaryEntries` method.
 */
type SearchDictionaryEntriesProps = {
    /**
     * Dictionary type in which to search.
     */
    dictionaryName: DictionaryType;
    searchDictionaryEntriesParams: SearchDictionaryEntriesParams;
};
/**
 * Properties for the `searchForFacetValues` method.
 */
type SearchForFacetValuesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Facet attribute in which to search for values.  This attribute must be included in the `attributesForFaceting` index setting with the `searchable()` modifier.
     */
    facetName: string;
    searchForFacetValuesRequest?: SearchForFacetValuesRequest | undefined;
};
/**
 * Properties for the `searchRules` method.
 */
type SearchRulesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    searchRulesParams?: SearchRulesParams | undefined;
};
/**
 * Properties for the `searchSingleIndex` method.
 */
type SearchSingleIndexProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    searchParams?: SearchParams | undefined;
};
/**
 * Properties for the `searchSynonyms` method.
 */
type SearchSynonymsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * Body of the `searchSynonyms` operation.
     */
    searchSynonymsParams?: SearchSynonymsParams | undefined;
};
/**
 * Properties for the `setSettings` method.
 */
type SetSettingsProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    indexSettings: IndexSettings;
    /**
     * Whether changes are applied to replica indices.
     */
    forwardToReplicas?: boolean | undefined;
};
/**
 * Properties for the `updateApiKey` method.
 */
type UpdateApiKeyProps = {
    /**
     * API key.
     */
    key: string;
    apiKey: ApiKey;
};
/**
 * The `browseObjects`, `browseRules`, `browseSynonyms` options.
 */
type BrowseOptions<T> = Partial<Pick<CreateIterablePromise<T>, 'validate'>> & Required<Pick<CreateIterablePromise<T>, 'aggregator'>>;
type WaitForOptions = Partial<{
    /**
     * The maximum number of retries. 50 by default.
     */
    maxRetries: number;
    /**
     * The function to decide how long to wait between retries.
     */
    timeout: (retryCount: number) => number;
}>;
type WaitForAppTaskOptions = WaitForOptions & {
    /**
     * The `taskID` returned by the method response.
     */
    taskID: number;
};
type WaitForTaskOptions = WaitForAppTaskOptions & {
    /**
     * The `indexName` where the operation was performed.
     */
    indexName: string;
};
type WaitForApiKeyOptions = WaitForOptions & {
    /**
     * The API Key.
     */
    key: string;
} & ({
    /**
     * The operation that has been performed, used to compute the stop condition.
     */
    operation: Extract<ApiKeyOperation, 'add' | 'delete'>;
    apiKey?: never;
} | {
    /**
     * The operation that has been performed, used to compute the stop condition.
     */
    operation: Extract<ApiKeyOperation, 'update'>;
    /**
     * The updated fields, used to compute the stop condition.
     */
    apiKey: Partial<ApiKey>;
});
type GenerateSecuredApiKeyOptions = {
    /**
     * The base API key from which to generate the new secured one.
     */
    parentApiKey: string;
    /**
     * A set of properties defining the restrictions of the secured API key.
     */
    restrictions?: SecuredApiKeyRestrictions | undefined;
};
type GetSecuredApiKeyRemainingValidityOptions = {
    /**
     * The secured API key generated with the `generateSecuredApiKey` method.
     */
    securedApiKey: string;
};
type SearchClientNodeHelpers = {
    accountCopyIndex: (opts: AccountCopyIndexOptions) => Promise<void>;
    generateSecuredApiKey: (opts: GenerateSecuredApiKeyOptions) => string;
    getSecuredApiKeyRemainingValidity: (opts: GetSecuredApiKeyRemainingValidityOptions) => number;
};
type DeleteObjectsOptions = Pick<ChunkedBatchOptions, 'indexName' | 'waitForTasks' | 'batchSize'> & {
    /**
     * The objectIDs to delete.
     */
    objectIDs: string[];
};
type PartialUpdateObjectsOptions = Pick<ChunkedBatchOptions, 'indexName' | 'objects' | 'waitForTasks' | 'batchSize'> & {
    /**
     *To be provided if non-existing objects are passed, otherwise, the call will fail.
     */
    createIfNotExists?: boolean | undefined;
};
type SaveObjectsOptions = Pick<ChunkedBatchOptions, 'indexName' | 'objects' | 'waitForTasks' | 'batchSize'>;
type ChunkedBatchOptions = ReplaceAllObjectsOptions & {
    /**
     * The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.
     */
    action?: Action | undefined;
    /**
     * Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.
     */
    waitForTasks?: boolean | undefined;
};
type ReplaceAllObjectsOptions = {
    /**
     * The `indexName` to replace `objects` in.
     */
    indexName: string;
    /**
     * The array of `objects` to store in the given Algolia `indexName`.
     */
    objects: Array<Record<string, unknown>>;
    /**
     * The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.
     */
    batchSize?: number | undefined;
    /**
     * The `scopes` to keep from the index. Defaults to ['settings', 'rules', 'synonyms'].
     */
    scopes?: Array<ScopeType> | undefined;
};
type AccountCopyIndexOptions = {
    /**
     * The name of the index to copy to the `destinationClient`.
     */
    sourceIndexName: string;
    /**
     * The application ID to write the index to.
     */
    destinationAppID: string;
    /**
     * The API Key of the `destinationAppID` to write the index to, must have write ACLs.
     */
    destinationApiKey: string;
    /**
     * The name of the index to write the copy in.
     */
    destinationIndexName: string;
};

declare const apiClientVersion = "5.30.0";
declare function createSearchClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, ...options }: CreateClientOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string | undefined): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * Helper: Wait for a task to be published (completed) for a given `indexName` and `taskID`.
     *
     * @summary Helper method that waits for a task to be published (completed).
     * @param waitForTaskOptions - The `waitForTaskOptions` object.
     * @param waitForTaskOptions.indexName - The `indexName` where the operation was performed.
     * @param waitForTaskOptions.taskID - The `taskID` returned in the method response.
     * @param waitForTaskOptions.maxRetries - The maximum number of retries. 50 by default.
     * @param waitForTaskOptions.timeout - The function to decide how long to wait between retries.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.
     */
    waitForTask({ indexName, taskID, maxRetries, timeout, }: WaitForTaskOptions, requestOptions?: RequestOptions | undefined): Promise<GetTaskResponse>;
    /**
     * Helper: Wait for an application-level task to complete for a given `taskID`.
     *
     * @summary Helper method that waits for a task to be published (completed).
     * @param waitForAppTaskOptions - The `waitForTaskOptions` object.
     * @param waitForAppTaskOptions.taskID - The `taskID` returned in the method response.
     * @param waitForAppTaskOptions.maxRetries - The maximum number of retries. 50 by default.
     * @param waitForAppTaskOptions.timeout - The function to decide how long to wait between retries.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.
     */
    waitForAppTask({ taskID, maxRetries, timeout, }: WaitForAppTaskOptions, requestOptions?: RequestOptions | undefined): Promise<GetTaskResponse>;
    /**
     * Helper: Wait for an API key to be added, updated or deleted based on a given `operation`.
     *
     * @summary Helper method that waits for an API key task to be processed.
     * @param waitForApiKeyOptions - The `waitForApiKeyOptions` object.
     * @param waitForApiKeyOptions.operation - The `operation` that was done on a `key`.
     * @param waitForApiKeyOptions.key - The `key` that has been added, deleted or updated.
     * @param waitForApiKeyOptions.apiKey - Necessary to know if an `update` operation has been processed, compare fields of the response with it.
     * @param waitForApiKeyOptions.maxRetries - The maximum number of retries. 50 by default.
     * @param waitForApiKeyOptions.timeout - The function to decide how long to wait between retries.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getApikey` method and merged with the transporter requestOptions.
     */
    waitForApiKey({ operation, key, apiKey, maxRetries, timeout, }: WaitForApiKeyOptions, requestOptions?: RequestOptions | undefined): Promise<GetApiKeyResponse | undefined>;
    /**
     * Helper: Iterate on the `browse` method of the client to allow aggregating objects of an index.
     *
     * @summary Helper method that iterates on the `browse` method.
     * @param browseObjects - The `browseObjects` object.
     * @param browseObjects.indexName - The index in which to perform the request.
     * @param browseObjects.browseParams - The `browse` parameters.
     * @param browseObjects.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is no `cursor` in the response.
     * @param browseObjects.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `browse` method and merged with the transporter requestOptions.
     */
    browseObjects<T>({ indexName, browseParams, ...browseObjectsOptions }: BrowseOptions<BrowseResponse<T>> & BrowseProps, requestOptions?: RequestOptions | undefined): Promise<BrowseResponse<T>>;
    /**
     * Helper: Iterate on the `searchRules` method of the client to allow aggregating rules of an index.
     *
     * @summary Helper method that iterates on the `searchRules` method.
     * @param browseRules - The `browseRules` object.
     * @param browseRules.indexName - The index in which to perform the request.
     * @param browseRules.searchRulesParams - The `searchRules` method parameters.
     * @param browseRules.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).
     * @param browseRules.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchRules` method and merged with the transporter requestOptions.
     */
    browseRules({ indexName, searchRulesParams, ...browseRulesOptions }: BrowseOptions<SearchRulesResponse> & SearchRulesProps, requestOptions?: RequestOptions | undefined): Promise<SearchRulesResponse>;
    /**
     * Helper: Iterate on the `searchSynonyms` method of the client to allow aggregating rules of an index.
     *
     * @summary Helper method that iterates on the `searchSynonyms` method.
     * @param browseSynonyms - The `browseSynonyms` object.
     * @param browseSynonyms.indexName - The index in which to perform the request.
     * @param browseSynonyms.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).
     * @param browseSynonyms.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.
     * @param browseSynonyms.searchSynonymsParams - The `searchSynonyms` method parameters.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchSynonyms` method and merged with the transporter requestOptions.
     */
    browseSynonyms({ indexName, searchSynonymsParams, ...browseSynonymsOptions }: BrowseOptions<SearchSynonymsResponse> & SearchSynonymsProps, requestOptions?: RequestOptions | undefined): Promise<SearchSynonymsResponse>;
    /**
     * Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.
     *
     * @summary Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.
     * @param chunkedBatch - The `chunkedBatch` object.
     * @param chunkedBatch.indexName - The `indexName` to replace `objects` in.
     * @param chunkedBatch.objects - The array of `objects` to store in the given Algolia `indexName`.
     * @param chunkedBatch.action - The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.
     * @param chunkedBatch.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.
     * @param chunkedBatch.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.
     */
    chunkedBatch({ indexName, objects, action, waitForTasks, batchSize }: ChunkedBatchOptions, requestOptions?: RequestOptions): Promise<Array<BatchResponse>>;
    /**
     * Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.
     *
     * @summary Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.
     * @param saveObjects - The `saveObjects` object.
     * @param saveObjects.indexName - The `indexName` to save `objects` in.
     * @param saveObjects.objects - The array of `objects` to store in the given Algolia `indexName`.
     * @param saveObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.
     * @param saveObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.
     */
    saveObjects({ indexName, objects, waitForTasks, batchSize }: SaveObjectsOptions, requestOptions?: RequestOptions | undefined): Promise<BatchResponse[]>;
    /**
     * Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.
     *
     * @summary Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.
     * @param deleteObjects - The `deleteObjects` object.
     * @param deleteObjects.indexName - The `indexName` to delete `objectIDs` from.
     * @param deleteObjects.objectIDs - The objectIDs to delete.
     * @param deleteObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.
     * @param deleteObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.
     */
    deleteObjects({ indexName, objectIDs, waitForTasks, batchSize }: DeleteObjectsOptions, requestOptions?: RequestOptions | undefined): Promise<BatchResponse[]>;
    /**
     * Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.
     *
     * @summary Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.
     * @param partialUpdateObjects - The `partialUpdateObjects` object.
     * @param partialUpdateObjects.indexName - The `indexName` to update `objects` in.
     * @param partialUpdateObjects.objects - The array of `objects` to update in the given Algolia `indexName`.
     * @param partialUpdateObjects.createIfNotExists - To be provided if non-existing objects are passed, otherwise, the call will fail..
     * @param partialUpdateObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.
     * @param partialUpdateObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.
     */
    partialUpdateObjects({ indexName, objects, createIfNotExists, waitForTasks, batchSize }: PartialUpdateObjectsOptions, requestOptions?: RequestOptions | undefined): Promise<BatchResponse[]>;
    /**
     * Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.
     * See https://api-clients-automation.netlify.app/docs/add-new-api-client#5-helpers for implementation details.
     *
     * @summary Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.
     * @param replaceAllObjects - The `replaceAllObjects` object.
     * @param replaceAllObjects.indexName - The `indexName` to replace `objects` in.
     * @param replaceAllObjects.objects - The array of `objects` to store in the given Algolia `indexName`.
     * @param replaceAllObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `objects.length / batchSize`. Defaults to 1000.
     * @param replaceAllObjects.scopes - The `scopes` to keep from the index. Defaults to ['settings', 'rules', 'synonyms'].
     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch`, `operationIndex` and `getTask` method and merged with the transporter requestOptions.
     */
    replaceAllObjects({ indexName, objects, batchSize, scopes }: ReplaceAllObjectsOptions, requestOptions?: RequestOptions | undefined): Promise<ReplaceAllObjectsResponse>;
    indexExists({ indexName }: GetSettingsProps): Promise<boolean>;
    /**
     * Helper: calls the `search` method but with certainty that we will only request Algolia records (hits) and not facets.
     * Disclaimer: We don't assert that the parameters you pass to this method only contains `hits` requests to prevent impacting search performances, this helper is purely for typing purposes.
     *
     * @summary Search multiple indices for `hits`.
     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchForHits<T>(searchMethodParams: LegacySearchMethodProps | SearchMethodParams, requestOptions?: RequestOptions | undefined): Promise<{
        results: Array<SearchResponse<T>>;
    }>;
    /**
     * Helper: calls the `search` method but with certainty that we will only request Algolia facets and not records (hits).
     * Disclaimer: We don't assert that the parameters you pass to this method only contains `facets` requests to prevent impacting search performances, this helper is purely for typing purposes.
     *
     * @summary Search multiple indices for `facets`.
     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchForFacets(searchMethodParams: LegacySearchMethodProps | SearchMethodParams, requestOptions?: RequestOptions | undefined): Promise<{
        results: Array<SearchForFacetValuesResponse>;
    }>;
    /**
     * Creates a new API key with specific permissions and restrictions.
     *
     * Required API Key ACLs:
     *  - admin
     * @param apiKey - The apiKey object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    addApiKey(apiKey: ApiKey, requestOptions?: RequestOptions): Promise<AddApiKeyResponse>;
    /**
     * If a record with the specified object ID exists, the existing record is replaced. Otherwise, a new record is added to the index.  If you want to use auto-generated object IDs, use the [`saveObject` operation](#tag/Records/operation/saveObject). To update _some_ attributes of an existing record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject) instead. To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).
     *
     * Required API Key ACLs:
     *  - addObject
     * @param addOrUpdateObject - The addOrUpdateObject object.
     * @param addOrUpdateObject.indexName - Name of the index on which to perform the operation.
     * @param addOrUpdateObject.objectID - Unique record identifier.
     * @param addOrUpdateObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    addOrUpdateObject<T extends object>({ indexName, objectID, body }: AddOrUpdateObjectProps<T>, requestOptions?: RequestOptions): Promise<UpdatedAtWithObjectIdResponse>;
    /**
     * Adds a source to the list of allowed sources.
     *
     * Required API Key ACLs:
     *  - admin
     * @param source - Source to add.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    appendSource(source: Source, requestOptions?: RequestOptions): Promise<CreatedAtResponse>;
    /**
     * Assigns or moves a user ID to a cluster.  The time it takes to move a user is proportional to the amount of data linked to the user ID.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param assignUserId - The assignUserId object.
     * @param assignUserId.xAlgoliaUserID - Unique identifier of the user who makes the search request.
     * @param assignUserId.assignUserIdParams - The assignUserIdParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    assignUserId({ xAlgoliaUserID, assignUserIdParams }: AssignUserIdProps, requestOptions?: RequestOptions): Promise<CreatedAtResponse>;
    /**
     * Adds, updates, or deletes records in one index with a single API request.  Batching index updates reduces latency and increases data integrity.  - Actions are applied in the order they\'re specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     * @param batch - The batch object.
     * @param batch.indexName - Name of the index on which to perform the operation.
     * @param batch.batchWriteParams - The batchWriteParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    batch({ indexName, batchWriteParams }: BatchProps, requestOptions?: RequestOptions): Promise<BatchResponse>;
    /**
     * Assigns multiple user IDs to a cluster.  **You can\'t move users with this operation**.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param batchAssignUserIds - The batchAssignUserIds object.
     * @param batchAssignUserIds.xAlgoliaUserID - Unique identifier of the user who makes the search request.
     * @param batchAssignUserIds.batchAssignUserIdsParams - The batchAssignUserIdsParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    batchAssignUserIds({ xAlgoliaUserID, batchAssignUserIdsParams }: BatchAssignUserIdsProps, requestOptions?: RequestOptions): Promise<CreatedAtResponse>;
    /**
     * Adds or deletes multiple entries from your plurals, segmentation, or stop word dictionaries.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param batchDictionaryEntries - The batchDictionaryEntries object.
     * @param batchDictionaryEntries.dictionaryName - Dictionary type in which to search.
     * @param batchDictionaryEntries.batchDictionaryEntriesParams - The batchDictionaryEntriesParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    batchDictionaryEntries({ dictionaryName, batchDictionaryEntriesParams }: BatchDictionaryEntriesProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Retrieves records from an index, up to 1,000 per request.  While searching retrieves _hits_ (records augmented with attributes for highlighting and ranking details), browsing _just_ returns matching records. This can be useful if you want to export your indices.  - The Analytics API doesn\'t collect data when using `browse`. - Records are ranked by attributes and custom ranking. - There\'s no ranking for: typo-tolerance, number of matched words, proximity, geo distance.  Browse requests automatically apply these settings:  - `advancedSyntax`: `false` - `attributesToHighlight`: `[]` - `attributesToSnippet`: `[]` - `distinct`: `false` - `enablePersonalization`: `false` - `enableRules`: `false` - `facets`: `[]` - `getRankingInfo`: `false` - `ignorePlurals`: `false` - `optionalFilters`: `[]` - `typoTolerance`: `true` or `false` (`min` and `strict` evaluate to `true`)  If you send these parameters with your browse requests, they\'ll be ignored.
     *
     * Required API Key ACLs:
     *  - browse
     * @param browse - The browse object.
     * @param browse.indexName - Name of the index on which to perform the operation.
     * @param browse.browseParams - The browseParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    browse<T>({ indexName, browseParams }: BrowseProps, requestOptions?: RequestOptions): Promise<BrowseResponse<T>>;
    /**
     * Deletes only the records from an index while keeping settings, synonyms, and rules. This operation is resource-intensive and subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - deleteIndex
     * @param clearObjects - The clearObjects object.
     * @param clearObjects.indexName - Name of the index on which to perform the operation.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    clearObjects({ indexName }: ClearObjectsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Deletes all rules from the index.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param clearRules - The clearRules object.
     * @param clearRules.indexName - Name of the index on which to perform the operation.
     * @param clearRules.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    clearRules({ indexName, forwardToReplicas }: ClearRulesProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Deletes all synonyms from the index.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param clearSynonyms - The clearSynonyms object.
     * @param clearSynonyms.indexName - Name of the index on which to perform the operation.
     * @param clearSynonyms.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    clearSynonyms({ indexName, forwardToReplicas }: ClearSynonymsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Deletes the API key.
     *
     * Required API Key ACLs:
     *  - admin
     * @param deleteApiKey - The deleteApiKey object.
     * @param deleteApiKey.key - API key.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteApiKey({ key }: DeleteApiKeyProps, requestOptions?: RequestOptions): Promise<DeleteApiKeyResponse>;
    /**
     * This operation doesn\'t accept empty filters.  This operation is resource-intensive. You should only use it if you can\'t get the object IDs of the records you want to delete. It\'s more efficient to get a list of object IDs with the [`browse` operation](#tag/Search/operation/browse), and then delete the records using the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - deleteIndex
     * @param deleteBy - The deleteBy object.
     * @param deleteBy.indexName - Name of the index on which to perform the operation.
     * @param deleteBy.deleteByParams - The deleteByParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteBy({ indexName, deleteByParams }: DeleteByProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Deletes an index and all its settings.  - Deleting an index doesn\'t delete its analytics data. - If you try to delete a non-existing index, the operation is ignored without warning. - If the index you want to delete has replica indices, the replicas become independent indices. - If the index you want to delete is a replica index, you must first unlink it from its primary index before you can delete it.   For more information, see [Delete replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/deleting-replicas/).
     *
     * Required API Key ACLs:
     *  - deleteIndex
     * @param deleteIndex - The deleteIndex object.
     * @param deleteIndex.indexName - Name of the index on which to perform the operation.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteIndex({ indexName }: DeleteIndexProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse>;
    /**
     * Deletes a record by its object ID.  To delete more than one record, use the [`batch` operation](#tag/Records/operation/batch). To delete records matching a query, use the [`deleteBy` operation](#tag/Records/operation/deleteBy).
     *
     * Required API Key ACLs:
     *  - deleteObject
     * @param deleteObject - The deleteObject object.
     * @param deleteObject.indexName - Name of the index on which to perform the operation.
     * @param deleteObject.objectID - Unique record identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteObject({ indexName, objectID }: DeleteObjectProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse>;
    /**
     * Deletes a rule by its ID. To find the object ID for rules, use the [`search` operation](#tag/Rules/operation/searchRules).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param deleteRule - The deleteRule object.
     * @param deleteRule.indexName - Name of the index on which to perform the operation.
     * @param deleteRule.objectID - Unique identifier of a rule object.
     * @param deleteRule.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteRule({ indexName, objectID, forwardToReplicas }: DeleteRuleProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Deletes a source from the list of allowed sources.
     *
     * Required API Key ACLs:
     *  - admin
     * @param deleteSource - The deleteSource object.
     * @param deleteSource.source - IP address range of the source.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteSource({ source }: DeleteSourceProps, requestOptions?: RequestOptions): Promise<DeleteSourceResponse>;
    /**
     * Deletes a synonym by its ID. To find the object IDs of your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param deleteSynonym - The deleteSynonym object.
     * @param deleteSynonym.indexName - Name of the index on which to perform the operation.
     * @param deleteSynonym.objectID - Unique identifier of a synonym object.
     * @param deleteSynonym.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteSynonym({ indexName, objectID, forwardToReplicas }: DeleteSynonymProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse>;
    /**
     * Gets the permissions and restrictions of an API key.  When authenticating with the admin API key, you can request information for any of your application\'s keys. When authenticating with other API keys, you can only retrieve information for that key, with the description replaced by `<redacted>`.
     * @param getApiKey - The getApiKey object.
     * @param getApiKey.key - API key.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getApiKey({ key }: GetApiKeyProps, requestOptions?: RequestOptions): Promise<GetApiKeyResponse>;
    /**
     * Checks the status of a given application task.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param getAppTask - The getAppTask object.
     * @param getAppTask.taskID - Unique task identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getAppTask({ taskID }: GetAppTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse>;
    /**
     * Lists supported languages with their supported dictionary types and number of custom entries.
     *
     * Required API Key ACLs:
     *  - settings
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getDictionaryLanguages(requestOptions?: RequestOptions | undefined): Promise<{
        [key: string]: Languages;
    }>;
    /**
     * Retrieves the languages for which standard dictionary entries are turned off.
     *
     * Required API Key ACLs:
     *  - settings
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getDictionarySettings(requestOptions?: RequestOptions | undefined): Promise<GetDictionarySettingsResponse>;
    /**
     * The request must be authenticated by an API key with the [`logs` ACL](https://www.algolia.com/doc/guides/security/api-keys/#access-control-list-acl).  - Logs are held for the last seven days. - Up to 1,000 API requests per server are logged. - This request counts towards your [operations quota](https://support.algolia.com/hc/en-us/articles/4406981829777-How-does-Algolia-count-records-and-operations-) but doesn\'t appear in the logs itself.
     *
     * Required API Key ACLs:
     *  - logs
     * @param getLogs - The getLogs object.
     * @param getLogs.offset - First log entry to retrieve. The most recent entries are listed first.
     * @param getLogs.length - Maximum number of entries to retrieve.
     * @param getLogs.indexName - Index for which to retrieve log entries. By default, log entries are retrieved for all indices.
     * @param getLogs.type - Type of log entries to retrieve. By default, all log entries are retrieved.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getLogs({ offset, length, indexName, type }?: GetLogsProps, requestOptions?: RequestOptions | undefined): Promise<GetLogsResponse>;
    /**
     * Retrieves one record by its object ID.  To retrieve more than one record, use the [`objects` operation](#tag/Records/operation/getObjects).
     *
     * Required API Key ACLs:
     *  - search
     * @param getObject - The getObject object.
     * @param getObject.indexName - Name of the index on which to perform the operation.
     * @param getObject.objectID - Unique record identifier.
     * @param getObject.attributesToRetrieve - Attributes to include with the records in the response. This is useful to reduce the size of the API response. By default, all retrievable attributes are returned.  `objectID` is always retrieved.  Attributes included in `unretrievableAttributes` won\'t be retrieved unless the request is authenticated with the admin API key.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getObject({ indexName, objectID, attributesToRetrieve }: GetObjectProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Retrieves one or more records, potentially from different indices.  Records are returned in the same order as the requests.
     *
     * Required API Key ACLs:
     *  - search
     * @param getObjectsParams - Request object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getObjects<T>(getObjectsParams: GetObjectsParams, requestOptions?: RequestOptions): Promise<GetObjectsResponse<T>>;
    /**
     * Retrieves a rule by its ID. To find the object ID of rules, use the [`search` operation](#tag/Rules/operation/searchRules).
     *
     * Required API Key ACLs:
     *  - settings
     * @param getRule - The getRule object.
     * @param getRule.indexName - Name of the index on which to perform the operation.
     * @param getRule.objectID - Unique identifier of a rule object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getRule({ indexName, objectID }: GetRuleProps, requestOptions?: RequestOptions): Promise<Rule>;
    /**
     * Retrieves an object with non-null index settings.
     *
     * Required API Key ACLs:
     *  - settings
     * @param getSettings - The getSettings object.
     * @param getSettings.indexName - Name of the index on which to perform the operation.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSettings({ indexName }: GetSettingsProps, requestOptions?: RequestOptions): Promise<SettingsResponse>;
    /**
     * Retrieves all allowed IP addresses with access to your application.
     *
     * Required API Key ACLs:
     *  - admin
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSources(requestOptions?: RequestOptions | undefined): Promise<Array<Source>>;
    /**
     * Retrieves a synonym by its ID. To find the object IDs for your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).
     *
     * Required API Key ACLs:
     *  - settings
     * @param getSynonym - The getSynonym object.
     * @param getSynonym.indexName - Name of the index on which to perform the operation.
     * @param getSynonym.objectID - Unique identifier of a synonym object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSynonym({ indexName, objectID }: GetSynonymProps, requestOptions?: RequestOptions): Promise<SynonymHit>;
    /**
     * Checks the status of a given task.  Indexing tasks are asynchronous. When you add, update, or delete records or indices, a task is created on a queue and completed depending on the load on the server.  The indexing tasks\' responses include a task ID that you can use to check the status.
     *
     * Required API Key ACLs:
     *  - addObject
     * @param getTask - The getTask object.
     * @param getTask.indexName - Name of the index on which to perform the operation.
     * @param getTask.taskID - Unique task identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTask({ indexName, taskID }: GetTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse>;
    /**
     * Get the IDs of the 10 users with the highest number of records per cluster.  Since it can take a few seconds to get the data from the different clusters, the response isn\'t real-time.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopUserIds(requestOptions?: RequestOptions | undefined): Promise<GetTopUserIdsResponse>;
    /**
     * Returns the user ID data stored in the mapping.  Since it can take a few seconds to get the data from the different clusters, the response isn\'t real-time.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param getUserId - The getUserId object.
     * @param getUserId.userID - Unique identifier of the user who makes the search request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getUserId({ userID }: GetUserIdProps, requestOptions?: RequestOptions): Promise<UserId>;
    /**
     * To determine when the time-consuming process of creating a large batch of users or migrating users from one cluster to another is complete, this operation retrieves the status of the process.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param hasPendingMappings - The hasPendingMappings object.
     * @param hasPendingMappings.getClusters - Whether to include the cluster\'s pending mapping state in the response.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    hasPendingMappings({ getClusters }?: HasPendingMappingsProps, requestOptions?: RequestOptions | undefined): Promise<HasPendingMappingsResponse>;
    /**
     * Lists all API keys associated with your Algolia application, including their permissions and restrictions.
     *
     * Required API Key ACLs:
     *  - admin
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    listApiKeys(requestOptions?: RequestOptions | undefined): Promise<ListApiKeysResponse>;
    /**
     * Lists the available clusters in a multi-cluster setup.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    listClusters(requestOptions?: RequestOptions | undefined): Promise<ListClustersResponse>;
    /**
     * Lists all indices in the current Algolia application.  The request follows any index restrictions of the API key you use to make the request.
     *
     * Required API Key ACLs:
     *  - listIndexes
     * @param listIndices - The listIndices object.
     * @param listIndices.page - Requested page of the API response. If `null`, the API response is not paginated.
     * @param listIndices.hitsPerPage - Number of hits per page.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    listIndices({ page, hitsPerPage }?: ListIndicesProps, requestOptions?: RequestOptions | undefined): Promise<ListIndicesResponse>;
    /**
     * Lists the userIDs assigned to a multi-cluster application.  Since it can take a few seconds to get the data from the different clusters, the response isn\'t real-time.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param listUserIds - The listUserIds object.
     * @param listUserIds.page - Requested page of the API response. If `null`, the API response is not paginated.
     * @param listUserIds.hitsPerPage - Number of hits per page.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    listUserIds({ page, hitsPerPage }?: ListUserIdsProps, requestOptions?: RequestOptions | undefined): Promise<ListUserIdsResponse>;
    /**
     * Adds, updates, or deletes records in multiple indices with a single API request.  - Actions are applied in the order they are specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     * @param batchParams - The batchParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    multipleBatch(batchParams: BatchParams, requestOptions?: RequestOptions): Promise<MultipleBatchResponse>;
    /**
     * Copies or moves (renames) an index within the same Algolia application.  - Existing destination indices are overwritten, except for their analytics data. - If the destination index doesn\'t exist yet, it\'ll be created. - This operation is resource-intensive.  **Copy**  - Copying a source index that doesn\'t exist creates a new index with 0 records and default settings. - The API keys of the source index are merged with the existing keys in the destination index. - You can\'t copy the `enableReRanking`, `mode`, and `replicas` settings. - You can\'t copy to a destination index that already has replicas. - Be aware of the [size limits](https://www.algolia.com/doc/guides/scaling/algolia-service-limits/#application-record-and-index-limits). - Related guide: [Copy indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/copy-indices/)  **Move**  - Moving a source index that doesn\'t exist is ignored without returning an error. - When moving an index, the analytics data keeps its original name, and a new set of analytics data is started for the new name.   To access the original analytics in the dashboard, create an index with the original name. - If the destination index has replicas, moving will overwrite the existing index and copy the data to the replica indices. - Related guide: [Move indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/move-indices/).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - addObject
     * @param operationIndex - The operationIndex object.
     * @param operationIndex.indexName - Name of the index on which to perform the operation.
     * @param operationIndex.operationIndexParams - The operationIndexParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    operationIndex({ indexName, operationIndexParams }: OperationIndexProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Adds new attributes to a record, or updates existing ones.  - If a record with the specified object ID doesn\'t exist,   a new record is added to the index **if** `createIfNotExists` is true. - If the index doesn\'t exist yet, this method creates a new index. - You can use any first-level attribute but not nested attributes.   If you specify a nested attribute, this operation replaces its first-level ancestor.  To update an attribute without pushing the entire record, you can use these built-in operations. These operations can be helpful if you don\'t have access to your initial data.  - Increment: increment a numeric attribute - Decrement: decrement a numeric attribute - Add: append a number or string element to an array attribute - Remove: remove all matching number or string elements from an array attribute made of numbers or strings - AddUnique: add a number or string element to an array attribute made of numbers or strings only if it\'s not already present - IncrementFrom: increment a numeric integer attribute only if the provided value matches the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementFrom value of 2 for the version attribute, but the current value of the attribute is 1, the engine ignores the update. If the object doesn\'t exist, the engine only creates it if you pass an IncrementFrom value of 0. - IncrementSet: increment a numeric integer attribute only if the provided value is greater than the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementSet value of 2 for the version attribute, and the current value of the attribute is 1, the engine updates the object. If the object doesn\'t exist yet, the engine only creates it if you pass an IncrementSet value greater than 0.  You can specify an operation by providing an object with the attribute to update as the key and its value being an object with the following properties:  - _operation: the operation to apply on the attribute - value: the right-hand side argument to the operation, for example, increment or decrement step, value to add or remove.  When updating multiple attributes or using multiple operations targeting the same record, you should use a single partial update for faster processing.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - addObject
     * @param partialUpdateObject - The partialUpdateObject object.
     * @param partialUpdateObject.indexName - Name of the index on which to perform the operation.
     * @param partialUpdateObject.objectID - Unique record identifier.
     * @param partialUpdateObject.attributesToUpdate - Attributes with their values.
     * @param partialUpdateObject.createIfNotExists - Whether to create a new record if it doesn\'t exist.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    partialUpdateObject({ indexName, objectID, attributesToUpdate, createIfNotExists }: PartialUpdateObjectProps, requestOptions?: RequestOptions): Promise<UpdatedAtWithObjectIdResponse>;
    /**
     * Deletes a user ID and its associated data from the clusters.
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param removeUserId - The removeUserId object.
     * @param removeUserId.userID - Unique identifier of the user who makes the search request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    removeUserId({ userID }: RemoveUserIdProps, requestOptions?: RequestOptions): Promise<RemoveUserIdResponse>;
    /**
     * Replaces the list of allowed sources.
     *
     * Required API Key ACLs:
     *  - admin
     * @param replaceSources - The replaceSources object.
     * @param replaceSources.source - Allowed sources.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    replaceSources({ source }: ReplaceSourcesProps, requestOptions?: RequestOptions): Promise<ReplaceSourceResponse>;
    /**
     * Restores a deleted API key.  Restoring resets the `validity` attribute to `0`.  Algolia stores up to 1,000 API keys per application. If you create more, the oldest API keys are deleted and can\'t be restored.
     *
     * Required API Key ACLs:
     *  - admin
     * @param restoreApiKey - The restoreApiKey object.
     * @param restoreApiKey.key - API key.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    restoreApiKey({ key }: RestoreApiKeyProps, requestOptions?: RequestOptions): Promise<AddApiKeyResponse>;
    /**
     * Adds a record to an index or replaces it.  - If the record doesn\'t have an object ID, a new record with an auto-generated object ID is added to your index. - If a record with the specified object ID exists, the existing record is replaced. - If a record with the specified object ID doesn\'t exist, a new record is added to your index. - If you add a record to an index that doesn\'t exist yet, a new index is created.  To update _some_ attributes of a record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject). To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - addObject
     * @param saveObject - The saveObject object.
     * @param saveObject.indexName - Name of the index on which to perform the operation.
     * @param saveObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    saveObject<T extends object>({ indexName, body }: SaveObjectProps<T>, requestOptions?: RequestOptions): Promise<SaveObjectResponse>;
    /**
     * If a rule with the specified object ID doesn\'t exist, it\'s created. Otherwise, the existing rule is replaced.  To create or update more than one rule, use the [`batch` operation](#tag/Rules/operation/saveRules).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param saveRule - The saveRule object.
     * @param saveRule.indexName - Name of the index on which to perform the operation.
     * @param saveRule.objectID - Unique identifier of a rule object.
     * @param saveRule.rule - The rule object.
     * @param saveRule.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    saveRule({ indexName, objectID, rule, forwardToReplicas }: SaveRuleProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Create or update multiple rules.  If a rule with the specified object ID doesn\'t exist, Algolia creates a new one. Otherwise, existing rules are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param saveRules - The saveRules object.
     * @param saveRules.indexName - Name of the index on which to perform the operation.
     * @param saveRules.rules - The rules object.
     * @param saveRules.forwardToReplicas - Whether changes are applied to replica indices.
     * @param saveRules.clearExistingRules - Whether existing rules should be deleted before adding this batch.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    saveRules({ indexName, rules, forwardToReplicas, clearExistingRules }: SaveRulesProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * If a synonym with the specified object ID doesn\'t exist, Algolia adds a new one. Otherwise, the existing synonym is replaced. To add multiple synonyms in a single API request, use the [`batch` operation](#tag/Synonyms/operation/saveSynonyms).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param saveSynonym - The saveSynonym object.
     * @param saveSynonym.indexName - Name of the index on which to perform the operation.
     * @param saveSynonym.objectID - Unique identifier of a synonym object.
     * @param saveSynonym.synonymHit - The synonymHit object.
     * @param saveSynonym.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    saveSynonym({ indexName, objectID, synonymHit, forwardToReplicas }: SaveSynonymProps, requestOptions?: RequestOptions): Promise<SaveSynonymResponse>;
    /**
     * If a synonym with the `objectID` doesn\'t exist, Algolia adds a new one. Otherwise, existing synonyms are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param saveSynonyms - The saveSynonyms object.
     * @param saveSynonyms.indexName - Name of the index on which to perform the operation.
     * @param saveSynonyms.synonymHit - The synonymHit object.
     * @param saveSynonyms.forwardToReplicas - Whether changes are applied to replica indices.
     * @param saveSynonyms.replaceExistingSynonyms - Whether to replace all synonyms in the index with the ones sent with this request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    saveSynonyms({ indexName, synonymHit, forwardToReplicas, replaceExistingSynonyms }: SaveSynonymsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Sends multiple search requests to one or more indices.  This can be useful in these cases:  - Different indices for different purposes, such as, one index for products, another one for marketing content. - Multiple searches to the same index—for example, with different filters.  Use the helper `searchForHits` or `searchForFacets` to get the results in a more convenient format, if you already know the return type you want.
     *
     * Required API Key ACLs:
     *  - search
     * @param searchMethodParams - Muli-search request body. Results are returned in the same order as the requests.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    search<T>(searchMethodParams: SearchMethodParams | LegacySearchMethodProps, requestOptions?: RequestOptions): Promise<SearchResponses<T>>;
    /**
     * Searches for standard and custom dictionary entries.
     *
     * Required API Key ACLs:
     *  - settings
     * @param searchDictionaryEntries - The searchDictionaryEntries object.
     * @param searchDictionaryEntries.dictionaryName - Dictionary type in which to search.
     * @param searchDictionaryEntries.searchDictionaryEntriesParams - The searchDictionaryEntriesParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchDictionaryEntries({ dictionaryName, searchDictionaryEntriesParams }: SearchDictionaryEntriesProps, requestOptions?: RequestOptions): Promise<SearchDictionaryEntriesResponse>;
    /**
     * Searches for values of a specified facet attribute.  - By default, facet values are sorted by decreasing count.   You can adjust this with the `sortFacetValueBy` parameter. - Searching for facet values doesn\'t work if you have **more than 65 searchable facets and searchable attributes combined**.
     *
     * Required API Key ACLs:
     *  - search
     * @param searchForFacetValues - The searchForFacetValues object.
     * @param searchForFacetValues.indexName - Name of the index on which to perform the operation.
     * @param searchForFacetValues.facetName - Facet attribute in which to search for values.  This attribute must be included in the `attributesForFaceting` index setting with the `searchable()` modifier.
     * @param searchForFacetValues.searchForFacetValuesRequest - The searchForFacetValuesRequest object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchForFacetValues({ indexName, facetName, searchForFacetValuesRequest }: SearchForFacetValuesProps, requestOptions?: RequestOptions): Promise<SearchForFacetValuesResponse>;
    /**
     * Searches for rules in your index.
     *
     * Required API Key ACLs:
     *  - settings
     * @param searchRules - The searchRules object.
     * @param searchRules.indexName - Name of the index on which to perform the operation.
     * @param searchRules.searchRulesParams - The searchRulesParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchRules({ indexName, searchRulesParams }: SearchRulesProps, requestOptions?: RequestOptions): Promise<SearchRulesResponse>;
    /**
     * Searches a single index and returns matching search results (_hits_).  This method lets you retrieve up to 1,000 hits. If you need more, use the [`browse` operation](#tag/Search/operation/browse) or increase the `paginatedLimitedTo` index setting.
     *
     * Required API Key ACLs:
     *  - search
     * @param searchSingleIndex - The searchSingleIndex object.
     * @param searchSingleIndex.indexName - Name of the index on which to perform the operation.
     * @param searchSingleIndex.searchParams - The searchParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchSingleIndex<T>({ indexName, searchParams }: SearchSingleIndexProps, requestOptions?: RequestOptions): Promise<SearchResponse<T>>;
    /**
     * Searches for synonyms in your index.
     *
     * Required API Key ACLs:
     *  - settings
     * @param searchSynonyms - The searchSynonyms object.
     * @param searchSynonyms.indexName - Name of the index on which to perform the operation.
     * @param searchSynonyms.searchSynonymsParams - Body of the `searchSynonyms` operation.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchSynonyms({ indexName, searchSynonymsParams }: SearchSynonymsProps, requestOptions?: RequestOptions): Promise<SearchSynonymsResponse>;
    /**
     * Since it can take a few seconds to get the data from the different clusters, the response isn\'t real-time.  To ensure rapid updates, the user IDs index isn\'t built at the same time as the mapping. Instead, it\'s built every 12 hours, at the same time as the update of user ID usage. For example, if you add or move a user ID, the search will show an old value until the next time the mapping is rebuilt (every 12 hours).
     *
     * Required API Key ACLs:
     *  - admin
     *
     * @deprecated
     * @param searchUserIdsParams - The searchUserIdsParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchUserIds(searchUserIdsParams: SearchUserIdsParams, requestOptions?: RequestOptions): Promise<SearchUserIdsResponse>;
    /**
     * Turns standard stop word dictionary entries on or off for a given language.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param dictionarySettingsParams - The dictionarySettingsParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    setDictionarySettings(dictionarySettingsParams: DictionarySettingsParams, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Update the specified index settings.  Index settings that you don\'t specify are left unchanged. Specify `null` to reset a setting to its default value.  For best performance, update the index settings before you add new records to your index.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param setSettings - The setSettings object.
     * @param setSettings.indexName - Name of the index on which to perform the operation.
     * @param setSettings.indexSettings - The indexSettings object.
     * @param setSettings.forwardToReplicas - Whether changes are applied to replica indices.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    setSettings({ indexName, indexSettings, forwardToReplicas }: SetSettingsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse>;
    /**
     * Replaces the permissions of an existing API key.  Any unspecified attribute resets that attribute to its default value.
     *
     * Required API Key ACLs:
     *  - admin
     * @param updateApiKey - The updateApiKey object.
     * @param updateApiKey.key - API key.
     * @param updateApiKey.apiKey - The apiKey object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    updateApiKey({ key, apiKey }: UpdateApiKeyProps, requestOptions?: RequestOptions): Promise<UpdateApiKeyResponse>;
};

/**
 * How to change the attribute.
 */
type BuiltInOperationType = 'Increment' | 'Decrement' | 'Add' | 'Remove' | 'AddUnique' | 'IncrementFrom' | 'IncrementSet';

type BuiltInOperationValue = string | number;

/**
 * Update to perform on the attribute.
 */
type BuiltInOperation = {
    _operation: BuiltInOperationType;
    value: BuiltInOperationValue;
};

type AttributeToUpdate = string | BuiltInOperation;

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string | undefined;
};

type EventStatus = 'created' | 'started' | 'retried' | 'failed' | 'succeeded' | 'critical';

type EventType = 'fetch' | 'record' | 'log' | 'transform';

/**
 * An event describe a step of the task execution flow..
 */
type Event = {
    /**
     * Universally unique identifier (UUID) of an event.
     */
    eventID: string;
    /**
     * Universally unique identifier (UUID) of a task run.
     */
    runID: string;
    status: EventStatus | null;
    type: EventType;
    /**
     * The extracted record batch size.
     */
    batchSize: number;
    data?: {
        [key: string]: any;
    } | null | undefined;
    /**
     * Date of publish RFC 3339 format.
     */
    publishedAt: string;
};

type WatchResponse = {
    /**
     * Universally unique identifier (UUID) of a task run.
     */
    runID: string;
    /**
     * Universally unique identifier (UUID) of an event.
     */
    eventID?: string | undefined;
    /**
     * This field is always null when used with the Push endpoint. When used for a source discover or source validate run, it will include the sampled data of the source.
     */
    data?: Array<Record<string, unknown>> | undefined;
    /**
     * in case of error, observability events will be added to the response.
     */
    events?: Array<Event> | undefined;
    /**
     * a message describing the outcome of the operation that has been ran (push, discover or validate) run.
     */
    message?: string | undefined;
    /**
     * Date of creation in RFC 3339 format.
     */
    createdAt?: string | undefined;
};

type ReplaceAllObjectsWithTransformationResponse = {
    copyOperationResponse: UpdatedAtResponse;
    /**
     * The response of the `push` request(s).
     */
    watchResponses: Array<WatchResponse>;
    moveOperationResponse: UpdatedAtResponse;
};

type SearchClientWorkerHelpers = {
    generateSecuredApiKey: (opts: GenerateSecuredApiKeyOptions) => Promise<string>;
    getSecuredApiKeyRemainingValidity: (opts: GetSecuredApiKeyRemainingValidityOptions) => number;
};
type SearchClient = ReturnType<typeof createSearchClient> & SearchClientWorkerHelpers;

declare function searchClient(appId: string, apiKey: string, options?: ClientOptions | undefined): SearchClient;

export { type AccountCopyIndexOptions, type Acl, type Action, type AddApiKeyResponse, type AddOrUpdateObjectProps, type AdvancedSyntaxFeatures, type AlternativesAsExact, type Anchoring, type ApiKey, type ApiKeyOperation, type AroundPrecision, type AroundRadius, type AroundRadiusAll, type AssignUserIdParams, type AssignUserIdProps, type AttributeToUpdate, type AutomaticFacetFilter, type AutomaticFacetFilters, type Banner, type BannerImage, type BannerImageUrl, type BannerLink, type BaseGetApiKeyResponse, type BaseIndexSettings, type BaseSearchParams, type BaseSearchParamsWithoutQuery, type BaseSearchResponse, type BatchAssignUserIdsParams, type BatchAssignUserIdsProps, type BatchDictionaryEntriesParams, type BatchDictionaryEntriesProps, type BatchDictionaryEntriesRequest, type BatchParams, type BatchProps, type BatchRequest, type BatchResponse, type BatchWriteParams, type BooleanString, type BrowseOptions, type BrowsePagination, type BrowseParams, type BrowseParamsObject, type BrowseProps, type BrowseResponse, type BuiltInOperation, type BuiltInOperationType, type BuiltInOperationValue, type ChunkedBatchOptions, type ClearObjectsProps, type ClearRulesProps, type ClearSynonymsProps, type Condition, type Consequence, type ConsequenceHide, type ConsequenceParams, type ConsequenceQuery, type ConsequenceQueryObject, type CreatedAtResponse, type Cursor, type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type DeleteApiKeyProps, type DeleteApiKeyResponse, type DeleteByParams, type DeleteByProps, type DeleteIndexProps, type DeleteObjectProps, type DeleteObjectsOptions, type DeleteRuleProps, type DeleteSourceProps, type DeleteSourceResponse, type DeleteSynonymProps, type DeletedAtResponse, type DictionaryAction, type DictionaryEntry, type DictionaryEntryState, type DictionaryEntryType, type DictionaryLanguage, type DictionarySettingsParams, type DictionaryType, type Distinct, type Edit, type EditType, type ErrorBase, type Event, type EventStatus, type EventType, type ExactOnSingleWordQuery, type Exhaustive, type FacetFilters, type FacetHits, type FacetOrdering, type FacetStats, type Facets, type FetchedIndex, type GenerateSecuredApiKeyOptions, type GetApiKeyProps, type GetApiKeyResponse, type GetAppTaskProps, type GetDictionarySettingsResponse, type GetLogsProps, type GetLogsResponse, type GetObjectProps, type GetObjectsParams, type GetObjectsRequest, type GetObjectsResponse, type GetRuleProps, type GetSecuredApiKeyRemainingValidityOptions, type GetSettingsProps, type GetSynonymProps, type GetTaskProps, type GetTaskResponse, type GetTopUserIdsResponse, type GetUserIdProps, type HasPendingMappingsProps, type HasPendingMappingsResponse, type HighlightResult, type HighlightResultOption, type Hit, type IgnorePlurals, type IndexSettings, type IndexSettingsAsSearchParams, type InsideBoundingBox, type Languages, type LegacySearchMethodProps, type ListApiKeysResponse, type ListClustersResponse, type ListIndicesProps, type ListIndicesResponse, type ListUserIdsProps, type ListUserIdsResponse, type Log, type LogQuery, type LogType, type MatchLevel, type MatchedGeoLocation, type Mode, type MultipleBatchRequest, type MultipleBatchResponse, type NumericFilters, type OperationIndexParams, type OperationIndexProps, type OperationType, type OptionalFilters, type OptionalWords, type Params, type PartialUpdateObjectProps, type PartialUpdateObjectsOptions, type Personalization, type Promote, type PromoteObjectID, type PromoteObjectIDs, type QueryType, type Range, type RankingInfo, type ReRankingApplyFilter, type Redirect, type RedirectRuleIndexData, type RedirectRuleIndexMetadata, type RedirectURL, type RemoveStopWords, type RemoveUserIdProps, type RemoveUserIdResponse, type RemoveWordsIfNoResults, type RenderingContent, type ReplaceAllObjectsOptions, type ReplaceAllObjectsResponse, type ReplaceAllObjectsWithTransformationResponse, type ReplaceSourceResponse, type ReplaceSourcesProps, type RestoreApiKeyProps, type Rule, type SaveObjectProps, type SaveObjectResponse, type SaveObjectsOptions, type SaveRuleProps, type SaveRulesProps, type SaveSynonymProps, type SaveSynonymResponse, type SaveSynonymsProps, type ScopeType, type SearchClient, type SearchClientNodeHelpers, type SearchClientWorkerHelpers, type SearchDictionaryEntriesParams, type SearchDictionaryEntriesProps, type SearchDictionaryEntriesResponse, type SearchForFacetValuesProps, type SearchForFacetValuesRequest, type SearchForFacetValuesResponse, type SearchForFacets, type SearchForFacetsOptions, type SearchForHits, type SearchForHitsOptions, type SearchHits, type SearchMethodParams, type SearchPagination, type SearchParams, type SearchParamsObject, type SearchParamsQuery, type SearchParamsString, type SearchQuery, type SearchResponse, type SearchResponses, type SearchResult, type SearchRulesParams, type SearchRulesProps, type SearchRulesResponse, type SearchSingleIndexProps, type SearchStrategy, type SearchSynonymsParams, type SearchSynonymsProps, type SearchSynonymsResponse, type SearchTypeDefault, type SearchTypeFacet, type SearchUserIdsParams, type SearchUserIdsResponse, type SecuredApiKeyRestrictions, type SemanticSearch, type SetSettingsProps, type SettingsResponse, type SnippetResult, type SnippetResultOption, type SortRemainingBy, type Source, type StandardEntries, type SupportedLanguage, type SynonymHit, type SynonymType, type TagFilters, type TaskStatus, type TimeRange, type TypoTolerance, type TypoToleranceEnum, type UpdateApiKeyProps, type UpdateApiKeyResponse, type UpdatedAtResponse, type UpdatedAtWithObjectIdResponse, type UserHighlightResult, type UserHit, type UserId, type Value, type WaitForApiKeyOptions, type WaitForAppTaskOptions, type WaitForTaskOptions, type WatchResponse, type Widgets, type WithPrimary, apiClientVersion, searchClient };
