function Q(){function u(i){return new Promise(d=>{let p=new XMLHttpRequest;p.open(i.method,i.url,!0),Object.keys(i.headers).forEach(e=>p.setRequestHeader(e,i.headers[e]));let h=(e,r)=>setTimeout(()=>{p.abort(),d({status:0,content:r,isTimedOut:!0})},e),P=h(i.connectTimeout,"Connection timeout"),c;p.onreadystatechange=()=>{p.readyState>p.OPENED&&c===void 0&&(clearTimeout(P),c=h(i.responseTimeout,"Socket timeout"))},p.onerror=()=>{p.status===0&&(clearTimeout(P),clearTimeout(c),d({content:p.responseText||"Network request failed",status:p.status,isTimedOut:!1}))},p.onload=()=>{clearTimeout(P),clearTimeout(c),d({content:p.responseText,status:p.status,isTimedOut:!1})},p.send(i.data)})}return{send:u}}function L(u){let i,d=`algolia-client-js-${u.key}`;function p(){return i===void 0&&(i=u.localStorage||window.localStorage),i}function h(){return JSON.parse(p().getItem(d)||"{}")}function P(e){p().setItem(d,JSON.stringify(e))}function c(){let e=u.timeToLive?u.timeToLive*1e3:null,r=h(),t=Object.fromEntries(Object.entries(r).filter(([,s])=>s.timestamp!==void 0));if(P(t),!e)return;let a=Object.fromEntries(Object.entries(t).filter(([,s])=>{let o=new Date().getTime();return!(s.timestamp+e<o)}));P(a)}return{get(e,r,t={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(c(),h()[JSON.stringify(e)])).then(a=>Promise.all([a?a.value:r(),a!==void 0])).then(([a,s])=>Promise.all([a,s||t.miss(a)])).then(([a])=>a)},set(e,r){return Promise.resolve().then(()=>{let t=h();return t[JSON.stringify(e)]={timestamp:new Date().getTime(),value:r},p().setItem(d,JSON.stringify(t)),r})},delete(e){return Promise.resolve().then(()=>{let r=h();delete r[JSON.stringify(e)],p().setItem(d,JSON.stringify(r))})},clear(){return Promise.resolve().then(()=>{p().removeItem(d)})}}}function Y(){return{get(u,i,d={miss:()=>Promise.resolve()}){return i().then(h=>Promise.all([h,d.miss(h)])).then(([h])=>h)},set(u,i){return Promise.resolve(i)},delete(u){return Promise.resolve()},clear(){return Promise.resolve()}}}function O(u){let i=[...u.caches],d=i.shift();return d===void 0?Y():{get(p,h,P={miss:()=>Promise.resolve()}){return d.get(p,h,P).catch(()=>O({caches:i}).get(p,h,P))},set(p,h){return d.set(p,h).catch(()=>O({caches:i}).set(p,h))},delete(p){return d.delete(p).catch(()=>O({caches:i}).delete(p))},clear(){return d.clear().catch(()=>O({caches:i}).clear())}}}function T(u={serializable:!0}){let i={};return{get(d,p,h={miss:()=>Promise.resolve()}){let P=JSON.stringify(d);if(P in i)return Promise.resolve(u.serializable?JSON.parse(i[P]):i[P]);let c=p();return c.then(e=>h.miss(e)).then(()=>c)},set(d,p){return i[JSON.stringify(d)]=u.serializable?JSON.stringify(p):p,Promise.resolve(p)},delete(d){return delete i[JSON.stringify(d)],Promise.resolve()},clear(){return i={},Promise.resolve()}}}function Z(u){let i={value:`Algolia for JavaScript (${u})`,add(d){let p=`; ${d.segment}${d.version!==void 0?` (${d.version})`:""}`;return i.value.indexOf(p)===-1&&(i.value=`${i.value}${p}`),i}};return i}function K(u,i,d="WithinHeaders"){let p={"x-algolia-api-key":i,"x-algolia-application-id":u};return{headers(){return d==="WithinHeaders"?p:{}},queryParameters(){return d==="WithinQueryParameters"?p:{}}}}function S({func:u,validate:i,aggregator:d,error:p,timeout:h=()=>0}){let P=c=>new Promise((e,r)=>{u(c).then(async t=>(d&&await d(t),await i(t)?e(t):p&&await p.validate(t)?r(new Error(await p.message(t))):setTimeout(()=>{P(t).then(e).catch(r)},await h()))).catch(t=>{r(t)})});return P()}function F({algoliaAgents:u,client:i,version:d}){let p=Z(d).add({segment:i,version:d});return u.forEach(h=>p.add(h)),p}function _(){return{debug(u,i){return Promise.resolve()},info(u,i){return Promise.resolve()},error(u,i){return Promise.resolve()}}}var G=2*60*1e3;function B(u,i="up"){let d=Date.now();function p(){return i==="up"||Date.now()-d>G}function h(){return i==="timed out"&&Date.now()-d<=G}return{...u,status:i,lastUpdate:d,isUp:p,isTimedOut:h}}var $=class extends Error{name="AlgoliaError";constructor(u,i){super(u),i&&(this.name=i)}};var M=class extends ${stackTrace;constructor(u,i,d){super(u,d),this.stackTrace=i}},ee=class extends M{constructor(u){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",u,"RetryError")}},I=class extends M{status;constructor(u,i,d,p="ApiError"){super(u,d,p),this.status=i}},re=class extends ${response;constructor(u,i){super(u,"DeserializationError"),this.response=i}},te=class extends I{error;constructor(u,i,d,p){super(u,i,p,"DetailedApiError"),this.error=d}};function W(u){let i=u;for(let d=u.length-1;d>0;d--){let p=Math.floor(Math.random()*(d+1)),h=u[d];i[d]=u[p],i[p]=h}return i}function se(u,i,d){let p=oe(d),h=`${u.protocol}://${u.url}${u.port?`:${u.port}`:""}/${i.charAt(0)==="/"?i.substring(1):i}`;return p.length&&(h+=`?${p}`),h}function oe(u){return Object.keys(u).filter(i=>u[i]!==void 0).sort().map(i=>`${i}=${encodeURIComponent(Object.prototype.toString.call(u[i])==="[object Array]"?u[i].join(","):u[i]).replace(/\+/g,"%20")}`).join("&")}function ae(u,i){if(u.method==="GET"||u.data===void 0&&i.data===void 0)return;let d=Array.isArray(u.data)?u.data:{...u.data,...i.data};return JSON.stringify(d)}function ne(u,i,d){let p={Accept:"application/json",...u,...i,...d},h={};return Object.keys(p).forEach(P=>{let c=p[P];h[P.toLowerCase()]=c}),h}function ie(u){try{return JSON.parse(u.content)}catch(i){throw new re(i.message,u)}}function ue({content:u,status:i},d){try{let p=JSON.parse(u);return"error"in p?new te(p.message,i,p.error,d):new I(p.message,i,d)}catch{}return new I(u,i,d)}function ce({isTimedOut:u,status:i}){return!u&&~~i===0}function me({isTimedOut:u,status:i}){return u||ce({isTimedOut:u,status:i})||~~(i/100)!==2&&~~(i/100)!==4}function pe({status:u}){return~~(u/100)===2}function de(u){return u.map(i=>J(i))}function J(u){let i=u.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...u,request:{...u.request,headers:{...u.request.headers,...i}}}}function z({hosts:u,hostsCache:i,baseHeaders:d,logger:p,baseQueryParameters:h,algoliaAgent:P,timeouts:c,requester:e,requestsCache:r,responsesCache:t}){async function a(n){let m=await Promise.all(n.map(f=>i.get(f,()=>Promise.resolve(B(f))))),l=m.filter(f=>f.isUp()),q=m.filter(f=>f.isTimedOut()),g=[...l,...q];return{hosts:g.length>0?g:n,getTimeout(f,w){return(q.length===0&&f===0?1:q.length+3+f)*w}}}async function s(n,m,l=!0){let q=[],g=ae(n,m),y=ne(d,n.headers,m.headers),f=n.method==="GET"?{...n.data,...m.data}:{},w={...h,...n.queryParameters,...f};if(P.value&&(w["x-algolia-agent"]=P.value),m&&m.queryParameters)for(let R of Object.keys(m.queryParameters))!m.queryParameters[R]||Object.prototype.toString.call(m.queryParameters[R])==="[object Object]"?w[R]=m.queryParameters[R]:w[R]=m.queryParameters[R].toString();let b=0,j=async(R,U)=>{let E=R.pop();if(E===void 0)throw new ee(de(q));let D={...c,...m.timeouts},N={data:g,headers:y,method:n.method,url:se(E,n.path,w),connectTimeout:U(b,D.connect),responseTimeout:U(b,l?D.read:D.write)},k=v=>{let H={request:N,response:v,host:E,triesLeft:R.length};return q.push(H),H},x=await e.send(N);if(me(x)){let v=k(x);return x.isTimedOut&&b++,p.info("Retryable failure",J(v)),await i.set(E,B(E,x.isTimedOut?"timed out":"down")),j(R,U)}if(pe(x))return ie(x);throw k(x),ue(x,q)},X=u.filter(R=>R.accept==="readWrite"||(l?R.accept==="read":R.accept==="write")),C=await a(X);return j([...C.hosts].reverse(),C.getTimeout)}function o(n,m={}){let l=n.useReadTransporter||n.method==="GET";if(!l)return s(n,m,l);let q=()=>s(n,m);if((m.cacheable||n.cacheable)!==!0)return q();let y={request:n,requestOptions:m,transporter:{queryParameters:h,headers:d}};return t.get(y,()=>r.get(y,()=>r.set(y,q()).then(f=>Promise.all([r.delete(y),f]),f=>Promise.all([r.delete(y),Promise.reject(f)])).then(([f,w])=>w)),{miss:f=>t.set(y,f)})}return{hostsCache:i,requester:e,timeouts:c,logger:p,algoliaAgent:P,baseHeaders:d,baseQueryParameters:h,hosts:u,request:o,requestsCache:r,responsesCache:t}}var A="5.30.0";function he(u){return[{url:`${u}-dsn.algolia.net`,accept:"read",protocol:"https"},{url:`${u}.algolia.net`,accept:"write",protocol:"https"}].concat(W([{url:`${u}-1.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${u}-2.algolianet.com`,accept:"readWrite",protocol:"https"},{url:`${u}-3.algolianet.com`,accept:"readWrite",protocol:"https"}]))}function V({appId:u,apiKey:i,authMode:d,algoliaAgents:p,...h}){let P=K(u,i,d),c=z({hosts:he(u),...h,algoliaAgent:F({algoliaAgents:p,client:"Search",version:A}),baseHeaders:{"content-type":"text/plain",...P.headers(),...h.baseHeaders},baseQueryParameters:{...P.queryParameters(),...h.baseQueryParameters}});return{transporter:c,appId:u,apiKey:i,clearCache(){return Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>{})},get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,r){c.algoliaAgent.add({segment:e,version:r})},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?c.baseHeaders["x-algolia-api-key"]=e:c.baseQueryParameters["x-algolia-api-key"]=e},waitForTask({indexName:e,taskID:r,maxRetries:t=50,timeout:a=o=>Math.min(o*200,5e3)},s){let o=0;return S({func:()=>this.getTask({indexName:e,taskID:r},s),validate:n=>n.status==="published",aggregator:()=>o+=1,error:{validate:()=>o>=t,message:()=>`The maximum number of retries exceeded. (${o}/${t})`},timeout:()=>a(o)})},waitForAppTask({taskID:e,maxRetries:r=50,timeout:t=s=>Math.min(s*200,5e3)},a){let s=0;return S({func:()=>this.getAppTask({taskID:e},a),validate:o=>o.status==="published",aggregator:()=>s+=1,error:{validate:()=>s>=r,message:()=>`The maximum number of retries exceeded. (${s}/${r})`},timeout:()=>t(s)})},waitForApiKey({operation:e,key:r,apiKey:t,maxRetries:a=50,timeout:s=n=>Math.min(n*200,5e3)},o){let n=0,m={aggregator:()=>n+=1,error:{validate:()=>n>=a,message:()=>`The maximum number of retries exceeded. (${n}/${a})`},timeout:()=>s(n)};if(e==="update"){if(!t)throw new Error("`apiKey` is required when waiting for an `update` operation.");return S({...m,func:()=>this.getApiKey({key:r},o),validate:l=>{for(let q of Object.keys(t)){let g=t[q],y=l[q];if(Array.isArray(g)&&Array.isArray(y)){if(g.length!==y.length||g.some((f,w)=>f!==y[w]))return!1}else if(g!==y)return!1}return!0}})}return S({...m,func:()=>this.getApiKey({key:r},o).catch(l=>{if(l.status!==404)throw l}),validate:l=>e==="add"?l!==void 0:l===void 0})},browseObjects({indexName:e,browseParams:r,...t},a){return S({func:s=>this.browse({indexName:e,browseParams:{cursor:s?s.cursor:void 0,hitsPerPage:1e3,...r}},a),validate:s=>s.cursor===void 0,...t})},browseRules({indexName:e,searchRulesParams:r,...t},a){let s={...r,hitsPerPage:r?.hitsPerPage||1e3};return S({func:o=>this.searchRules({indexName:e,searchRulesParams:{...s,page:o?o.page+1:s.page||0}},a),validate:o=>o.hits.length<s.hitsPerPage,...t})},browseSynonyms({indexName:e,searchSynonymsParams:r,...t},a){let s={...r,page:r?.page||0,hitsPerPage:1e3};return S({func:o=>{let n=this.searchSynonyms({indexName:e,searchSynonymsParams:{...s,page:s.page}},a);return s.page+=1,n},validate:o=>o.hits.length<s.hitsPerPage,...t})},async chunkedBatch({indexName:e,objects:r,action:t="addObject",waitForTasks:a,batchSize:s=1e3},o){let n=[],m=[],l=r.entries();for(let[q,g]of l)n.push({action:t,body:g}),(n.length===s||q===r.length-1)&&(m.push(await this.batch({indexName:e,batchWriteParams:{requests:n}},o)),n=[]);if(a)for(let q of m)await this.waitForTask({indexName:e,taskID:q.taskID});return m},async saveObjects({indexName:e,objects:r,waitForTasks:t,batchSize:a},s){return await this.chunkedBatch({indexName:e,objects:r,action:"addObject",waitForTasks:t,batchSize:a},s)},async deleteObjects({indexName:e,objectIDs:r,waitForTasks:t,batchSize:a},s){return await this.chunkedBatch({indexName:e,objects:r.map(o=>({objectID:o})),action:"deleteObject",waitForTasks:t,batchSize:a},s)},async partialUpdateObjects({indexName:e,objects:r,createIfNotExists:t,waitForTasks:a,batchSize:s},o){return await this.chunkedBatch({indexName:e,objects:r,action:t?"partialUpdateObject":"partialUpdateObjectNoCreate",batchSize:s,waitForTasks:a},o)},async replaceAllObjects({indexName:e,objects:r,batchSize:t,scopes:a},s){let o=Math.floor(Math.random()*1e6)+1e5,n=`${e}_tmp_${o}`;a===void 0&&(a=["settings","rules","synonyms"]);try{let m=await this.operationIndex({indexName:e,operationIndexParams:{operation:"copy",destination:n,scope:a}},s),l=await this.chunkedBatch({indexName:n,objects:r,waitForTasks:!0,batchSize:t},s);await this.waitForTask({indexName:n,taskID:m.taskID}),m=await this.operationIndex({indexName:e,operationIndexParams:{operation:"copy",destination:n,scope:a}},s),await this.waitForTask({indexName:n,taskID:m.taskID});let q=await this.operationIndex({indexName:n,operationIndexParams:{operation:"move",destination:e}},s);return await this.waitForTask({indexName:n,taskID:q.taskID}),{copyOperationResponse:m,batchResponses:l,moveOperationResponse:q}}catch(m){throw await this.deleteIndex({indexName:n}),m}},async indexExists({indexName:e}){try{await this.getSettings({indexName:e})}catch(r){if(r instanceof I&&r.status===404)return!1;throw r}return!0},searchForHits(e,r){return this.search(e,r)},searchForFacets(e,r){return this.search(e,r)},addApiKey(e,r){if(!e)throw new Error("Parameter `apiKey` is required when calling `addApiKey`.");if(!e.acl)throw new Error("Parameter `apiKey.acl` is required when calling `addApiKey`.");let o={method:"POST",path:"/1/keys",queryParameters:{},headers:{},data:e};return c.request(o,r)},addOrUpdateObject({indexName:e,objectID:r,body:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `addOrUpdateObject`.");if(!r)throw new Error("Parameter `objectID` is required when calling `addOrUpdateObject`.");if(!t)throw new Error("Parameter `body` is required when calling `addOrUpdateObject`.");let m={method:"PUT",path:"/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{},data:t};return c.request(m,a)},appendSource(e,r){if(!e)throw new Error("Parameter `source` is required when calling `appendSource`.");if(!e.source)throw new Error("Parameter `source.source` is required when calling `appendSource`.");let o={method:"POST",path:"/1/security/sources/append",queryParameters:{},headers:{},data:e};return c.request(o,r)},assignUserId({xAlgoliaUserID:e,assignUserIdParams:r},t){if(!e)throw new Error("Parameter `xAlgoliaUserID` is required when calling `assignUserId`.");if(!r)throw new Error("Parameter `assignUserIdParams` is required when calling `assignUserId`.");if(!r.cluster)throw new Error("Parameter `assignUserIdParams.cluster` is required when calling `assignUserId`.");let a="/1/clusters/mapping",s={},o={};e!==void 0&&(s["X-Algolia-User-ID"]=e.toString());let n={method:"POST",path:a,queryParameters:o,headers:s,data:r};return c.request(n,t)},batch({indexName:e,batchWriteParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `batch`.");if(!r)throw new Error("Parameter `batchWriteParams` is required when calling `batch`.");if(!r.requests)throw new Error("Parameter `batchWriteParams.requests` is required when calling `batch`.");let n={method:"POST",path:"/1/indexes/{indexName}/batch".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)},batchAssignUserIds({xAlgoliaUserID:e,batchAssignUserIdsParams:r},t){if(!e)throw new Error("Parameter `xAlgoliaUserID` is required when calling `batchAssignUserIds`.");if(!r)throw new Error("Parameter `batchAssignUserIdsParams` is required when calling `batchAssignUserIds`.");if(!r.cluster)throw new Error("Parameter `batchAssignUserIdsParams.cluster` is required when calling `batchAssignUserIds`.");if(!r.users)throw new Error("Parameter `batchAssignUserIdsParams.users` is required when calling `batchAssignUserIds`.");let a="/1/clusters/mapping/batch",s={},o={};e!==void 0&&(s["X-Algolia-User-ID"]=e.toString());let n={method:"POST",path:a,queryParameters:o,headers:s,data:r};return c.request(n,t)},batchDictionaryEntries({dictionaryName:e,batchDictionaryEntriesParams:r},t){if(!e)throw new Error("Parameter `dictionaryName` is required when calling `batchDictionaryEntries`.");if(!r)throw new Error("Parameter `batchDictionaryEntriesParams` is required when calling `batchDictionaryEntries`.");if(!r.requests)throw new Error("Parameter `batchDictionaryEntriesParams.requests` is required when calling `batchDictionaryEntries`.");let n={method:"POST",path:"/1/dictionaries/{dictionaryName}/batch".replace("{dictionaryName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)},browse({indexName:e,browseParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `browse`.");let n={method:"POST",path:"/1/indexes/{indexName}/browse".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:!0};return c.request(n,t)},clearObjects({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `clearObjects`.");let o={method:"POST",path:"/1/indexes/{indexName}/clear".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},clearRules({indexName:e,forwardToReplicas:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `clearRules`.");let a="/1/indexes/{indexName}/rules/clear".replace("{indexName}",encodeURIComponent(e)),s={},o={};r!==void 0&&(o.forwardToReplicas=r.toString());let n={method:"POST",path:a,queryParameters:o,headers:s};return c.request(n,t)},clearSynonyms({indexName:e,forwardToReplicas:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `clearSynonyms`.");let a="/1/indexes/{indexName}/synonyms/clear".replace("{indexName}",encodeURIComponent(e)),s={},o={};r!==void 0&&(o.forwardToReplicas=r.toString());let n={method:"POST",path:a,queryParameters:o,headers:s};return c.request(n,t)},customDelete({path:e,parameters:r},t){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let n={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return c.request(n,t)},customGet({path:e,parameters:r},t){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let n={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return c.request(n,t)},customPost({path:e,parameters:r,body:t},a){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let m={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:t||{}};return c.request(m,a)},customPut({path:e,parameters:r,body:t},a){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let m={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:t||{}};return c.request(m,a)},deleteApiKey({key:e},r){if(!e)throw new Error("Parameter `key` is required when calling `deleteApiKey`.");let o={method:"DELETE",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},deleteBy({indexName:e,deleteByParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteBy`.");if(!r)throw new Error("Parameter `deleteByParams` is required when calling `deleteBy`.");let n={method:"POST",path:"/1/indexes/{indexName}/deleteByQuery".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)},deleteIndex({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteIndex`.");let o={method:"DELETE",path:"/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},deleteObject({indexName:e,objectID:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteObject`.");if(!r)throw new Error("Parameter `objectID` is required when calling `deleteObject`.");let n={method:"DELETE",path:"/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return c.request(n,t)},deleteRule({indexName:e,objectID:r,forwardToReplicas:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteRule`.");if(!r)throw new Error("Parameter `objectID` is required when calling `deleteRule`.");let s="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),o={},n={};t!==void 0&&(n.forwardToReplicas=t.toString());let m={method:"DELETE",path:s,queryParameters:n,headers:o};return c.request(m,a)},deleteSource({source:e},r){if(!e)throw new Error("Parameter `source` is required when calling `deleteSource`.");let o={method:"DELETE",path:"/1/security/sources/{source}".replace("{source}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},deleteSynonym({indexName:e,objectID:r,forwardToReplicas:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `deleteSynonym`.");if(!r)throw new Error("Parameter `objectID` is required when calling `deleteSynonym`.");let s="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),o={},n={};t!==void 0&&(n.forwardToReplicas=t.toString());let m={method:"DELETE",path:s,queryParameters:n,headers:o};return c.request(m,a)},getApiKey({key:e},r){if(!e)throw new Error("Parameter `key` is required when calling `getApiKey`.");let o={method:"GET",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},getAppTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `getAppTask`.");let o={method:"GET",path:"/1/task/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},getDictionaryLanguages(e){let s={method:"GET",path:"/1/dictionaries/*/languages",queryParameters:{},headers:{}};return c.request(s,e)},getDictionarySettings(e){let s={method:"GET",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{}};return c.request(s,e)},getLogs({offset:e,length:r,indexName:t,type:a}={},s=void 0){let o="/1/logs",n={},m={};e!==void 0&&(m.offset=e.toString()),r!==void 0&&(m.length=r.toString()),t!==void 0&&(m.indexName=t.toString()),a!==void 0&&(m.type=a.toString());let l={method:"GET",path:o,queryParameters:m,headers:n};return c.request(l,s)},getObject({indexName:e,objectID:r,attributesToRetrieve:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `getObject`.");if(!r)throw new Error("Parameter `objectID` is required when calling `getObject`.");let s="/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),o={},n={};t!==void 0&&(n.attributesToRetrieve=t.toString());let m={method:"GET",path:s,queryParameters:n,headers:o};return c.request(m,a)},getObjects(e,r){if(!e)throw new Error("Parameter `getObjectsParams` is required when calling `getObjects`.");if(!e.requests)throw new Error("Parameter `getObjectsParams.requests` is required when calling `getObjects`.");let o={method:"POST",path:"/1/indexes/*/objects",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return c.request(o,r)},getRule({indexName:e,objectID:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `getRule`.");if(!r)throw new Error("Parameter `objectID` is required when calling `getRule`.");let n={method:"GET",path:"/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return c.request(n,t)},getSettings({indexName:e},r){if(!e)throw new Error("Parameter `indexName` is required when calling `getSettings`.");let o={method:"GET",path:"/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},getSources(e){let s={method:"GET",path:"/1/security/sources",queryParameters:{},headers:{}};return c.request(s,e)},getSynonym({indexName:e,objectID:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `getSynonym`.");if(!r)throw new Error("Parameter `objectID` is required when calling `getSynonym`.");let n={method:"GET",path:"/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return c.request(n,t)},getTask({indexName:e,taskID:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `getTask`.");if(!r)throw new Error("Parameter `taskID` is required when calling `getTask`.");let n={method:"GET",path:"/1/indexes/{indexName}/task/{taskID}".replace("{indexName}",encodeURIComponent(e)).replace("{taskID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return c.request(n,t)},getTopUserIds(e){let s={method:"GET",path:"/1/clusters/mapping/top",queryParameters:{},headers:{}};return c.request(s,e)},getUserId({userID:e},r){if(!e)throw new Error("Parameter `userID` is required when calling `getUserId`.");let o={method:"GET",path:"/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},hasPendingMappings({getClusters:e}={},r=void 0){let t="/1/clusters/mapping/pending",a={},s={};e!==void 0&&(s.getClusters=e.toString());let o={method:"GET",path:t,queryParameters:s,headers:a};return c.request(o,r)},listApiKeys(e){let s={method:"GET",path:"/1/keys",queryParameters:{},headers:{}};return c.request(s,e)},listClusters(e){let s={method:"GET",path:"/1/clusters",queryParameters:{},headers:{}};return c.request(s,e)},listIndices({page:e,hitsPerPage:r}={},t=void 0){let a="/1/indexes",s={},o={};e!==void 0&&(o.page=e.toString()),r!==void 0&&(o.hitsPerPage=r.toString());let n={method:"GET",path:a,queryParameters:o,headers:s};return c.request(n,t)},listUserIds({page:e,hitsPerPage:r}={},t=void 0){let a="/1/clusters/mapping",s={},o={};e!==void 0&&(o.page=e.toString()),r!==void 0&&(o.hitsPerPage=r.toString());let n={method:"GET",path:a,queryParameters:o,headers:s};return c.request(n,t)},multipleBatch(e,r){if(!e)throw new Error("Parameter `batchParams` is required when calling `multipleBatch`.");if(!e.requests)throw new Error("Parameter `batchParams.requests` is required when calling `multipleBatch`.");let o={method:"POST",path:"/1/indexes/*/batch",queryParameters:{},headers:{},data:e};return c.request(o,r)},operationIndex({indexName:e,operationIndexParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `operationIndex`.");if(!r)throw new Error("Parameter `operationIndexParams` is required when calling `operationIndex`.");if(!r.operation)throw new Error("Parameter `operationIndexParams.operation` is required when calling `operationIndex`.");if(!r.destination)throw new Error("Parameter `operationIndexParams.destination` is required when calling `operationIndex`.");let n={method:"POST",path:"/1/indexes/{indexName}/operation".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)},partialUpdateObject({indexName:e,objectID:r,attributesToUpdate:t,createIfNotExists:a},s){if(!e)throw new Error("Parameter `indexName` is required when calling `partialUpdateObject`.");if(!r)throw new Error("Parameter `objectID` is required when calling `partialUpdateObject`.");if(!t)throw new Error("Parameter `attributesToUpdate` is required when calling `partialUpdateObject`.");let o="/1/indexes/{indexName}/{objectID}/partial".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),n={},m={};a!==void 0&&(m.createIfNotExists=a.toString());let l={method:"POST",path:o,queryParameters:m,headers:n,data:t};return c.request(l,s)},removeUserId({userID:e},r){if(!e)throw new Error("Parameter `userID` is required when calling `removeUserId`.");let o={method:"DELETE",path:"/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},replaceSources({source:e},r){if(!e)throw new Error("Parameter `source` is required when calling `replaceSources`.");let o={method:"PUT",path:"/1/security/sources",queryParameters:{},headers:{},data:e};return c.request(o,r)},restoreApiKey({key:e},r){if(!e)throw new Error("Parameter `key` is required when calling `restoreApiKey`.");let o={method:"POST",path:"/1/keys/{key}/restore".replace("{key}",encodeURIComponent(e)),queryParameters:{},headers:{}};return c.request(o,r)},saveObject({indexName:e,body:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `saveObject`.");if(!r)throw new Error("Parameter `body` is required when calling `saveObject`.");let n={method:"POST",path:"/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)},saveRule({indexName:e,objectID:r,rule:t,forwardToReplicas:a},s){if(!e)throw new Error("Parameter `indexName` is required when calling `saveRule`.");if(!r)throw new Error("Parameter `objectID` is required when calling `saveRule`.");if(!t)throw new Error("Parameter `rule` is required when calling `saveRule`.");if(!t.objectID)throw new Error("Parameter `rule.objectID` is required when calling `saveRule`.");if(!t.consequence)throw new Error("Parameter `rule.consequence` is required when calling `saveRule`.");let o="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),n={},m={};a!==void 0&&(m.forwardToReplicas=a.toString());let l={method:"PUT",path:o,queryParameters:m,headers:n,data:t};return c.request(l,s)},saveRules({indexName:e,rules:r,forwardToReplicas:t,clearExistingRules:a},s){if(!e)throw new Error("Parameter `indexName` is required when calling `saveRules`.");if(!r)throw new Error("Parameter `rules` is required when calling `saveRules`.");let o="/1/indexes/{indexName}/rules/batch".replace("{indexName}",encodeURIComponent(e)),n={},m={};t!==void 0&&(m.forwardToReplicas=t.toString()),a!==void 0&&(m.clearExistingRules=a.toString());let l={method:"POST",path:o,queryParameters:m,headers:n,data:r};return c.request(l,s)},saveSynonym({indexName:e,objectID:r,synonymHit:t,forwardToReplicas:a},s){if(!e)throw new Error("Parameter `indexName` is required when calling `saveSynonym`.");if(!r)throw new Error("Parameter `objectID` is required when calling `saveSynonym`.");if(!t)throw new Error("Parameter `synonymHit` is required when calling `saveSynonym`.");if(!t.objectID)throw new Error("Parameter `synonymHit.objectID` is required when calling `saveSynonym`.");if(!t.type)throw new Error("Parameter `synonymHit.type` is required when calling `saveSynonym`.");let o="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(e)).replace("{objectID}",encodeURIComponent(r)),n={},m={};a!==void 0&&(m.forwardToReplicas=a.toString());let l={method:"PUT",path:o,queryParameters:m,headers:n,data:t};return c.request(l,s)},saveSynonyms({indexName:e,synonymHit:r,forwardToReplicas:t,replaceExistingSynonyms:a},s){if(!e)throw new Error("Parameter `indexName` is required when calling `saveSynonyms`.");if(!r)throw new Error("Parameter `synonymHit` is required when calling `saveSynonyms`.");let o="/1/indexes/{indexName}/synonyms/batch".replace("{indexName}",encodeURIComponent(e)),n={},m={};t!==void 0&&(m.forwardToReplicas=t.toString()),a!==void 0&&(m.replaceExistingSynonyms=a.toString());let l={method:"POST",path:o,queryParameters:m,headers:n,data:r};return c.request(l,s)},search(e,r){if(e&&Array.isArray(e)&&(e={requests:e.map(({params:m,...l})=>l.type==="facet"?{...l,...m,type:"facet"}:{...l,...m,facet:void 0,maxFacetHits:void 0,facetQuery:void 0})}),!e)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!e.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");let o={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return c.request(o,r)},searchDictionaryEntries({dictionaryName:e,searchDictionaryEntriesParams:r},t){if(!e)throw new Error("Parameter `dictionaryName` is required when calling `searchDictionaryEntries`.");if(!r)throw new Error("Parameter `searchDictionaryEntriesParams` is required when calling `searchDictionaryEntries`.");if(!r.query)throw new Error("Parameter `searchDictionaryEntriesParams.query` is required when calling `searchDictionaryEntries`.");let n={method:"POST",path:"/1/dictionaries/{dictionaryName}/search".replace("{dictionaryName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r,useReadTransporter:!0,cacheable:!0};return c.request(n,t)},searchForFacetValues({indexName:e,facetName:r,searchForFacetValuesRequest:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `searchForFacetValues`.");if(!r)throw new Error("Parameter `facetName` is required when calling `searchForFacetValues`.");let m={method:"POST",path:"/1/indexes/{indexName}/facets/{facetName}/query".replace("{indexName}",encodeURIComponent(e)).replace("{facetName}",encodeURIComponent(r)),queryParameters:{},headers:{},data:t||{},useReadTransporter:!0,cacheable:!0};return c.request(m,a)},searchRules({indexName:e,searchRulesParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `searchRules`.");let n={method:"POST",path:"/1/indexes/{indexName}/rules/search".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:!0,cacheable:!0};return c.request(n,t)},searchSingleIndex({indexName:e,searchParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `searchSingleIndex`.");let n={method:"POST",path:"/1/indexes/{indexName}/query".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:!0,cacheable:!0};return c.request(n,t)},searchSynonyms({indexName:e,searchSynonymsParams:r},t){if(!e)throw new Error("Parameter `indexName` is required when calling `searchSynonyms`.");let n={method:"POST",path:"/1/indexes/{indexName}/synonyms/search".replace("{indexName}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{},useReadTransporter:!0,cacheable:!0};return c.request(n,t)},searchUserIds(e,r){if(!e)throw new Error("Parameter `searchUserIdsParams` is required when calling `searchUserIds`.");if(!e.query)throw new Error("Parameter `searchUserIdsParams.query` is required when calling `searchUserIds`.");let o={method:"POST",path:"/1/clusters/mapping/search",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return c.request(o,r)},setDictionarySettings(e,r){if(!e)throw new Error("Parameter `dictionarySettingsParams` is required when calling `setDictionarySettings`.");if(!e.disableStandardEntries)throw new Error("Parameter `dictionarySettingsParams.disableStandardEntries` is required when calling `setDictionarySettings`.");let o={method:"PUT",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{},data:e};return c.request(o,r)},setSettings({indexName:e,indexSettings:r,forwardToReplicas:t},a){if(!e)throw new Error("Parameter `indexName` is required when calling `setSettings`.");if(!r)throw new Error("Parameter `indexSettings` is required when calling `setSettings`.");let s="/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(e)),o={},n={};t!==void 0&&(n.forwardToReplicas=t.toString());let m={method:"PUT",path:s,queryParameters:n,headers:o,data:r};return c.request(m,a)},updateApiKey({key:e,apiKey:r},t){if(!e)throw new Error("Parameter `key` is required when calling `updateApiKey`.");if(!r)throw new Error("Parameter `apiKey` is required when calling `updateApiKey`.");if(!r.acl)throw new Error("Parameter `apiKey.acl` is required when calling `updateApiKey`.");let n={method:"PUT",path:"/1/keys/{key}".replace("{key}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return c.request(n,t)}}}function Xs(u,i,d){if(!u||typeof u!="string")throw new Error("`appId` is missing.");if(!i||typeof i!="string")throw new Error("`apiKey` is missing.");return V({appId:u,apiKey:i,timeouts:{connect:1e3,read:2e3,write:3e4},logger:_(),requester:Q(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:T(),requestsCache:T({serializable:!1}),hostsCache:O({caches:[L({key:`${A}-${u}`}),T()]}),...d})}export{A as apiClientVersion,Xs as searchClient};
//# sourceMappingURL=browser.min.js.map