{"version": 3, "sources": ["../../../requester-browser-xhr/src/createXhrRequester.ts", "../../../client-common/src/cache/createBrowserLocalStorageCache.ts", "../../../client-common/src/cache/createNullCache.ts", "../../../client-common/src/cache/createFallbackableCache.ts", "../../../client-common/src/cache/createMemoryCache.ts", "../../../client-common/src/constants.ts", "../../../client-common/src/createAlgoliaAgent.ts", "../../../client-common/src/createAuth.ts", "../../../client-common/src/createIterablePromise.ts", "../../../client-common/src/getAlgoliaAgent.ts", "../../../client-common/src/logger/createNullLogger.ts", "../../../client-common/src/transporter/createStatefulHost.ts", "../../../client-common/src/transporter/errors.ts", "../../../client-common/src/transporter/helpers.ts", "../../../client-common/src/transporter/responses.ts", "../../../client-common/src/transporter/stackTrace.ts", "../../../client-common/src/transporter/createTransporter.ts", "../../../client-common/src/types/logger.ts", "../../src/searchClient.ts", "../../builds/browser.ts"], "sourcesContent": ["import type { EndRequest, Requester, Response } from '@algolia/client-common';\n\ntype Timeout = ReturnType<typeof setTimeout>;\n\nexport function createXhrRequester(): Requester {\n  function send(request: EndRequest): Promise<Response> {\n    return new Promise((resolve) => {\n      const baseRequester = new XMLHttpRequest();\n      baseRequester.open(request.method, request.url, true);\n\n      Object.keys(request.headers).forEach((key) => baseRequester.setRequestHeader(key, request.headers[key]));\n\n      const createTimeout = (timeout: number, content: string): Timeout => {\n        return setTimeout(() => {\n          baseRequester.abort();\n\n          resolve({\n            status: 0,\n            content,\n            isTimedOut: true,\n          });\n        }, timeout);\n      };\n\n      const connectTimeout = createTimeout(request.connectTimeout, 'Connection timeout');\n\n      let responseTimeout: Timeout | undefined;\n\n      baseRequester.onreadystatechange = (): void => {\n        if (baseRequester.readyState > baseRequester.OPENED && responseTimeout === undefined) {\n          clearTimeout(connectTimeout);\n\n          responseTimeout = createTimeout(request.responseTimeout, 'Socket timeout');\n        }\n      };\n\n      baseRequester.onerror = (): void => {\n        // istanbul ignore next\n        if (baseRequester.status === 0) {\n          clearTimeout(connectTimeout);\n          clearTimeout(responseTimeout!);\n\n          resolve({\n            content: baseRequester.responseText || 'Network request failed',\n            status: baseRequester.status,\n            isTimedOut: false,\n          });\n        }\n      };\n\n      baseRequester.onload = (): void => {\n        clearTimeout(connectTimeout);\n        clearTimeout(responseTimeout!);\n\n        resolve({\n          content: baseRequester.responseText,\n          status: baseRequester.status,\n          isTimedOut: false,\n        });\n      };\n\n      baseRequester.send(request.data);\n    });\n  }\n\n  return { send };\n}\n", "import type { BrowserLocalStorageCacheItem, BrowserLocalStorageOptions, Cache, CacheEvents } from '../types';\n\nexport function createBrowserLocalStorageCache(options: BrowserLocalStorageOptions): Cache {\n  let storage: Storage;\n  // We've changed the namespace to avoid conflicts with v4, as this version is a huge breaking change\n  const namespaceKey = `algolia-client-js-${options.key}`;\n\n  function getStorage(): Storage {\n    if (storage === undefined) {\n      storage = options.localStorage || window.localStorage;\n    }\n\n    return storage;\n  }\n\n  function getNamespace<TValue>(): Record<string, TValue> {\n    return JSON.parse(getStorage().getItem(namespaceKey) || '{}');\n  }\n\n  function setNamespace(namespace: Record<string, any>): void {\n    getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n  }\n\n  function removeOutdatedCacheItems(): void {\n    const timeToLive = options.timeToLive ? options.timeToLive * 1000 : null;\n    const namespace = getNamespace<BrowserLocalStorageCacheItem>();\n\n    const filteredNamespaceWithoutOldFormattedCacheItems = Object.fromEntries(\n      Object.entries(namespace).filter(([, cacheItem]) => {\n        return cacheItem.timestamp !== undefined;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutOldFormattedCacheItems);\n\n    if (!timeToLive) {\n      return;\n    }\n\n    const filteredNamespaceWithoutExpiredItems = Object.fromEntries(\n      Object.entries(filteredNamespaceWithoutOldFormattedCacheItems).filter(([, cacheItem]) => {\n        const currentTimestamp = new Date().getTime();\n        const isExpired = cacheItem.timestamp + timeToLive < currentTimestamp;\n\n        return !isExpired;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutExpiredItems);\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: () => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return Promise.resolve()\n        .then(() => {\n          removeOutdatedCacheItems();\n\n          return getNamespace<Promise<BrowserLocalStorageCacheItem>>()[JSON.stringify(key)];\n        })\n        .then((value) => {\n          return Promise.all([value ? value.value : defaultValue(), value !== undefined]);\n        })\n        .then(([value, exists]) => {\n          return Promise.all([value, exists || events.miss(value)]);\n        })\n        .then(([value]) => value);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        namespace[JSON.stringify(key)] = {\n          timestamp: new Date().getTime(),\n          value,\n        };\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n\n        return value;\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        delete namespace[JSON.stringify(key)];\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n      });\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve().then(() => {\n        getStorage().removeItem(namespaceKey);\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents } from '../types';\n\nexport function createNullCache(): Cache {\n  return {\n    get<TValue>(\n      _key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const value = defaultValue();\n\n      return value.then((result) => Promise.all([result, events.miss(result)])).then(([result]) => result);\n    },\n\n    set<TValue>(_key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve(value);\n    },\n\n    delete(_key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Cache, CacheEvents, FallbackableCacheOptions } from '../types';\nimport { createNullCache } from './createNullCache';\n\nexport function createFallbackableCache(options: FallbackableCacheOptions): Cache {\n  const caches = [...options.caches];\n  const current = caches.shift();\n\n  if (current === undefined) {\n    return createNullCache();\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return current.get(key, defaultValue, events).catch(() => {\n        return createFallbackableCache({ caches }).get(key, defaultValue, events);\n      });\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return current.set(key, value).catch(() => {\n        return createFallbackableCache({ caches }).set(key, value);\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return current.delete(key).catch(() => {\n        return createFallbackableCache({ caches }).delete(key);\n      });\n    },\n\n    clear(): Promise<void> {\n      return current.clear().catch(() => {\n        return createFallbackableCache({ caches }).clear();\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents, MemoryCacheOptions } from '../types';\n\nexport function createMemoryCache(options: MemoryCacheOptions = { serializable: true }): Cache {\n  let cache: Record<string, any> = {};\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const keyAsString = JSON.stringify(key);\n\n      if (keyAsString in cache) {\n        return Promise.resolve(options.serializable ? JSON.parse(cache[keyAsString]) : cache[keyAsString]);\n      }\n\n      const promise = defaultValue();\n\n      return promise.then((value: TValue) => events.miss(value)).then(() => promise);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      cache[JSON.stringify(key)] = options.serializable ? JSON.stringify(value) : value;\n\n      return Promise.resolve(value);\n    },\n\n    delete(key: Record<string, unknown> | string): Promise<void> {\n      delete cache[JSON.stringify(key)];\n\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      cache = {};\n\n      return Promise.resolve();\n    },\n  };\n}\n", "export const DEFAULT_CONNECT_TIMEOUT_BROWSER = 1000;\nexport const DEFAULT_READ_TIMEOUT_BROWSER = 2000;\nexport const DEFAULT_WRITE_TIMEOUT_BROWSER = 30000;\n\nexport const DEFAULT_CONNECT_TIMEOUT_NODE = 2000;\nexport const DEFAULT_READ_TIMEOUT_NODE = 5000;\nexport const DEFAULT_WRITE_TIMEOUT_NODE = 30000;\n", "import type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport function createAlgoliaAgent(version: string): AlgoliaAgent {\n  const algoliaAgent = {\n    value: `Algolia for JavaScript (${version})`,\n    add(options: AlgoliaAgentOptions): AlgoliaAgent {\n      const addedAlgoliaAgent = `; ${options.segment}${options.version !== undefined ? ` (${options.version})` : ''}`;\n\n      if (algoliaAgent.value.indexOf(addedAlgoliaAgent) === -1) {\n        algoliaAgent.value = `${algoliaAgent.value}${addedAlgoliaAgent}`;\n      }\n\n      return algoliaAgent;\n    },\n  };\n\n  return algoliaAgent;\n}\n", "import type { AuthMode, Headers, QueryParameters } from './types';\n\nexport function createAuth(\n  appId: string,\n  apiKey: string,\n  authMode: AuthMode = 'WithinHeaders',\n): {\n  readonly headers: () => Headers;\n  readonly queryParameters: () => QueryParameters;\n} {\n  const credentials = {\n    'x-algolia-api-key': apiKey,\n    'x-algolia-application-id': appId,\n  };\n\n  return {\n    headers(): Headers {\n      return authMode === 'WithinHeaders' ? credentials : {};\n    },\n\n    queryParameters(): QueryParameters {\n      return authMode === 'WithinQueryParameters' ? credentials : {};\n    },\n  };\n}\n", "import type { CreateIterablePromise } from './types/createIterablePromise';\n\n/**\n * Helper: Returns the promise of a given `func` to iterate on, based on a given `validate` condition.\n *\n * @param createIterator - The createIterator options.\n * @param createIterator.func - The function to run, which returns a promise.\n * @param createIterator.validate - The validator function. It receives the resolved return of `func`.\n * @param createIterator.aggregator - The function that runs right after the `func` method has been executed, allows you to do anything with the response before `validate`.\n * @param createIterator.error - The `validate` condition to throw an error, and its message.\n * @param createIterator.timeout - The function to decide how long to wait between iterations.\n */\nexport function createIterablePromise<TResponse>({\n  func,\n  validate,\n  aggregator,\n  error,\n  timeout = (): number => 0,\n}: CreateIterablePromise<TResponse>): Promise<TResponse> {\n  const retry = (previousResponse?: TResponse | undefined): Promise<TResponse> => {\n    return new Promise<TResponse>((resolve, reject) => {\n      func(previousResponse)\n        .then(async (response) => {\n          if (aggregator) {\n            await aggregator(response);\n          }\n\n          if (await validate(response)) {\n            return resolve(response);\n          }\n\n          if (error && (await error.validate(response))) {\n            return reject(new Error(await error.message(response)));\n          }\n\n          return setTimeout(\n            () => {\n              retry(response).then(resolve).catch(reject);\n            },\n            await timeout(),\n          );\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  };\n\n  return retry();\n}\n", "import { createAlgoliaAgent } from './createAlgoliaAgent';\nimport type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport type GetAlgoliaAgent = {\n  algoliaAgents: AlgoliaAgentOptions[];\n  client: string;\n  version: string;\n};\n\nexport function getAlgoliaAgent({ algoliaAgents, client, version }: GetAlgoliaAgent): AlgoliaAgent {\n  const defaultAlgoliaAgent = createAlgoliaAgent(version).add({\n    segment: client,\n    version,\n  });\n\n  algoliaAgents.forEach((algoliaAgent) => defaultAlgoliaAgent.add(algoliaAgent));\n\n  return defaultAlgoliaAgent;\n}\n", "import type { Logger } from '../types/logger';\n\nexport function createNullLogger(): Logger {\n  return {\n    debug(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    info(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    error(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Host, StatefulHost } from '../types';\n\n// By default, API Clients at Algolia have expiration delay of 5 mins.\n// In the JavaScript client, we have 2 mins.\nconst EXPIRATION_DELAY = 2 * 60 * 1000;\n\nexport function createStatefulHost(host: Host, status: StatefulHost['status'] = 'up'): StatefulHost {\n  const lastUpdate = Date.now();\n\n  function isUp(): boolean {\n    return status === 'up' || Date.now() - lastUpdate > EXPIRATION_DELAY;\n  }\n\n  function isTimedOut(): boolean {\n    return status === 'timed out' && Date.now() - lastUpdate <= EXPIRATION_DELAY;\n  }\n\n  return { ...host, status, lastUpdate, isUp, isTimedOut };\n}\n", "import type { Response, StackFrame } from '../types';\n\nexport class AlgoliaError extends Error {\n  override name: string = 'AlgoliaError';\n\n  constructor(message: string, name: string) {\n    super(message);\n\n    if (name) {\n      this.name = name;\n    }\n  }\n}\n\nexport class IndexNotFoundError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} does not exist`, 'IndexNotFoundError');\n  }\n}\n\nexport class IndicesInSameAppError extends AlgoliaError {\n  constructor() {\n    super('Indices are in the same application. Use operationIndex instead.', 'IndicesInSameAppError');\n  }\n}\n\nexport class IndexAlreadyExistsError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} index already exists.`, 'IndexAlreadyExistsError');\n  }\n}\n\nexport class ErrorWithStackTrace extends AlgoliaError {\n  stackTrace: StackFrame[];\n\n  constructor(message: string, stackTrace: StackFrame[], name: string) {\n    super(message, name);\n    // the array and object should be frozen to reflect the stackTrace at the time of the error\n    this.stackTrace = stackTrace;\n  }\n}\n\nexport class RetryError extends ErrorWithStackTrace {\n  constructor(stackTrace: StackFrame[]) {\n    super(\n      'Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.',\n      stackTrace,\n      'RetryError',\n    );\n  }\n}\n\nexport class ApiError extends ErrorWithStackTrace {\n  status: number;\n\n  constructor(message: string, status: number, stackTrace: StackFrame[], name = 'ApiError') {\n    super(message, stackTrace, name);\n    this.status = status;\n  }\n}\n\nexport class DeserializationError extends AlgoliaError {\n  response: Response;\n\n  constructor(message: string, response: Response) {\n    super(message, 'DeserializationError');\n    this.response = response;\n  }\n}\n\nexport type DetailedErrorWithMessage = {\n  message: string;\n  label: string;\n};\n\nexport type DetailedErrorWithTypeID = {\n  id: string;\n  type: string;\n  name?: string | undefined;\n};\n\nexport type DetailedError = {\n  code: string;\n  details?: DetailedErrorWithMessage[] | DetailedErrorWithTypeID[] | undefined;\n};\n\n// DetailedApiError is only used by the ingestion client to return more informative error, other clients will use ApiClient.\nexport class DetailedApiError extends ApiError {\n  error: DetailedError;\n\n  constructor(message: string, status: number, error: DetailedError, stackTrace: StackFrame[]) {\n    super(message, status, stackTrace, 'DetailedApiError');\n    this.error = error;\n  }\n}\n", "import type { Headers, Host, QueryParameters, Request, RequestOptions, Response, StackFrame } from '../types';\nimport { ApiError, DeserializationError, DetailedApiError } from './errors';\n\nexport function shuffle<TData>(array: TData[]): TData[] {\n  const shuffledArray = array;\n\n  for (let c = array.length - 1; c > 0; c--) {\n    const b = Math.floor(Math.random() * (c + 1));\n    const a = array[c];\n\n    shuffledArray[c] = array[b];\n    shuffledArray[b] = a;\n  }\n\n  return shuffledArray;\n}\n\nexport function serializeUrl(host: Host, path: string, queryParameters: QueryParameters): string {\n  const queryParametersAsString = serializeQueryParameters(queryParameters);\n  let url = `${host.protocol}://${host.url}${host.port ? `:${host.port}` : ''}/${\n    path.charAt(0) === '/' ? path.substring(1) : path\n  }`;\n\n  if (queryParametersAsString.length) {\n    url += `?${queryParametersAsString}`;\n  }\n\n  return url;\n}\n\nexport function serializeQueryParameters(parameters: QueryParameters): string {\n  return Object.keys(parameters)\n    .filter((key) => parameters[key] !== undefined)\n    .sort()\n    .map(\n      (key) =>\n        `${key}=${encodeURIComponent(\n          Object.prototype.toString.call(parameters[key]) === '[object Array]'\n            ? parameters[key].join(',')\n            : parameters[key],\n        ).replace(/\\+/g, '%20')}`,\n    )\n    .join('&');\n}\n\nexport function serializeData(request: Request, requestOptions: RequestOptions): string | undefined {\n  if (request.method === 'GET' || (request.data === undefined && requestOptions.data === undefined)) {\n    return undefined;\n  }\n\n  const data = Array.isArray(request.data) ? request.data : { ...request.data, ...requestOptions.data };\n\n  return JSON.stringify(data);\n}\n\nexport function serializeHeaders(\n  baseHeaders: Headers,\n  requestHeaders: Headers,\n  requestOptionsHeaders?: Headers | undefined,\n): Headers {\n  const headers: Headers = {\n    Accept: 'application/json',\n    ...baseHeaders,\n    ...requestHeaders,\n    ...requestOptionsHeaders,\n  };\n  const serializedHeaders: Headers = {};\n\n  Object.keys(headers).forEach((header) => {\n    const value = headers[header];\n    serializedHeaders[header.toLowerCase()] = value;\n  });\n\n  return serializedHeaders;\n}\n\nexport function deserializeSuccess<TObject>(response: Response): TObject {\n  try {\n    return JSON.parse(response.content);\n  } catch (e) {\n    throw new DeserializationError((e as Error).message, response);\n  }\n}\n\nexport function deserializeFailure({ content, status }: Response, stackFrame: StackFrame[]): Error {\n  try {\n    const parsed = JSON.parse(content);\n    if ('error' in parsed) {\n      return new DetailedApiError(parsed.message, status, parsed.error, stackFrame);\n    }\n    return new ApiError(parsed.message, status, stackFrame);\n  } catch {\n    // ..\n  }\n  return new ApiError(content, status, stackFrame);\n}\n", "import type { Response } from '../types';\n\nexport function isNetworkError({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return !isTimedOut && ~~status === 0;\n}\n\nexport function isRetryable({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return isTimedOut || isNetworkError({ isTimedOut, status }) || (~~(status / 100) !== 2 && ~~(status / 100) !== 4);\n}\n\nexport function isSuccess({ status }: Pick<Response, 'status'>): boolean {\n  return ~~(status / 100) === 2;\n}\n", "import type { Head<PERSON>, StackFrame } from '../types';\n\nexport function stackTraceWithoutCredentials(stackTrace: StackFrame[]): StackFrame[] {\n  return stackTrace.map((stackFrame) => stackFrameWithoutCredentials(stackFrame));\n}\n\nexport function stackFrameWithoutCredentials(stackFrame: StackFrame): StackFrame {\n  const modifiedHeaders: Headers = stackFrame.request.headers['x-algolia-api-key']\n    ? { 'x-algolia-api-key': '*****' }\n    : {};\n\n  return {\n    ...stackFrame,\n    request: {\n      ...stackFrame.request,\n      headers: {\n        ...stackFrame.request.headers,\n        ...modifiedHeaders,\n      },\n    },\n  };\n}\n", "import type {\n  EndRequest,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n  Response,\n  StackFrame,\n  Transporter,\n  TransporterOptions,\n} from '../types';\nimport { createStatefulHost } from './createStatefulHost';\nimport { RetryError } from './errors';\nimport { deserializeFailure, deserializeSuccess, serializeData, serializeHeaders, serializeUrl } from './helpers';\nimport { isRetryable, isSuccess } from './responses';\nimport { stackFrameWithoutCredentials, stackTraceWithoutCredentials } from './stackTrace';\n\ntype RetryableOptions = {\n  hosts: Host[];\n  getTimeout: (retryCount: number, timeout: number) => number;\n};\n\nexport function createTransporter({\n  hosts,\n  hostsCache,\n  baseHeaders,\n  logger,\n  baseQueryParameters,\n  algoliaAgent,\n  timeouts,\n  requester,\n  requestsCache,\n  responsesCache,\n}: TransporterOptions): Transporter {\n  async function createRetryableOptions(compatibleHosts: Host[]): Promise<RetryableOptions> {\n    const statefulHosts = await Promise.all(\n      compatibleHosts.map((compatibleHost) => {\n        return hostsCache.get(compatibleHost, () => {\n          return Promise.resolve(createStatefulHost(compatibleHost));\n        });\n      }),\n    );\n    const hostsUp = statefulHosts.filter((host) => host.isUp());\n    const hostsTimedOut = statefulHosts.filter((host) => host.isTimedOut());\n\n    // Note, we put the hosts that previously timed out on the end of the list.\n    const hostsAvailable = [...hostsUp, ...hostsTimedOut];\n    const compatibleHostsAvailable = hostsAvailable.length > 0 ? hostsAvailable : compatibleHosts;\n\n    return {\n      hosts: compatibleHostsAvailable,\n      getTimeout(timeoutsCount: number, baseTimeout: number): number {\n        /**\n         * Imagine that you have 4 hosts, if timeouts will increase\n         * on the following way: 1 (timed out) > 4 (timed out) > 5 (200).\n         *\n         * Note that, the very next request, we start from the previous timeout.\n         *\n         *  5 (timed out) > 6 (timed out) > 7 ...\n         *\n         * This strategy may need to be reviewed, but is the strategy on the our\n         * current v3 version.\n         */\n        const timeoutMultiplier =\n          hostsTimedOut.length === 0 && timeoutsCount === 0 ? 1 : hostsTimedOut.length + 3 + timeoutsCount;\n\n        return timeoutMultiplier * baseTimeout;\n      },\n    };\n  }\n\n  async function retryableRequest<TResponse>(\n    request: Request,\n    requestOptions: RequestOptions,\n    isRead = true,\n  ): Promise<TResponse> {\n    const stackTrace: StackFrame[] = [];\n\n    /**\n     * First we prepare the payload that do not depend from hosts.\n     */\n    const data = serializeData(request, requestOptions);\n    const headers = serializeHeaders(baseHeaders, request.headers, requestOptions.headers);\n\n    // On `GET`, the data is proxied to query parameters.\n    const dataQueryParameters: QueryParameters =\n      request.method === 'GET'\n        ? {\n            ...request.data,\n            ...requestOptions.data,\n          }\n        : {};\n\n    const queryParameters: QueryParameters = {\n      ...baseQueryParameters,\n      ...request.queryParameters,\n      ...dataQueryParameters,\n    };\n\n    if (algoliaAgent.value) {\n      queryParameters['x-algolia-agent'] = algoliaAgent.value;\n    }\n\n    if (requestOptions && requestOptions.queryParameters) {\n      for (const key of Object.keys(requestOptions.queryParameters)) {\n        // We want to keep `undefined` and `null` values,\n        // but also avoid stringifying `object`s, as they are\n        // handled in the `serializeUrl` step right after.\n        if (\n          !requestOptions.queryParameters[key] ||\n          Object.prototype.toString.call(requestOptions.queryParameters[key]) === '[object Object]'\n        ) {\n          queryParameters[key] = requestOptions.queryParameters[key];\n        } else {\n          queryParameters[key] = requestOptions.queryParameters[key].toString();\n        }\n      }\n    }\n\n    let timeoutsCount = 0;\n\n    const retry = async (\n      retryableHosts: Host[],\n      getTimeout: (timeoutsCount: number, timeout: number) => number,\n    ): Promise<TResponse> => {\n      /**\n       * We iterate on each host, until there is no host left.\n       */\n      const host = retryableHosts.pop();\n      if (host === undefined) {\n        throw new RetryError(stackTraceWithoutCredentials(stackTrace));\n      }\n\n      const timeout = { ...timeouts, ...requestOptions.timeouts };\n\n      const payload: EndRequest = {\n        data,\n        headers,\n        method: request.method,\n        url: serializeUrl(host, request.path, queryParameters),\n        connectTimeout: getTimeout(timeoutsCount, timeout.connect),\n        responseTimeout: getTimeout(timeoutsCount, isRead ? timeout.read : timeout.write),\n      };\n\n      /**\n       * The stackFrame is pushed to the stackTrace so we\n       * can have information about onRetry and onFailure\n       * decisions.\n       */\n      const pushToStackTrace = (response: Response): StackFrame => {\n        const stackFrame: StackFrame = {\n          request: payload,\n          response,\n          host,\n          triesLeft: retryableHosts.length,\n        };\n\n        stackTrace.push(stackFrame);\n\n        return stackFrame;\n      };\n\n      const response = await requester.send(payload);\n\n      if (isRetryable(response)) {\n        const stackFrame = pushToStackTrace(response);\n\n        // If response is a timeout, we increase the number of timeouts so we can increase the timeout later.\n        if (response.isTimedOut) {\n          timeoutsCount++;\n        }\n        /**\n         * Failures are individually sent to the logger, allowing\n         * the end user to debug / store stack frames even\n         * when a retry error does not happen.\n         */\n        logger.info('Retryable failure', stackFrameWithoutCredentials(stackFrame));\n\n        /**\n         * We also store the state of the host in failure cases. If the host, is\n         * down it will remain down for the next 2 minutes. In a timeout situation,\n         * this host will be added end of the list of hosts on the next request.\n         */\n        await hostsCache.set(host, createStatefulHost(host, response.isTimedOut ? 'timed out' : 'down'));\n\n        return retry(retryableHosts, getTimeout);\n      }\n\n      if (isSuccess(response)) {\n        return deserializeSuccess(response);\n      }\n\n      pushToStackTrace(response);\n      throw deserializeFailure(response, stackTrace);\n    };\n\n    /**\n     * Finally, for each retryable host perform request until we got a non\n     * retryable response. Some notes here:\n     *\n     * 1. The reverse here is applied so we can apply a `pop` later on => more performant.\n     * 2. We also get from the retryable options a timeout multiplier that is tailored\n     * for the current context.\n     */\n    const compatibleHosts = hosts.filter(\n      (host) => host.accept === 'readWrite' || (isRead ? host.accept === 'read' : host.accept === 'write'),\n    );\n    const options = await createRetryableOptions(compatibleHosts);\n\n    return retry([...options.hosts].reverse(), options.getTimeout);\n  }\n\n  function createRequest<TResponse>(request: Request, requestOptions: RequestOptions = {}): Promise<TResponse> {\n    /**\n     * A read request is either a `GET` request, or a request that we make\n     * via the `read` transporter (e.g. `search`).\n     */\n    const isRead = request.useReadTransporter || request.method === 'GET';\n    if (!isRead) {\n      /**\n       * On write requests, no cache mechanisms are applied, and we\n       * proxy the request immediately to the requester.\n       */\n      return retryableRequest<TResponse>(request, requestOptions, isRead);\n    }\n\n    const createRetryableRequest = (): Promise<TResponse> => {\n      /**\n       * Then, we prepare a function factory that contains the construction of\n       * the retryable request. At this point, we may *not* perform the actual\n       * request. But we want to have the function factory ready.\n       */\n      return retryableRequest<TResponse>(request, requestOptions);\n    };\n\n    /**\n     * Once we have the function factory ready, we need to determine of the\n     * request is \"cacheable\" - should be cached. Note that, once again,\n     * the user can force this option.\n     */\n    const cacheable = requestOptions.cacheable || request.cacheable;\n\n    /**\n     * If is not \"cacheable\", we immediately trigger the retryable request, no\n     * need to check cache implementations.\n     */\n    if (cacheable !== true) {\n      return createRetryableRequest();\n    }\n\n    /**\n     * If the request is \"cacheable\", we need to first compute the key to ask\n     * the cache implementations if this request is on progress or if the\n     * response already exists on the cache.\n     */\n    const key = {\n      request,\n      requestOptions,\n      transporter: {\n        queryParameters: baseQueryParameters,\n        headers: baseHeaders,\n      },\n    };\n\n    /**\n     * With the computed key, we first ask the responses cache\n     * implementation if this request was been resolved before.\n     */\n    return responsesCache.get(\n      key,\n      () => {\n        /**\n         * If the request has never resolved before, we actually ask if there\n         * is a current request with the same key on progress.\n         */\n        return requestsCache.get(key, () =>\n          /**\n           * Finally, if there is no request in progress with the same key,\n           * this `createRetryableRequest()` will actually trigger the\n           * retryable request.\n           */\n          requestsCache\n            .set(key, createRetryableRequest())\n            .then(\n              (response) => Promise.all([requestsCache.delete(key), response]),\n              (err) => Promise.all([requestsCache.delete(key), Promise.reject(err)]),\n            )\n            .then(([_, response]) => response),\n        );\n      },\n      {\n        /**\n         * Of course, once we get this response back from the server, we\n         * tell response cache to actually store the received response\n         * to be used later.\n         */\n        miss: (response) => responsesCache.set(key, response),\n      },\n    );\n  }\n\n  return {\n    hostsCache,\n    requester,\n    timeouts,\n    logger,\n    algoliaAgent,\n    baseHeaders,\n    baseQueryParameters,\n    hosts,\n    request: createRequest,\n    requestsCache,\n    responsesCache,\n  };\n}\n", "export const LogLevelEnum: Readonly<Record<string, LogLevelType>> = {\n  Debug: 1,\n  Info: 2,\n  Error: 3,\n};\n\nexport type LogLevelType = 1 | 2 | 3;\n\nexport type Logger = {\n  /**\n   * Logs debug messages.\n   */\n  debug: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs info messages.\n   */\n  info: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs error messages.\n   */\n  error: (message: string, args?: any | undefined) => Promise<void>;\n};\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  IterableOptions,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport {\n  ApiError,\n  createAuth,\n  createIterablePromise,\n  createTransporter,\n  getAlgoliaAgent,\n  shuffle,\n} from '@algolia/client-common';\n\nimport type { AddApiKeyResponse } from '../model/addApiKeyResponse';\nimport type { ApiKey } from '../model/apiKey';\n\nimport type { BatchParams } from '../model/batchParams';\nimport type { BatchResponse } from '../model/batchResponse';\n\nimport type { BrowseResponse } from '../model/browseResponse';\nimport type { CreatedAtResponse } from '../model/createdAtResponse';\nimport type { DeleteApiKeyResponse } from '../model/deleteApiKeyResponse';\n\nimport type { DeleteSourceResponse } from '../model/deleteSourceResponse';\nimport type { DeletedAtResponse } from '../model/deletedAtResponse';\nimport type { DictionarySettingsParams } from '../model/dictionarySettingsParams';\n\nimport type { GetApiKeyResponse } from '../model/getApiKeyResponse';\nimport type { GetDictionarySettingsResponse } from '../model/getDictionarySettingsResponse';\nimport type { GetLogsResponse } from '../model/getLogsResponse';\nimport type { GetObjectsParams } from '../model/getObjectsParams';\nimport type { GetObjectsResponse } from '../model/getObjectsResponse';\nimport type { GetTaskResponse } from '../model/getTaskResponse';\nimport type { GetTopUserIdsResponse } from '../model/getTopUserIdsResponse';\nimport type { HasPendingMappingsResponse } from '../model/hasPendingMappingsResponse';\n\nimport type { Languages } from '../model/languages';\nimport type { ListApiKeysResponse } from '../model/listApiKeysResponse';\nimport type { ListClustersResponse } from '../model/listClustersResponse';\nimport type { ListIndicesResponse } from '../model/listIndicesResponse';\nimport type { ListUserIdsResponse } from '../model/listUserIdsResponse';\n\nimport type { MultipleBatchResponse } from '../model/multipleBatchResponse';\n\nimport type { RemoveUserIdResponse } from '../model/removeUserIdResponse';\nimport type { ReplaceAllObjectsResponse } from '../model/replaceAllObjectsResponse';\n\nimport type { ReplaceSourceResponse } from '../model/replaceSourceResponse';\nimport type { Rule } from '../model/rule';\nimport type { SaveObjectResponse } from '../model/saveObjectResponse';\nimport type { SaveSynonymResponse } from '../model/saveSynonymResponse';\n\nimport type { SearchDictionaryEntriesResponse } from '../model/searchDictionaryEntriesResponse';\n\nimport type { SearchForFacetValuesResponse } from '../model/searchForFacetValuesResponse';\nimport type { SearchMethodParams } from '../model/searchMethodParams';\n\nimport type { SearchResponse } from '../model/searchResponse';\nimport type { SearchResponses } from '../model/searchResponses';\n\nimport type { SearchRulesResponse } from '../model/searchRulesResponse';\n\nimport type { SearchSynonymsResponse } from '../model/searchSynonymsResponse';\nimport type { SearchUserIdsParams } from '../model/searchUserIdsParams';\nimport type { SearchUserIdsResponse } from '../model/searchUserIdsResponse';\n\nimport type { SettingsResponse } from '../model/settingsResponse';\nimport type { Source } from '../model/source';\nimport type { SynonymHit } from '../model/synonymHit';\nimport type { UpdateApiKeyResponse } from '../model/updateApiKeyResponse';\nimport type { UpdatedAtResponse } from '../model/updatedAtResponse';\nimport type { UpdatedAtWithObjectIdResponse } from '../model/updatedAtWithObjectIdResponse';\nimport type { UserId } from '../model/userId';\n\nimport type {\n  AddOrUpdateObjectProps,\n  AssignUserIdProps,\n  BatchAssignUserIdsProps,\n  BatchDictionaryEntriesProps,\n  BatchProps,\n  BrowseOptions,\n  BrowseProps,\n  ChunkedBatchOptions,\n  ClearObjectsProps,\n  ClearRulesProps,\n  ClearSynonymsProps,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteApiKeyProps,\n  DeleteByProps,\n  DeleteIndexProps,\n  DeleteObjectProps,\n  DeleteObjectsOptions,\n  DeleteRuleProps,\n  DeleteSourceProps,\n  DeleteSynonymProps,\n  GetApiKeyProps,\n  GetAppTaskProps,\n  GetLogsProps,\n  GetObjectProps,\n  GetRuleProps,\n  GetSettingsProps,\n  GetSynonymProps,\n  GetTaskProps,\n  GetUserIdProps,\n  HasPendingMappingsProps,\n  LegacySearchMethodProps,\n  ListIndicesProps,\n  ListUserIdsProps,\n  OperationIndexProps,\n  PartialUpdateObjectProps,\n  PartialUpdateObjectsOptions,\n  RemoveUserIdProps,\n  ReplaceAllObjectsOptions,\n  ReplaceSourcesProps,\n  RestoreApiKeyProps,\n  SaveObjectProps,\n  SaveObjectsOptions,\n  SaveRuleProps,\n  SaveRulesProps,\n  SaveSynonymProps,\n  SaveSynonymsProps,\n  SearchDictionaryEntriesProps,\n  SearchForFacetValuesProps,\n  SearchRulesProps,\n  SearchSingleIndexProps,\n  SearchSynonymsProps,\n  SetSettingsProps,\n  UpdateApiKeyProps,\n  WaitForApiKeyOptions,\n  WaitForAppTaskOptions,\n  WaitForTaskOptions,\n} from '../model/clientMethodProps';\n\nimport type { BatchRequest } from '../model/batchRequest';\n\nexport const apiClientVersion = '5.30.0';\n\nfunction getDefaultHosts(appId: string): Host[] {\n  return (\n    [\n      {\n        url: `${appId}-dsn.algolia.net`,\n        accept: 'read',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}.algolia.net`,\n        accept: 'write',\n        protocol: 'https',\n      },\n    ] as Host[]\n  ).concat(\n    shuffle([\n      {\n        url: `${appId}-1.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-2.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-3.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n    ]),\n  );\n}\n\nexport function createSearchClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  ...options\n}: CreateClientOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(appIdOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Search',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Helper: Wait for a task to be published (completed) for a given `indexName` and `taskID`.\n     *\n     * @summary Helper method that waits for a task to be published (completed).\n     * @param waitForTaskOptions - The `waitForTaskOptions` object.\n     * @param waitForTaskOptions.indexName - The `indexName` where the operation was performed.\n     * @param waitForTaskOptions.taskID - The `taskID` returned in the method response.\n     * @param waitForTaskOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForTaskOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    waitForTask(\n      {\n        indexName,\n        taskID,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForTaskOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetTaskResponse> {\n      let retryCount = 0;\n\n      return createIterablePromise({\n        func: () => this.getTask({ indexName, taskID }, requestOptions),\n        validate: (response) => response.status === 'published',\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      });\n    },\n\n    /**\n     * Helper: Wait for an application-level task to complete for a given `taskID`.\n     *\n     * @summary Helper method that waits for a task to be published (completed).\n     * @param waitForAppTaskOptions - The `waitForTaskOptions` object.\n     * @param waitForAppTaskOptions.taskID - The `taskID` returned in the method response.\n     * @param waitForAppTaskOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForAppTaskOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    waitForAppTask(\n      {\n        taskID,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForAppTaskOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetTaskResponse> {\n      let retryCount = 0;\n\n      return createIterablePromise({\n        func: () => this.getAppTask({ taskID }, requestOptions),\n        validate: (response) => response.status === 'published',\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      });\n    },\n\n    /**\n     * Helper: Wait for an API key to be added, updated or deleted based on a given `operation`.\n     *\n     * @summary Helper method that waits for an API key task to be processed.\n     * @param waitForApiKeyOptions - The `waitForApiKeyOptions` object.\n     * @param waitForApiKeyOptions.operation - The `operation` that was done on a `key`.\n     * @param waitForApiKeyOptions.key - The `key` that has been added, deleted or updated.\n     * @param waitForApiKeyOptions.apiKey - Necessary to know if an `update` operation has been processed, compare fields of the response with it.\n     * @param waitForApiKeyOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForApiKeyOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getApikey` method and merged with the transporter requestOptions.\n     */\n    waitForApiKey(\n      {\n        operation,\n        key,\n        apiKey,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForApiKeyOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetApiKeyResponse | undefined> {\n      let retryCount = 0;\n      const baseIteratorOptions: IterableOptions<GetApiKeyResponse | undefined> = {\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      };\n\n      if (operation === 'update') {\n        if (!apiKey) {\n          throw new Error('`apiKey` is required when waiting for an `update` operation.');\n        }\n\n        return createIterablePromise({\n          ...baseIteratorOptions,\n          func: () => this.getApiKey({ key }, requestOptions),\n          validate: (response) => {\n            for (const field of Object.keys(apiKey)) {\n              const value = apiKey[field as keyof ApiKey];\n              const resValue = response[field as keyof ApiKey];\n              if (Array.isArray(value) && Array.isArray(resValue)) {\n                if (value.length !== resValue.length || value.some((v, index) => v !== resValue[index])) {\n                  return false;\n                }\n              } else if (value !== resValue) {\n                return false;\n              }\n            }\n            return true;\n          },\n        });\n      }\n\n      return createIterablePromise({\n        ...baseIteratorOptions,\n        func: () =>\n          this.getApiKey({ key }, requestOptions).catch((error: ApiError) => {\n            if (error.status === 404) {\n              return undefined;\n            }\n\n            throw error;\n          }),\n        validate: (response) => (operation === 'add' ? response !== undefined : response === undefined),\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `browse` method of the client to allow aggregating objects of an index.\n     *\n     * @summary Helper method that iterates on the `browse` method.\n     * @param browseObjects - The `browseObjects` object.\n     * @param browseObjects.indexName - The index in which to perform the request.\n     * @param browseObjects.browseParams - The `browse` parameters.\n     * @param browseObjects.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is no `cursor` in the response.\n     * @param browseObjects.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `browse` method and merged with the transporter requestOptions.\n     */\n    browseObjects<T>(\n      { indexName, browseParams, ...browseObjectsOptions }: BrowseOptions<BrowseResponse<T>> & BrowseProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BrowseResponse<T>> {\n      return createIterablePromise<BrowseResponse<T>>({\n        func: (previousResponse) => {\n          return this.browse(\n            {\n              indexName,\n              browseParams: {\n                cursor: previousResponse ? previousResponse.cursor : undefined,\n                hitsPerPage: 1000,\n                ...browseParams,\n              },\n            },\n            requestOptions,\n          );\n        },\n        validate: (response) => response.cursor === undefined,\n        ...browseObjectsOptions,\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `searchRules` method of the client to allow aggregating rules of an index.\n     *\n     * @summary Helper method that iterates on the `searchRules` method.\n     * @param browseRules - The `browseRules` object.\n     * @param browseRules.indexName - The index in which to perform the request.\n     * @param browseRules.searchRulesParams - The `searchRules` method parameters.\n     * @param browseRules.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).\n     * @param browseRules.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchRules` method and merged with the transporter requestOptions.\n     */\n    browseRules(\n      { indexName, searchRulesParams, ...browseRulesOptions }: BrowseOptions<SearchRulesResponse> & SearchRulesProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<SearchRulesResponse> {\n      const params = {\n        ...searchRulesParams,\n        hitsPerPage: searchRulesParams?.hitsPerPage || 1000,\n      };\n\n      return createIterablePromise<SearchRulesResponse>({\n        func: (previousResponse) => {\n          return this.searchRules(\n            {\n              indexName,\n              searchRulesParams: {\n                ...params,\n                page: previousResponse ? previousResponse.page + 1 : params.page || 0,\n              },\n            },\n            requestOptions,\n          );\n        },\n        validate: (response) => response.hits.length < params.hitsPerPage,\n        ...browseRulesOptions,\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `searchSynonyms` method of the client to allow aggregating rules of an index.\n     *\n     * @summary Helper method that iterates on the `searchSynonyms` method.\n     * @param browseSynonyms - The `browseSynonyms` object.\n     * @param browseSynonyms.indexName - The index in which to perform the request.\n     * @param browseSynonyms.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).\n     * @param browseSynonyms.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param browseSynonyms.searchSynonymsParams - The `searchSynonyms` method parameters.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchSynonyms` method and merged with the transporter requestOptions.\n     */\n    browseSynonyms(\n      {\n        indexName,\n        searchSynonymsParams,\n        ...browseSynonymsOptions\n      }: BrowseOptions<SearchSynonymsResponse> & SearchSynonymsProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<SearchSynonymsResponse> {\n      const params = {\n        ...searchSynonymsParams,\n        page: searchSynonymsParams?.page || 0,\n        hitsPerPage: 1000,\n      };\n\n      return createIterablePromise<SearchSynonymsResponse>({\n        func: (_) => {\n          const resp = this.searchSynonyms(\n            {\n              indexName,\n              searchSynonymsParams: {\n                ...params,\n                page: params.page,\n              },\n            },\n            requestOptions,\n          );\n          params.page += 1;\n          return resp;\n        },\n        validate: (response) => response.hits.length < params.hitsPerPage,\n        ...browseSynonymsOptions,\n      });\n    },\n\n    /**\n     * Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     *\n     * @summary Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     * @param chunkedBatch - The `chunkedBatch` object.\n     * @param chunkedBatch.indexName - The `indexName` to replace `objects` in.\n     * @param chunkedBatch.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param chunkedBatch.action - The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.\n     * @param chunkedBatch.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param chunkedBatch.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    async chunkedBatch(\n      { indexName, objects, action = 'addObject', waitForTasks, batchSize = 1000 }: ChunkedBatchOptions,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<BatchResponse>> {\n      let requests: Array<BatchRequest> = [];\n      const responses: Array<BatchResponse> = [];\n\n      const objectEntries = objects.entries();\n      for (const [i, obj] of objectEntries) {\n        requests.push({ action, body: obj });\n        if (requests.length === batchSize || i === objects.length - 1) {\n          responses.push(await this.batch({ indexName, batchWriteParams: { requests } }, requestOptions));\n          requests = [];\n        }\n      }\n\n      if (waitForTasks) {\n        for (const resp of responses) {\n          await this.waitForTask({ indexName, taskID: resp.taskID });\n        }\n      }\n\n      return responses;\n    },\n\n    /**\n     * Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     *\n     * @summary Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     * @param saveObjects - The `saveObjects` object.\n     * @param saveObjects.indexName - The `indexName` to save `objects` in.\n     * @param saveObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param saveObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param saveObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.\n     */\n    async saveObjects(\n      { indexName, objects, waitForTasks, batchSize }: SaveObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        { indexName, objects, action: 'addObject', waitForTasks, batchSize },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.\n     *\n     * @summary Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.\n     * @param deleteObjects - The `deleteObjects` object.\n     * @param deleteObjects.indexName - The `indexName` to delete `objectIDs` from.\n     * @param deleteObjects.objectIDs - The objectIDs to delete.\n     * @param deleteObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param deleteObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.\n     */\n    async deleteObjects(\n      { indexName, objectIDs, waitForTasks, batchSize }: DeleteObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        {\n          indexName,\n          objects: objectIDs.map((objectID) => ({ objectID })),\n          action: 'deleteObject',\n          waitForTasks,\n          batchSize,\n        },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     *\n     * @summary Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     * @param partialUpdateObjects - The `partialUpdateObjects` object.\n     * @param partialUpdateObjects.indexName - The `indexName` to update `objects` in.\n     * @param partialUpdateObjects.objects - The array of `objects` to update in the given Algolia `indexName`.\n     * @param partialUpdateObjects.createIfNotExists - To be provided if non-existing objects are passed, otherwise, the call will fail..\n     * @param partialUpdateObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param partialUpdateObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    async partialUpdateObjects(\n      { indexName, objects, createIfNotExists, waitForTasks, batchSize }: PartialUpdateObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        {\n          indexName,\n          objects,\n          action: createIfNotExists ? 'partialUpdateObject' : 'partialUpdateObjectNoCreate',\n          batchSize,\n          waitForTasks,\n        },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.\n     * See https://api-clients-automation.netlify.app/docs/add-new-api-client#5-helpers for implementation details.\n     *\n     * @summary Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.\n     * @param replaceAllObjects - The `replaceAllObjects` object.\n     * @param replaceAllObjects.indexName - The `indexName` to replace `objects` in.\n     * @param replaceAllObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param replaceAllObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `objects.length / batchSize`. Defaults to 1000.\n     * @param replaceAllObjects.scopes - The `scopes` to keep from the index. Defaults to ['settings', 'rules', 'synonyms'].\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch`, `operationIndex` and `getTask` method and merged with the transporter requestOptions.\n     */\n    async replaceAllObjects(\n      { indexName, objects, batchSize, scopes }: ReplaceAllObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<ReplaceAllObjectsResponse> {\n      const randomSuffix = Math.floor(Math.random() * 1000000) + 100000;\n      const tmpIndexName = `${indexName}_tmp_${randomSuffix}`;\n\n      if (scopes === undefined) {\n        scopes = ['settings', 'rules', 'synonyms'];\n      }\n\n      try {\n        let copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n\n        const batchResponses = await this.chunkedBatch(\n          { indexName: tmpIndexName, objects, waitForTasks: true, batchSize },\n          requestOptions,\n        );\n\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        const moveOperationResponse = await this.operationIndex(\n          {\n            indexName: tmpIndexName,\n            operationIndexParams: { operation: 'move', destination: indexName },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: moveOperationResponse.taskID,\n        });\n\n        return { copyOperationResponse, batchResponses, moveOperationResponse };\n      } catch (error) {\n        await this.deleteIndex({ indexName: tmpIndexName });\n\n        throw error;\n      }\n    },\n\n    async indexExists({ indexName }: GetSettingsProps): Promise<boolean> {\n      try {\n        await this.getSettings({ indexName });\n      } catch (error) {\n        if (error instanceof ApiError && error.status === 404) {\n          return false;\n        }\n        throw error;\n      }\n\n      return true;\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia records (hits) and not facets.\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `hits` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `hits`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForHits<T>(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchResponse<T>> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{ results: Array<SearchResponse<T>> }>;\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia facets and not records (hits).\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `facets` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `facets`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacets(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchForFacetValuesResponse> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{\n        results: Array<SearchForFacetValuesResponse>;\n      }>;\n    },\n    /**\n     * Creates a new API key with specific permissions and restrictions.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param apiKey - The apiKey object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    addApiKey(apiKey: ApiKey, requestOptions?: RequestOptions): Promise<AddApiKeyResponse> {\n      if (!apiKey) {\n        throw new Error('Parameter `apiKey` is required when calling `addApiKey`.');\n      }\n\n      if (!apiKey.acl) {\n        throw new Error('Parameter `apiKey.acl` is required when calling `addApiKey`.');\n      }\n\n      const requestPath = '/1/keys';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: apiKey,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a record with the specified object ID exists, the existing record is replaced. Otherwise, a new record is added to the index.  If you want to use auto-generated object IDs, use the [`saveObject` operation](#tag/Records/operation/saveObject). To update _some_ attributes of an existing record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject) instead. To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param addOrUpdateObject - The addOrUpdateObject object.\n     * @param addOrUpdateObject.indexName - Name of the index on which to perform the operation.\n     * @param addOrUpdateObject.objectID - Unique record identifier.\n     * @param addOrUpdateObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    addOrUpdateObject<T extends object>(\n      { indexName, objectID, body }: AddOrUpdateObjectProps<T>,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtWithObjectIdResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `addOrUpdateObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `addOrUpdateObject`.');\n      }\n\n      if (!body) {\n        throw new Error('Parameter `body` is required when calling `addOrUpdateObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds a source to the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param source - Source to add.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    appendSource(source: Source, requestOptions?: RequestOptions): Promise<CreatedAtResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `appendSource`.');\n      }\n\n      if (!source.source) {\n        throw new Error('Parameter `source.source` is required when calling `appendSource`.');\n      }\n\n      const requestPath = '/1/security/sources/append';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: source,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Assigns or moves a user ID to a cluster.  The time it takes to move a user is proportional to the amount of data linked to the user ID.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param assignUserId - The assignUserId object.\n     * @param assignUserId.xAlgoliaUserID - Unique identifier of the user who makes the search request.\n     * @param assignUserId.assignUserIdParams - The assignUserIdParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    assignUserId(\n      { xAlgoliaUserID, assignUserIdParams }: AssignUserIdProps,\n      requestOptions?: RequestOptions,\n    ): Promise<CreatedAtResponse> {\n      if (!xAlgoliaUserID) {\n        throw new Error('Parameter `xAlgoliaUserID` is required when calling `assignUserId`.');\n      }\n\n      if (!assignUserIdParams) {\n        throw new Error('Parameter `assignUserIdParams` is required when calling `assignUserId`.');\n      }\n\n      if (!assignUserIdParams.cluster) {\n        throw new Error('Parameter `assignUserIdParams.cluster` is required when calling `assignUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (xAlgoliaUserID !== undefined) {\n        headers['X-Algolia-User-ID'] = xAlgoliaUserID.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: assignUserIdParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds, updates, or deletes records in one index with a single API request.  Batching index updates reduces latency and increases data integrity.  - Actions are applied in the order they\\'re specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     * @param batch - The batch object.\n     * @param batch.indexName - Name of the index on which to perform the operation.\n     * @param batch.batchWriteParams - The batchWriteParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batch({ indexName, batchWriteParams }: BatchProps, requestOptions?: RequestOptions): Promise<BatchResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `batch`.');\n      }\n\n      if (!batchWriteParams) {\n        throw new Error('Parameter `batchWriteParams` is required when calling `batch`.');\n      }\n\n      if (!batchWriteParams.requests) {\n        throw new Error('Parameter `batchWriteParams.requests` is required when calling `batch`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchWriteParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Assigns multiple user IDs to a cluster.  **You can\\'t move users with this operation**.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param batchAssignUserIds - The batchAssignUserIds object.\n     * @param batchAssignUserIds.xAlgoliaUserID - Unique identifier of the user who makes the search request.\n     * @param batchAssignUserIds.batchAssignUserIdsParams - The batchAssignUserIdsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batchAssignUserIds(\n      { xAlgoliaUserID, batchAssignUserIdsParams }: BatchAssignUserIdsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<CreatedAtResponse> {\n      if (!xAlgoliaUserID) {\n        throw new Error('Parameter `xAlgoliaUserID` is required when calling `batchAssignUserIds`.');\n      }\n\n      if (!batchAssignUserIdsParams) {\n        throw new Error('Parameter `batchAssignUserIdsParams` is required when calling `batchAssignUserIds`.');\n      }\n\n      if (!batchAssignUserIdsParams.cluster) {\n        throw new Error('Parameter `batchAssignUserIdsParams.cluster` is required when calling `batchAssignUserIds`.');\n      }\n      if (!batchAssignUserIdsParams.users) {\n        throw new Error('Parameter `batchAssignUserIdsParams.users` is required when calling `batchAssignUserIds`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/batch';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (xAlgoliaUserID !== undefined) {\n        headers['X-Algolia-User-ID'] = xAlgoliaUserID.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchAssignUserIdsParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds or deletes multiple entries from your plurals, segmentation, or stop word dictionaries.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param batchDictionaryEntries - The batchDictionaryEntries object.\n     * @param batchDictionaryEntries.dictionaryName - Dictionary type in which to search.\n     * @param batchDictionaryEntries.batchDictionaryEntriesParams - The batchDictionaryEntriesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batchDictionaryEntries(\n      { dictionaryName, batchDictionaryEntriesParams }: BatchDictionaryEntriesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!dictionaryName) {\n        throw new Error('Parameter `dictionaryName` is required when calling `batchDictionaryEntries`.');\n      }\n\n      if (!batchDictionaryEntriesParams) {\n        throw new Error('Parameter `batchDictionaryEntriesParams` is required when calling `batchDictionaryEntries`.');\n      }\n\n      if (!batchDictionaryEntriesParams.requests) {\n        throw new Error(\n          'Parameter `batchDictionaryEntriesParams.requests` is required when calling `batchDictionaryEntries`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/{dictionaryName}/batch'.replace(\n        '{dictionaryName}',\n        encodeURIComponent(dictionaryName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchDictionaryEntriesParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves records from an index, up to 1,000 per request.  While searching retrieves _hits_ (records augmented with attributes for highlighting and ranking details), browsing _just_ returns matching records. This can be useful if you want to export your indices.  - The Analytics API doesn\\'t collect data when using `browse`. - Records are ranked by attributes and custom ranking. - There\\'s no ranking for: typo-tolerance, number of matched words, proximity, geo distance.  Browse requests automatically apply these settings:  - `advancedSyntax`: `false` - `attributesToHighlight`: `[]` - `attributesToSnippet`: `[]` - `distinct`: `false` - `enablePersonalization`: `false` - `enableRules`: `false` - `facets`: `[]` - `getRankingInfo`: `false` - `ignorePlurals`: `false` - `optionalFilters`: `[]` - `typoTolerance`: `true` or `false` (`min` and `strict` evaluate to `true`)  If you send these parameters with your browse requests, they\\'ll be ignored.\n     *\n     * Required API Key ACLs:\n     *  - browse\n     * @param browse - The browse object.\n     * @param browse.indexName - Name of the index on which to perform the operation.\n     * @param browse.browseParams - The browseParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    browse<T>({ indexName, browseParams }: BrowseProps, requestOptions?: RequestOptions): Promise<BrowseResponse<T>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `browse`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/browse'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: browseParams ? browseParams : {},\n        useReadTransporter: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes only the records from an index while keeping settings, synonyms, and rules. This operation is resource-intensive and subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param clearObjects - The clearObjects object.\n     * @param clearObjects.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearObjects({ indexName }: ClearObjectsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearObjects`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes all rules from the index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param clearRules - The clearRules object.\n     * @param clearRules.indexName - Name of the index on which to perform the operation.\n     * @param clearRules.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearRules(\n      { indexName, forwardToReplicas }: ClearRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes all synonyms from the index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param clearSynonyms - The clearSynonyms object.\n     * @param clearSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param clearSynonyms.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearSynonyms(\n      { indexName, forwardToReplicas }: ClearSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes the API key.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param deleteApiKey - The deleteApiKey object.\n     * @param deleteApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteApiKey({ key }: DeleteApiKeyProps, requestOptions?: RequestOptions): Promise<DeleteApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `deleteApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This operation doesn\\'t accept empty filters.  This operation is resource-intensive. You should only use it if you can\\'t get the object IDs of the records you want to delete. It\\'s more efficient to get a list of object IDs with the [`browse` operation](#tag/Search/operation/browse), and then delete the records using the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param deleteBy - The deleteBy object.\n     * @param deleteBy.indexName - Name of the index on which to perform the operation.\n     * @param deleteBy.deleteByParams - The deleteByParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteBy(\n      { indexName, deleteByParams }: DeleteByProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteBy`.');\n      }\n\n      if (!deleteByParams) {\n        throw new Error('Parameter `deleteByParams` is required when calling `deleteBy`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/deleteByQuery'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: deleteByParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes an index and all its settings.  - Deleting an index doesn\\'t delete its analytics data. - If you try to delete a non-existing index, the operation is ignored without warning. - If the index you want to delete has replica indices, the replicas become independent indices. - If the index you want to delete is a replica index, you must first unlink it from its primary index before you can delete it.   For more information, see [Delete replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/deleting-replicas/).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param deleteIndex - The deleteIndex object.\n     * @param deleteIndex.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteIndex({ indexName }: DeleteIndexProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a record by its object ID.  To delete more than one record, use the [`batch` operation](#tag/Records/operation/batch). To delete records matching a query, use the [`deleteBy` operation](#tag/Records/operation/deleteBy).\n     *\n     * Required API Key ACLs:\n     *  - deleteObject\n     * @param deleteObject - The deleteObject object.\n     * @param deleteObject.indexName - Name of the index on which to perform the operation.\n     * @param deleteObject.objectID - Unique record identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteObject(\n      { indexName, objectID }: DeleteObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a rule by its ID. To find the object ID for rules, use the [`search` operation](#tag/Rules/operation/searchRules).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteRule - The deleteRule object.\n     * @param deleteRule.indexName - Name of the index on which to perform the operation.\n     * @param deleteRule.objectID - Unique identifier of a rule object.\n     * @param deleteRule.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteRule(\n      { indexName, objectID, forwardToReplicas }: DeleteRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a source from the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param deleteSource - The deleteSource object.\n     * @param deleteSource.source - IP address range of the source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSource({ source }: DeleteSourceProps, requestOptions?: RequestOptions): Promise<DeleteSourceResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `deleteSource`.');\n      }\n\n      const requestPath = '/1/security/sources/{source}'.replace('{source}', encodeURIComponent(source));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a synonym by its ID. To find the object IDs of your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteSynonym - The deleteSynonym object.\n     * @param deleteSynonym.indexName - Name of the index on which to perform the operation.\n     * @param deleteSynonym.objectID - Unique identifier of a synonym object.\n     * @param deleteSynonym.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSynonym(\n      { indexName, objectID, forwardToReplicas }: DeleteSynonymProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Gets the permissions and restrictions of an API key.  When authenticating with the admin API key, you can request information for any of your application\\'s keys. When authenticating with other API keys, you can only retrieve information for that key, with the description replaced by `<redacted>`.\n     * @param getApiKey - The getApiKey object.\n     * @param getApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getApiKey({ key }: GetApiKeyProps, requestOptions?: RequestOptions): Promise<GetApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `getApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Checks the status of a given application task.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param getAppTask - The getAppTask object.\n     * @param getAppTask.taskID - Unique task identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAppTask({ taskID }: GetAppTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getAppTask`.');\n      }\n\n      const requestPath = '/1/task/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists supported languages with their supported dictionary types and number of custom entries.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDictionaryLanguages(requestOptions?: RequestOptions | undefined): Promise<{ [key: string]: Languages }> {\n      const requestPath = '/1/dictionaries/*/languages';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the languages for which standard dictionary entries are turned off.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDictionarySettings(requestOptions?: RequestOptions | undefined): Promise<GetDictionarySettingsResponse> {\n      const requestPath = '/1/dictionaries/*/settings';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * The request must be authenticated by an API key with the [`logs` ACL](https://www.algolia.com/doc/guides/security/api-keys/#access-control-list-acl).  - Logs are held for the last seven days. - Up to 1,000 API requests per server are logged. - This request counts towards your [operations quota](https://support.algolia.com/hc/en-us/articles/4406981829777-How-does-Algolia-count-records-and-operations-) but doesn\\'t appear in the logs itself.\n     *\n     * Required API Key ACLs:\n     *  - logs\n     * @param getLogs - The getLogs object.\n     * @param getLogs.offset - First log entry to retrieve. The most recent entries are listed first.\n     * @param getLogs.length - Maximum number of entries to retrieve.\n     * @param getLogs.indexName - Index for which to retrieve log entries. By default, log entries are retrieved for all indices.\n     * @param getLogs.type - Type of log entries to retrieve. By default, all log entries are retrieved.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getLogs(\n      { offset, length, indexName, type }: GetLogsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<GetLogsResponse> {\n      const requestPath = '/1/logs';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (length !== undefined) {\n        queryParameters['length'] = length.toString();\n      }\n\n      if (indexName !== undefined) {\n        queryParameters['indexName'] = indexName.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves one record by its object ID.  To retrieve more than one record, use the [`objects` operation](#tag/Records/operation/getObjects).\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getObject - The getObject object.\n     * @param getObject.indexName - Name of the index on which to perform the operation.\n     * @param getObject.objectID - Unique record identifier.\n     * @param getObject.attributesToRetrieve - Attributes to include with the records in the response. This is useful to reduce the size of the API response. By default, all retrievable attributes are returned.  `objectID` is always retrieved.  Attributes included in `unretrievableAttributes` won\\'t be retrieved unless the request is authenticated with the admin API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getObject(\n      { indexName, objectID, attributesToRetrieve }: GetObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (attributesToRetrieve !== undefined) {\n        queryParameters['attributesToRetrieve'] = attributesToRetrieve.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves one or more records, potentially from different indices.  Records are returned in the same order as the requests.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getObjectsParams - Request object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getObjects<T>(getObjectsParams: GetObjectsParams, requestOptions?: RequestOptions): Promise<GetObjectsResponse<T>> {\n      if (!getObjectsParams) {\n        throw new Error('Parameter `getObjectsParams` is required when calling `getObjects`.');\n      }\n\n      if (!getObjectsParams.requests) {\n        throw new Error('Parameter `getObjectsParams.requests` is required when calling `getObjects`.');\n      }\n\n      const requestPath = '/1/indexes/*/objects';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: getObjectsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a rule by its ID. To find the object ID of rules, use the [`search` operation](#tag/Rules/operation/searchRules).\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getRule - The getRule object.\n     * @param getRule.indexName - Name of the index on which to perform the operation.\n     * @param getRule.objectID - Unique identifier of a rule object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRule({ indexName, objectID }: GetRuleProps, requestOptions?: RequestOptions): Promise<Rule> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves an object with non-null index settings.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getSettings - The getSettings object.\n     * @param getSettings.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSettings({ indexName }: GetSettingsProps, requestOptions?: RequestOptions): Promise<SettingsResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getSettings`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/settings'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves all allowed IP addresses with access to your application.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSources(requestOptions?: RequestOptions | undefined): Promise<Array<Source>> {\n      const requestPath = '/1/security/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a synonym by its ID. To find the object IDs for your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getSynonym - The getSynonym object.\n     * @param getSynonym.indexName - Name of the index on which to perform the operation.\n     * @param getSynonym.objectID - Unique identifier of a synonym object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSynonym({ indexName, objectID }: GetSynonymProps, requestOptions?: RequestOptions): Promise<SynonymHit> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Checks the status of a given task.  Indexing tasks are asynchronous. When you add, update, or delete records or indices, a task is created on a queue and completed depending on the load on the server.  The indexing tasks\\' responses include a task ID that you can use to check the status.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param getTask - The getTask object.\n     * @param getTask.indexName - Name of the index on which to perform the operation.\n     * @param getTask.taskID - Unique task identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTask({ indexName, taskID }: GetTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getTask`.');\n      }\n\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTask`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/task/{taskID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Get the IDs of the 10 users with the highest number of records per cluster.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopUserIds(requestOptions?: RequestOptions | undefined): Promise<GetTopUserIdsResponse> {\n      const requestPath = '/1/clusters/mapping/top';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Returns the user ID data stored in the mapping.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param getUserId - The getUserId object.\n     * @param getUserId.userID - Unique identifier of the user who makes the search request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getUserId({ userID }: GetUserIdProps, requestOptions?: RequestOptions): Promise<UserId> {\n      if (!userID) {\n        throw new Error('Parameter `userID` is required when calling `getUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/{userID}'.replace('{userID}', encodeURIComponent(userID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * To determine when the time-consuming process of creating a large batch of users or migrating users from one cluster to another is complete, this operation retrieves the status of the process.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param hasPendingMappings - The hasPendingMappings object.\n     * @param hasPendingMappings.getClusters - Whether to include the cluster\\'s pending mapping state in the response.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    hasPendingMappings(\n      { getClusters }: HasPendingMappingsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<HasPendingMappingsResponse> {\n      const requestPath = '/1/clusters/mapping/pending';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (getClusters !== undefined) {\n        queryParameters['getClusters'] = getClusters.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists all API keys associated with your Algolia application, including their permissions and restrictions.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listApiKeys(requestOptions?: RequestOptions | undefined): Promise<ListApiKeysResponse> {\n      const requestPath = '/1/keys';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists the available clusters in a multi-cluster setup.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listClusters(requestOptions?: RequestOptions | undefined): Promise<ListClustersResponse> {\n      const requestPath = '/1/clusters';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists all indices in the current Algolia application.  The request follows any index restrictions of the API key you use to make the request.\n     *\n     * Required API Key ACLs:\n     *  - listIndexes\n     * @param listIndices - The listIndices object.\n     * @param listIndices.page - Requested page of the API response. If `null`, the API response is not paginated.\n     * @param listIndices.hitsPerPage - Number of hits per page.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listIndices(\n      { page, hitsPerPage }: ListIndicesProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListIndicesResponse> {\n      const requestPath = '/1/indexes';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (hitsPerPage !== undefined) {\n        queryParameters['hitsPerPage'] = hitsPerPage.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists the userIDs assigned to a multi-cluster application.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param listUserIds - The listUserIds object.\n     * @param listUserIds.page - Requested page of the API response. If `null`, the API response is not paginated.\n     * @param listUserIds.hitsPerPage - Number of hits per page.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listUserIds(\n      { page, hitsPerPage }: ListUserIdsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListUserIdsResponse> {\n      const requestPath = '/1/clusters/mapping';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (hitsPerPage !== undefined) {\n        queryParameters['hitsPerPage'] = hitsPerPage.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds, updates, or deletes records in multiple indices with a single API request.  - Actions are applied in the order they are specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     * @param batchParams - The batchParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    multipleBatch(batchParams: BatchParams, requestOptions?: RequestOptions): Promise<MultipleBatchResponse> {\n      if (!batchParams) {\n        throw new Error('Parameter `batchParams` is required when calling `multipleBatch`.');\n      }\n\n      if (!batchParams.requests) {\n        throw new Error('Parameter `batchParams.requests` is required when calling `multipleBatch`.');\n      }\n\n      const requestPath = '/1/indexes/*/batch';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Copies or moves (renames) an index within the same Algolia application.  - Existing destination indices are overwritten, except for their analytics data. - If the destination index doesn\\'t exist yet, it\\'ll be created. - This operation is resource-intensive.  **Copy**  - Copying a source index that doesn\\'t exist creates a new index with 0 records and default settings. - The API keys of the source index are merged with the existing keys in the destination index. - You can\\'t copy the `enableReRanking`, `mode`, and `replicas` settings. - You can\\'t copy to a destination index that already has replicas. - Be aware of the [size limits](https://www.algolia.com/doc/guides/scaling/algolia-service-limits/#application-record-and-index-limits). - Related guide: [Copy indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/copy-indices/)  **Move**  - Moving a source index that doesn\\'t exist is ignored without returning an error. - When moving an index, the analytics data keeps its original name, and a new set of analytics data is started for the new name.   To access the original analytics in the dashboard, create an index with the original name. - If the destination index has replicas, moving will overwrite the existing index and copy the data to the replica indices. - Related guide: [Move indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/move-indices/).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param operationIndex - The operationIndex object.\n     * @param operationIndex.indexName - Name of the index on which to perform the operation.\n     * @param operationIndex.operationIndexParams - The operationIndexParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    operationIndex(\n      { indexName, operationIndexParams }: OperationIndexProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `operationIndex`.');\n      }\n\n      if (!operationIndexParams) {\n        throw new Error('Parameter `operationIndexParams` is required when calling `operationIndex`.');\n      }\n\n      if (!operationIndexParams.operation) {\n        throw new Error('Parameter `operationIndexParams.operation` is required when calling `operationIndex`.');\n      }\n      if (!operationIndexParams.destination) {\n        throw new Error('Parameter `operationIndexParams.destination` is required when calling `operationIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/operation'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: operationIndexParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds new attributes to a record, or updates existing ones.  - If a record with the specified object ID doesn\\'t exist,   a new record is added to the index **if** `createIfNotExists` is true. - If the index doesn\\'t exist yet, this method creates a new index. - You can use any first-level attribute but not nested attributes.   If you specify a nested attribute, this operation replaces its first-level ancestor.  To update an attribute without pushing the entire record, you can use these built-in operations. These operations can be helpful if you don\\'t have access to your initial data.  - Increment: increment a numeric attribute - Decrement: decrement a numeric attribute - Add: append a number or string element to an array attribute - Remove: remove all matching number or string elements from an array attribute made of numbers or strings - AddUnique: add a number or string element to an array attribute made of numbers or strings only if it\\'s not already present - IncrementFrom: increment a numeric integer attribute only if the provided value matches the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementFrom value of 2 for the version attribute, but the current value of the attribute is 1, the engine ignores the update. If the object doesn\\'t exist, the engine only creates it if you pass an IncrementFrom value of 0. - IncrementSet: increment a numeric integer attribute only if the provided value is greater than the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementSet value of 2 for the version attribute, and the current value of the attribute is 1, the engine updates the object. If the object doesn\\'t exist yet, the engine only creates it if you pass an IncrementSet value greater than 0.  You can specify an operation by providing an object with the attribute to update as the key and its value being an object with the following properties:  - _operation: the operation to apply on the attribute - value: the right-hand side argument to the operation, for example, increment or decrement step, value to add or remove.  When updating multiple attributes or using multiple operations targeting the same record, you should use a single partial update for faster processing.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param partialUpdateObject - The partialUpdateObject object.\n     * @param partialUpdateObject.indexName - Name of the index on which to perform the operation.\n     * @param partialUpdateObject.objectID - Unique record identifier.\n     * @param partialUpdateObject.attributesToUpdate - Attributes with their values.\n     * @param partialUpdateObject.createIfNotExists - Whether to create a new record if it doesn\\'t exist.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    partialUpdateObject(\n      { indexName, objectID, attributesToUpdate, createIfNotExists }: PartialUpdateObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtWithObjectIdResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `partialUpdateObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `partialUpdateObject`.');\n      }\n\n      if (!attributesToUpdate) {\n        throw new Error('Parameter `attributesToUpdate` is required when calling `partialUpdateObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}/partial'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (createIfNotExists !== undefined) {\n        queryParameters['createIfNotExists'] = createIfNotExists.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: attributesToUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a user ID and its associated data from the clusters.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param removeUserId - The removeUserId object.\n     * @param removeUserId.userID - Unique identifier of the user who makes the search request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    removeUserId({ userID }: RemoveUserIdProps, requestOptions?: RequestOptions): Promise<RemoveUserIdResponse> {\n      if (!userID) {\n        throw new Error('Parameter `userID` is required when calling `removeUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/{userID}'.replace('{userID}', encodeURIComponent(userID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Replaces the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param replaceSources - The replaceSources object.\n     * @param replaceSources.source - Allowed sources.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    replaceSources({ source }: ReplaceSourcesProps, requestOptions?: RequestOptions): Promise<ReplaceSourceResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `replaceSources`.');\n      }\n\n      const requestPath = '/1/security/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: source,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Restores a deleted API key.  Restoring resets the `validity` attribute to `0`.  Algolia stores up to 1,000 API keys per application. If you create more, the oldest API keys are deleted and can\\'t be restored.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param restoreApiKey - The restoreApiKey object.\n     * @param restoreApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    restoreApiKey({ key }: RestoreApiKeyProps, requestOptions?: RequestOptions): Promise<AddApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `restoreApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}/restore'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds a record to an index or replaces it.  - If the record doesn\\'t have an object ID, a new record with an auto-generated object ID is added to your index. - If a record with the specified object ID exists, the existing record is replaced. - If a record with the specified object ID doesn\\'t exist, a new record is added to your index. - If you add a record to an index that doesn\\'t exist yet, a new index is created.  To update _some_ attributes of a record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject). To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param saveObject - The saveObject object.\n     * @param saveObject.indexName - Name of the index on which to perform the operation.\n     * @param saveObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveObject<T extends object>(\n      { indexName, body }: SaveObjectProps<T>,\n      requestOptions?: RequestOptions,\n    ): Promise<SaveObjectResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveObject`.');\n      }\n\n      if (!body) {\n        throw new Error('Parameter `body` is required when calling `saveObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a rule with the specified object ID doesn\\'t exist, it\\'s created. Otherwise, the existing rule is replaced.  To create or update more than one rule, use the [`batch` operation](#tag/Rules/operation/saveRules).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveRule - The saveRule object.\n     * @param saveRule.indexName - Name of the index on which to perform the operation.\n     * @param saveRule.objectID - Unique identifier of a rule object.\n     * @param saveRule.rule - The rule object.\n     * @param saveRule.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveRule(\n      { indexName, objectID, rule, forwardToReplicas }: SaveRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `saveRule`.');\n      }\n\n      if (!rule) {\n        throw new Error('Parameter `rule` is required when calling `saveRule`.');\n      }\n\n      if (!rule.objectID) {\n        throw new Error('Parameter `rule.objectID` is required when calling `saveRule`.');\n      }\n      if (!rule.consequence) {\n        throw new Error('Parameter `rule.consequence` is required when calling `saveRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: rule,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Create or update multiple rules.  If a rule with the specified object ID doesn\\'t exist, Algolia creates a new one. Otherwise, existing rules are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveRules - The saveRules object.\n     * @param saveRules.indexName - Name of the index on which to perform the operation.\n     * @param saveRules.rules - The rules object.\n     * @param saveRules.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param saveRules.clearExistingRules - Whether existing rules should be deleted before adding this batch.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveRules(\n      { indexName, rules, forwardToReplicas, clearExistingRules }: SaveRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveRules`.');\n      }\n\n      if (!rules) {\n        throw new Error('Parameter `rules` is required when calling `saveRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      if (clearExistingRules !== undefined) {\n        queryParameters['clearExistingRules'] = clearExistingRules.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: rules,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a synonym with the specified object ID doesn\\'t exist, Algolia adds a new one. Otherwise, the existing synonym is replaced. To add multiple synonyms in a single API request, use the [`batch` operation](#tag/Synonyms/operation/saveSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveSynonym - The saveSynonym object.\n     * @param saveSynonym.indexName - Name of the index on which to perform the operation.\n     * @param saveSynonym.objectID - Unique identifier of a synonym object.\n     * @param saveSynonym.synonymHit - The synonymHit object.\n     * @param saveSynonym.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveSynonym(\n      { indexName, objectID, synonymHit, forwardToReplicas }: SaveSynonymProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SaveSynonymResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `saveSynonym`.');\n      }\n\n      if (!synonymHit) {\n        throw new Error('Parameter `synonymHit` is required when calling `saveSynonym`.');\n      }\n\n      if (!synonymHit.objectID) {\n        throw new Error('Parameter `synonymHit.objectID` is required when calling `saveSynonym`.');\n      }\n      if (!synonymHit.type) {\n        throw new Error('Parameter `synonymHit.type` is required when calling `saveSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: synonymHit,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a synonym with the `objectID` doesn\\'t exist, Algolia adds a new one. Otherwise, existing synonyms are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveSynonyms - The saveSynonyms object.\n     * @param saveSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param saveSynonyms.synonymHit - The synonymHit object.\n     * @param saveSynonyms.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param saveSynonyms.replaceExistingSynonyms - Whether to replace all synonyms in the index with the ones sent with this request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveSynonyms(\n      { indexName, synonymHit, forwardToReplicas, replaceExistingSynonyms }: SaveSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveSynonyms`.');\n      }\n\n      if (!synonymHit) {\n        throw new Error('Parameter `synonymHit` is required when calling `saveSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      if (replaceExistingSynonyms !== undefined) {\n        queryParameters['replaceExistingSynonyms'] = replaceExistingSynonyms.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: synonymHit,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Sends multiple search requests to one or more indices.  This can be useful in these cases:  - Different indices for different purposes, such as, one index for products, another one for marketing content. - Multiple searches to the same index—for example, with different filters.  Use the helper `searchForHits` or `searchForFacets` to get the results in a more convenient format, if you already know the return type you want.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchMethodParams - Muli-search request body. Results are returned in the same order as the requests.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    search<T>(\n      searchMethodParams: SearchMethodParams | LegacySearchMethodProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchResponses<T>> {\n      if (searchMethodParams && Array.isArray(searchMethodParams)) {\n        const newSignatureRequest: SearchMethodParams = {\n          requests: searchMethodParams.map(({ params, ...legacyRequest }) => {\n            if (legacyRequest.type === 'facet') {\n              return {\n                ...legacyRequest,\n                ...params,\n                type: 'facet',\n              };\n            }\n\n            return {\n              ...legacyRequest,\n              ...params,\n              facet: undefined,\n              maxFacetHits: undefined,\n              facetQuery: undefined,\n            };\n          }),\n        };\n\n        searchMethodParams = newSignatureRequest;\n      }\n\n      if (!searchMethodParams) {\n        throw new Error('Parameter `searchMethodParams` is required when calling `search`.');\n      }\n\n      if (!searchMethodParams.requests) {\n        throw new Error('Parameter `searchMethodParams.requests` is required when calling `search`.');\n      }\n\n      const requestPath = '/1/indexes/*/queries';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchMethodParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for standard and custom dictionary entries.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchDictionaryEntries - The searchDictionaryEntries object.\n     * @param searchDictionaryEntries.dictionaryName - Dictionary type in which to search.\n     * @param searchDictionaryEntries.searchDictionaryEntriesParams - The searchDictionaryEntriesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchDictionaryEntries(\n      { dictionaryName, searchDictionaryEntriesParams }: SearchDictionaryEntriesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchDictionaryEntriesResponse> {\n      if (!dictionaryName) {\n        throw new Error('Parameter `dictionaryName` is required when calling `searchDictionaryEntries`.');\n      }\n\n      if (!searchDictionaryEntriesParams) {\n        throw new Error(\n          'Parameter `searchDictionaryEntriesParams` is required when calling `searchDictionaryEntries`.',\n        );\n      }\n\n      if (!searchDictionaryEntriesParams.query) {\n        throw new Error(\n          'Parameter `searchDictionaryEntriesParams.query` is required when calling `searchDictionaryEntries`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/{dictionaryName}/search'.replace(\n        '{dictionaryName}',\n        encodeURIComponent(dictionaryName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchDictionaryEntriesParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for values of a specified facet attribute.  - By default, facet values are sorted by decreasing count.   You can adjust this with the `sortFacetValueBy` parameter. - Searching for facet values doesn\\'t work if you have **more than 65 searchable facets and searchable attributes combined**.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchForFacetValues - The searchForFacetValues object.\n     * @param searchForFacetValues.indexName - Name of the index on which to perform the operation.\n     * @param searchForFacetValues.facetName - Facet attribute in which to search for values.  This attribute must be included in the `attributesForFaceting` index setting with the `searchable()` modifier.\n     * @param searchForFacetValues.searchForFacetValuesRequest - The searchForFacetValuesRequest object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacetValues(\n      { indexName, facetName, searchForFacetValuesRequest }: SearchForFacetValuesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchForFacetValuesResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchForFacetValues`.');\n      }\n\n      if (!facetName) {\n        throw new Error('Parameter `facetName` is required when calling `searchForFacetValues`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/facets/{facetName}/query'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{facetName}', encodeURIComponent(facetName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchForFacetValuesRequest ? searchForFacetValuesRequest : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for rules in your index.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchRules - The searchRules object.\n     * @param searchRules.indexName - Name of the index on which to perform the operation.\n     * @param searchRules.searchRulesParams - The searchRulesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchRules(\n      { indexName, searchRulesParams }: SearchRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchRulesResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/search'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchRulesParams ? searchRulesParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches a single index and returns matching search results (_hits_).  This method lets you retrieve up to 1,000 hits. If you need more, use the [`browse` operation](#tag/Search/operation/browse) or increase the `paginatedLimitedTo` index setting.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchSingleIndex - The searchSingleIndex object.\n     * @param searchSingleIndex.indexName - Name of the index on which to perform the operation.\n     * @param searchSingleIndex.searchParams - The searchParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSingleIndex<T>(\n      { indexName, searchParams }: SearchSingleIndexProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchResponse<T>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchSingleIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/query'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchParams ? searchParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for synonyms in your index.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchSynonyms - The searchSynonyms object.\n     * @param searchSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param searchSynonyms.searchSynonymsParams - Body of the `searchSynonyms` operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSynonyms(\n      { indexName, searchSynonymsParams }: SearchSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchSynonymsResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/search'.replace(\n        '{indexName}',\n        encodeURIComponent(indexName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchSynonymsParams ? searchSynonymsParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.  To ensure rapid updates, the user IDs index isn\\'t built at the same time as the mapping. Instead, it\\'s built every 12 hours, at the same time as the update of user ID usage. For example, if you add or move a user ID, the search will show an old value until the next time the mapping is rebuilt (every 12 hours).\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param searchUserIdsParams - The searchUserIdsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchUserIds(\n      searchUserIdsParams: SearchUserIdsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchUserIdsResponse> {\n      if (!searchUserIdsParams) {\n        throw new Error('Parameter `searchUserIdsParams` is required when calling `searchUserIds`.');\n      }\n\n      if (!searchUserIdsParams.query) {\n        throw new Error('Parameter `searchUserIdsParams.query` is required when calling `searchUserIds`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchUserIdsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Turns standard stop word dictionary entries on or off for a given language.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param dictionarySettingsParams - The dictionarySettingsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setDictionarySettings(\n      dictionarySettingsParams: DictionarySettingsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!dictionarySettingsParams) {\n        throw new Error('Parameter `dictionarySettingsParams` is required when calling `setDictionarySettings`.');\n      }\n\n      if (!dictionarySettingsParams.disableStandardEntries) {\n        throw new Error(\n          'Parameter `dictionarySettingsParams.disableStandardEntries` is required when calling `setDictionarySettings`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/*/settings';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: dictionarySettingsParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Update the specified index settings.  Index settings that you don\\'t specify are left unchanged. Specify `null` to reset a setting to its default value.  For best performance, update the index settings before you add new records to your index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param setSettings - The setSettings object.\n     * @param setSettings.indexName - Name of the index on which to perform the operation.\n     * @param setSettings.indexSettings - The indexSettings object.\n     * @param setSettings.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setSettings(\n      { indexName, indexSettings, forwardToReplicas }: SetSettingsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `setSettings`.');\n      }\n\n      if (!indexSettings) {\n        throw new Error('Parameter `indexSettings` is required when calling `setSettings`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/settings'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: indexSettings,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Replaces the permissions of an existing API key.  Any unspecified attribute resets that attribute to its default value.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param updateApiKey - The updateApiKey object.\n     * @param updateApiKey.key - API key.\n     * @param updateApiKey.apiKey - The apiKey object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateApiKey({ key, apiKey }: UpdateApiKeyProps, requestOptions?: RequestOptions): Promise<UpdateApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `updateApiKey`.');\n      }\n\n      if (!apiKey) {\n        throw new Error('Parameter `apiKey` is required when calling `updateApiKey`.');\n      }\n\n      if (!apiKey.acl) {\n        throw new Error('Parameter `apiKey.acl` is required when calling `updateApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: apiKey,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport { createXhrRequester } from '@algolia/requester-browser-xhr';\n\nimport {\n  createBrowserLocalStorageCache,\n  createFallbackableCache,\n  createMemoryCache,\n  createNullLogger,\n} from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { apiClientVersion, createSearchClient } from '../src/searchClient';\n\nexport { apiClientVersion } from '../src/searchClient';\n\nexport * from '../model';\n\nimport type {} from '../model';\n\nexport function searchClient(appId: string, apiKey: string, options?: ClientOptions | undefined): SearchClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  return createSearchClient({\n    appId,\n    apiKey,\n    timeouts: {\n      connect: 1000,\n      read: 2000,\n      write: 30000,\n    },\n    logger: createNullLogger(),\n    requester: createXhrRequester(),\n    algoliaAgents: [{ segment: 'Browser' }],\n    authMode: 'WithinQueryParameters',\n    responsesCache: createMemoryCache(),\n    requestsCache: createMemoryCache({ serializable: false }),\n    hostsCache: createFallbackableCache({\n      caches: [createBrowserLocalStorageCache({ key: `${apiClientVersion}-${appId}` }), createMemoryCache()],\n    }),\n    ...options,\n  });\n}\n\nexport type SearchClient = ReturnType<typeof createSearchClient>;\n"], "mappings": "AAIO,SAASA,GAAgC,CAC9C,SAASC,EAAKC,EAAwC,CACpD,OAAO,IAAI,QAASC,GAAY,CAC9B,IAAMC,EAAgB,IAAI,eAC1BA,EAAc,KAAKF,EAAQ,OAAQA,EAAQ,IAAK,EAAI,EAEpD,OAAO,KAAKA,EAAQ,OAAO,EAAE,QAASG,GAAQD,EAAc,iBAAiBC,EAAKH,EAAQ,QAAQG,CAAG,CAAC,CAAC,EAEvG,IAAMC,EAAgB,CAACC,EAAiBC,IAC/B,WAAW,IAAM,CACtBJ,EAAc,MAAM,EAEpBD,EAAQ,CACN,OAAQ,EACR,QAAAK,EACA,WAAY,EACd,CAAC,CACH,EAAGD,CAAO,EAGNE,EAAiBH,EAAcJ,EAAQ,eAAgB,oBAAoB,EAE7EQ,EAEJN,EAAc,mBAAqB,IAAY,CACzCA,EAAc,WAAaA,EAAc,QAAUM,IAAoB,SACzE,aAAaD,CAAc,EAE3BC,EAAkBJ,EAAcJ,EAAQ,gBAAiB,gBAAgB,EAE7E,EAEAE,EAAc,QAAU,IAAY,CAE9BA,EAAc,SAAW,IAC3B,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,cAAgB,yBACvC,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,EAEL,EAEAA,EAAc,OAAS,IAAY,CACjC,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,aACvB,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,CACH,EAEAA,EAAc,KAAKF,EAAQ,IAAI,CACjC,CAAC,CACH,CAEA,MAAO,CAAE,KAAAD,CAAK,CAChB,CChEO,SAASU,EAA+BC,EAA4C,CACzF,IAAIC,EAEEC,EAAe,qBAAqBF,EAAQ,GAAG,GAErD,SAASG,GAAsB,CAC7B,OAAIF,IAAY,SACdA,EAAUD,EAAQ,cAAgB,OAAO,cAGpCC,CACT,CAEA,SAASG,GAA+C,CACtD,OAAO,KAAK,MAAMD,EAAW,EAAE,QAAQD,CAAY,GAAK,IAAI,CAC9D,CAEA,SAASG,EAAaC,EAAsC,CAC1DH,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAEA,SAASC,GAAiC,CACxC,IAAMC,EAAaR,EAAQ,WAAaA,EAAQ,WAAa,IAAO,KAC9DM,EAAYF,EAA2C,EAEvDK,EAAiD,OAAO,YAC5D,OAAO,QAAQH,CAAS,EAAE,OAAO,CAAC,CAAC,CAAEI,CAAS,IACrCA,EAAU,YAAc,MAChC,CACH,EAIA,GAFAL,EAAaI,CAA8C,EAEvD,CAACD,EACH,OAGF,IAAMG,EAAuC,OAAO,YAClD,OAAO,QAAQF,CAA8C,EAAE,OAAO,CAAC,CAAC,CAAEC,CAAS,IAAM,CACvF,IAAME,EAAmB,IAAI,KAAK,EAAE,QAAQ,EAG5C,MAAO,EAFWF,EAAU,UAAYF,EAAaI,EAGvD,CAAC,CACH,EAEAP,EAAaM,CAAoC,CACnD,CAEA,MAAO,CACL,IACEE,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAM,QAAQ,QAAQ,CAC9B,EACiB,CACjB,OAAO,QAAQ,QAAQ,EACpB,KAAK,KACJR,EAAyB,EAElBH,EAAoD,EAAE,KAAK,UAAUS,CAAG,CAAC,EACjF,EACA,KAAMG,GACE,QAAQ,IAAI,CAACA,EAAQA,EAAM,MAAQF,EAAa,EAAGE,IAAU,MAAS,CAAC,CAC/E,EACA,KAAK,CAAC,CAACA,EAAOC,CAAM,IACZ,QAAQ,IAAI,CAACD,EAAOC,GAAUF,EAAO,KAAKC,CAAK,CAAC,CAAC,CACzD,EACA,KAAK,CAAC,CAACA,CAAK,IAAMA,CAAK,CAC5B,EAEA,IAAYH,EAAmCG,EAAgC,CAC7E,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMV,EAAYF,EAAa,EAE/B,OAAAE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAAI,CAC/B,UAAW,IAAI,KAAK,EAAE,QAAQ,EAC9B,MAAAG,CACF,EAEAb,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,EAErDU,CACT,CAAC,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMP,EAAYF,EAAa,EAE/B,OAAOE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAEpCV,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAAC,CACH,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClCH,EAAW,EAAE,WAAWD,CAAY,CACtC,CAAC,CACH,CACF,CACF,CCvGO,SAASgB,GAAyB,CACvC,MAAO,CACL,IACEC,EACAL,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CAGjB,OAFcD,EAAa,EAEd,KAAMM,GAAW,QAAQ,IAAI,CAACA,EAAQL,EAAO,KAAKK,CAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,CAAM,IAAMA,CAAM,CACrG,EAEA,IAAYD,EAAoCH,EAAgC,CAC9E,OAAO,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOG,EAAmD,CACxD,OAAO,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCzBO,SAASE,EAAwBrB,EAA0C,CAChF,IAAMsB,EAAS,CAAC,GAAGtB,EAAQ,MAAM,EAC3BuB,EAAUD,EAAO,MAAM,EAE7B,OAAIC,IAAY,OACPL,EAAgB,EAGlB,CACL,IACEL,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,OAAOQ,EAAQ,IAAIV,EAAKC,EAAcC,CAAM,EAAE,MAAM,IAC3CM,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKC,EAAcC,CAAM,CACzE,CACH,EAEA,IAAYF,EAAmCG,EAAgC,CAC7E,OAAOO,EAAQ,IAAIV,EAAKG,CAAK,EAAE,MAAM,IAC5BK,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKG,CAAK,CAC1D,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAOU,EAAQ,OAAOV,CAAG,EAAE,MAAM,IACxBQ,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,OAAOT,CAAG,CACtD,CACH,EAEA,OAAuB,CACrB,OAAOU,EAAQ,MAAM,EAAE,MAAM,IACpBF,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,MAAM,CAClD,CACH,CACF,CACF,CCxCO,SAASE,EAAkBxB,EAA8B,CAAE,aAAc,EAAK,EAAU,CAC7F,IAAIyB,EAA6B,CAAC,EAElC,MAAO,CACL,IACEZ,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,IAAMW,EAAc,KAAK,UAAUb,CAAG,EAEtC,GAAIa,KAAeD,EACjB,OAAO,QAAQ,QAAQzB,EAAQ,aAAe,KAAK,MAAMyB,EAAMC,CAAW,CAAC,EAAID,EAAMC,CAAW,CAAC,EAGnG,IAAMC,EAAUb,EAAa,EAE7B,OAAOa,EAAQ,KAAMX,GAAkBD,EAAO,KAAKC,CAAK,CAAC,EAAE,KAAK,IAAMW,CAAO,CAC/E,EAEA,IAAYd,EAAmCG,EAAgC,CAC7E,OAAAS,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAAIb,EAAQ,aAAe,KAAK,UAAUgB,CAAK,EAAIA,EAErE,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOH,EAAsD,CAC3D,cAAOY,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAEzB,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAAY,EAAQ,CAAC,EAEF,QAAQ,QAAQ,CACzB,CACF,CACF,CExCO,SAASG,EAAmBC,EAA+B,CAChE,IAAMC,EAAe,CACnB,MAAO,2BAA2BD,CAAO,IACzC,IAAIE,EAA4C,CAC9C,IAAMC,EAAoB,KAAKD,EAAQ,OAAO,GAAGA,EAAQ,UAAY,OAAY,KAAKA,EAAQ,OAAO,IAAM,EAAE,GAE7G,OAAID,EAAa,MAAM,QAAQE,CAAiB,IAAM,KACpDF,EAAa,MAAQ,GAAGA,EAAa,KAAK,GAAGE,CAAiB,IAGzDF,CACT,CACF,EAEA,OAAOA,CACT,CCfO,SAASG,EACdC,EACAC,EACAC,EAAqB,gBAIrB,CACA,IAAMC,EAAc,CAClB,oBAAqBF,EACrB,2BAA4BD,CAC9B,EAEA,MAAO,CACL,SAAmB,CACjB,OAAOE,IAAa,gBAAkBC,EAAc,CAAC,CACvD,EAEA,iBAAmC,CACjC,OAAOD,IAAa,wBAA0BC,EAAc,CAAC,CAC/D,CACF,CACF,CCZO,SAASC,EAAiC,CAC/C,KAAAC,EACA,SAAAC,EACA,WAAAC,EACA,MAAAC,EACA,QAAAC,EAAU,IAAc,CAC1B,EAAyD,CACvD,IAAMC,EAASC,GACN,IAAI,QAAmB,CAACC,EAASC,IAAW,CACjDR,EAAKM,CAAgB,EAClB,KAAK,MAAOG,IACPP,GACF,MAAMA,EAAWO,CAAQ,EAGvB,MAAMR,EAASQ,CAAQ,EAClBF,EAAQE,CAAQ,EAGrBN,GAAU,MAAMA,EAAM,SAASM,CAAQ,EAClCD,EAAO,IAAI,MAAM,MAAML,EAAM,QAAQM,CAAQ,CAAC,CAAC,EAGjD,WACL,IAAM,CACJJ,EAAMI,CAAQ,EAAE,KAAKF,CAAO,EAAE,MAAMC,CAAM,CAC5C,EACA,MAAMJ,EAAQ,CAChB,EACD,EACA,MAAOM,GAAQ,CACdF,EAAOE,CAAG,CACZ,CAAC,CACL,CAAC,EAGH,OAAOL,EAAM,CACf,CCxCO,SAASM,EAAgB,CAAE,cAAAC,EAAe,OAAAC,EAAQ,QAAAvB,CAAQ,EAAkC,CACjG,IAAMwB,EAAsBzB,EAAmBC,CAAO,EAAE,IAAI,CAC1D,QAASuB,EACT,QAAAvB,CACF,CAAC,EAED,OAAAsB,EAAc,QAASrB,GAAiBuB,EAAoB,IAAIvB,CAAY,CAAC,EAEtEuB,CACT,CChBO,SAASC,GAA2B,CACzC,MAAO,CACL,MAAMC,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,EACA,KAAKD,EAAkBC,EAAwC,CAC7D,OAAO,QAAQ,QAAQ,CACzB,EACA,MAAMD,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCVA,IAAMC,EAAmB,EAAI,GAAK,IAE3B,SAASC,EAAmBC,EAAYC,EAAiC,KAAoB,CAClG,IAAMC,EAAa,KAAK,IAAI,EAE5B,SAASC,GAAgB,CACvB,OAAOF,IAAW,MAAQ,KAAK,IAAI,EAAIC,EAAaJ,CACtD,CAEA,SAASM,GAAsB,CAC7B,OAAOH,IAAW,aAAe,KAAK,IAAI,EAAIC,GAAcJ,CAC9D,CAEA,MAAO,CAAE,GAAGE,EAAM,OAAAC,EAAQ,WAAAC,EAAY,KAAAC,EAAM,WAAAC,CAAW,CACzD,CChBO,IAAMC,EAAN,cAA2B,KAAM,CAC7B,KAAe,eAExB,YAAYC,EAAiBC,EAAc,CACzC,MAAMD,CAAO,EAETC,IACF,KAAK,KAAOA,EAEhB,CACF,EAoBO,IAAMC,EAAN,cAAkCC,CAAa,CACpD,WAEA,YAAYC,EAAiBC,EAA0BC,EAAc,CACnE,MAAMF,EAASE,CAAI,EAEnB,KAAK,WAAaD,CACpB,CACF,EAEaE,GAAN,cAAyBL,CAAoB,CAClD,YAAYG,EAA0B,CACpC,MACE,yJACAA,EACA,YACF,CACF,CACF,EAEaG,EAAN,cAAuBN,CAAoB,CAChD,OAEA,YAAYE,EAAiBK,EAAgBJ,EAA0BC,EAAO,WAAY,CACxF,MAAMF,EAASC,EAAYC,CAAI,EAC/B,KAAK,OAASG,CAChB,CACF,EAEaC,GAAN,cAAmCP,CAAa,CACrD,SAEA,YAAYC,EAAiBO,EAAoB,CAC/C,MAAMP,EAAS,sBAAsB,EACrC,KAAK,SAAWO,CAClB,CACF,EAmBaC,GAAN,cAA+BJ,CAAS,CAC7C,MAEA,YAAYJ,EAAiBK,EAAgBI,EAAsBR,EAA0B,CAC3F,MAAMD,EAASK,EAAQJ,EAAY,kBAAkB,EACrD,KAAK,MAAQQ,CACf,CACF,EC3FO,SAASC,EAAeC,EAAyB,CACtD,IAAMC,EAAgBD,EAEtB,QAASE,EAAIF,EAAM,OAAS,EAAGE,EAAI,EAAGA,IAAK,CACzC,IAAMC,EAAI,KAAK,MAAM,KAAK,OAAO,GAAKD,EAAI,EAAE,EACtCE,EAAIJ,EAAME,CAAC,EAEjBD,EAAcC,CAAC,EAAIF,EAAMG,CAAC,EAC1BF,EAAcE,CAAC,EAAIC,CACrB,CAEA,OAAOH,CACT,CAEO,SAASI,GAAaC,EAAYC,EAAcC,EAA0C,CAC/F,IAAMC,EAA0BC,GAAyBF,CAAe,EACpEG,EAAM,GAAGL,EAAK,QAAQ,MAAMA,EAAK,GAAG,GAAGA,EAAK,KAAO,IAAIA,EAAK,IAAI,GAAK,EAAE,IACzEC,EAAK,OAAO,CAAC,IAAM,IAAMA,EAAK,UAAU,CAAC,EAAIA,CAC/C,GAEA,OAAIE,EAAwB,SAC1BE,GAAO,IAAIF,CAAuB,IAG7BE,CACT,CAEO,SAASD,GAAyBE,EAAqC,CAC5E,OAAO,OAAO,KAAKA,CAAU,EAC1B,OAAQC,GAAQD,EAAWC,CAAG,IAAM,MAAS,EAC7C,KAAK,EACL,IACEA,GACC,GAAGA,CAAG,IAAI,mBACR,OAAO,UAAU,SAAS,KAAKD,EAAWC,CAAG,CAAC,IAAM,iBAChDD,EAAWC,CAAG,EAAE,KAAK,GAAG,EACxBD,EAAWC,CAAG,CACpB,EAAE,QAAQ,MAAO,KAAK,CAAC,EAC3B,EACC,KAAK,GAAG,CACb,CAEO,SAASC,GAAcC,EAAkBC,EAAoD,CAClG,GAAID,EAAQ,SAAW,OAAUA,EAAQ,OAAS,QAAaC,EAAe,OAAS,OACrF,OAGF,IAAMC,EAAO,MAAM,QAAQF,EAAQ,IAAI,EAAIA,EAAQ,KAAO,CAAE,GAAGA,EAAQ,KAAM,GAAGC,EAAe,IAAK,EAEpG,OAAO,KAAK,UAAUC,CAAI,CAC5B,CAEO,SAASC,GACdC,EACAC,EACAC,EACS,CACT,IAAMC,EAAmB,CACvB,OAAQ,mBACR,GAAGH,EACH,GAAGC,EACH,GAAGC,CACL,EACME,EAA6B,CAAC,EAEpC,cAAO,KAAKD,CAAO,EAAE,QAASE,GAAW,CACvC,IAAMC,EAAQH,EAAQE,CAAM,EAC5BD,EAAkBC,EAAO,YAAY,CAAC,EAAIC,CAC5C,CAAC,EAEMF,CACT,CAEO,SAASG,GAA4B9B,EAA6B,CACvE,GAAI,CACF,OAAO,KAAK,MAAMA,EAAS,OAAO,CACpC,OAAS+B,EAAG,CACV,MAAM,IAAIhC,GAAsBgC,EAAY,QAAS/B,CAAQ,CAC/D,CACF,CAEO,SAASgC,GAAmB,CAAE,QAAAC,EAAS,OAAAnC,CAAO,EAAaoC,EAAiC,CACjG,GAAI,CACF,IAAMC,EAAS,KAAK,MAAMF,CAAO,EACjC,MAAI,UAAWE,EACN,IAAIlC,GAAiBkC,EAAO,QAASrC,EAAQqC,EAAO,MAAOD,CAAU,EAEvE,IAAIrC,EAASsC,EAAO,QAASrC,EAAQoC,CAAU,CACxD,MAAQ,CAER,CACA,OAAO,IAAIrC,EAASoC,EAASnC,EAAQoC,CAAU,CACjD,CC7FO,SAASE,GAAe,CAAE,WAAAC,EAAY,OAAAvC,CAAO,EAAuC,CACzF,MAAO,CAACuC,GAAc,CAAC,CAACvC,IAAW,CACrC,CAEO,SAASwC,GAAY,CAAE,WAAAD,EAAY,OAAAvC,CAAO,EAAuC,CACtF,OAAOuC,GAAcD,GAAe,CAAE,WAAAC,EAAY,OAAAvC,CAAO,CAAC,GAAM,CAAC,EAAEA,EAAS,OAAS,GAAK,CAAC,EAAEA,EAAS,OAAS,CACjH,CAEO,SAASyC,GAAU,CAAE,OAAAzC,CAAO,EAAsC,CACvE,MAAO,CAAC,EAAEA,EAAS,OAAS,CAC9B,CCVO,SAAS0C,GAA6B9C,EAAwC,CACnF,OAAOA,EAAW,IAAKwC,GAAeO,EAA6BP,CAAU,CAAC,CAChF,CAEO,SAASO,EAA6BP,EAAoC,CAC/E,IAAMQ,EAA2BR,EAAW,QAAQ,QAAQ,mBAAmB,EAC3E,CAAE,oBAAqB,OAAQ,EAC/B,CAAC,EAEL,MAAO,CACL,GAAGA,EACH,QAAS,CACP,GAAGA,EAAW,QACd,QAAS,CACP,GAAGA,EAAW,QAAQ,QACtB,GAAGQ,CACL,CACF,CACF,CACF,CCCO,SAASC,EAAkB,CAChC,MAAAC,EACA,WAAAC,EACA,YAAAtB,EACA,OAAAuB,EACA,oBAAAC,EACA,aAAAC,EACA,SAAAC,EACA,UAAAC,EACA,cAAAC,EACA,eAAAC,CACF,EAAoC,CAClC,eAAeC,EAAuBC,EAAoD,CACxF,IAAMC,EAAgB,MAAM,QAAQ,IAClCD,EAAgB,IAAKE,GACZX,EAAW,IAAIW,EAAgB,IAC7B,QAAQ,QAAQC,EAAmBD,CAAc,CAAC,CAC1D,CACF,CACH,EACME,EAAUH,EAAc,OAAQ7C,GAASA,EAAK,KAAK,CAAC,EACpDiD,EAAgBJ,EAAc,OAAQ7C,GAASA,EAAK,WAAW,CAAC,EAGhEkD,EAAiB,CAAC,GAAGF,EAAS,GAAGC,CAAa,EAGpD,MAAO,CACL,MAH+BC,EAAe,OAAS,EAAIA,EAAiBN,EAI5E,WAAWO,EAAuBC,EAA6B,CAe7D,OAFEH,EAAc,SAAW,GAAKE,IAAkB,EAAI,EAAIF,EAAc,OAAS,EAAIE,GAE1DC,CAC7B,CACF,CACF,CAEA,eAAeC,EACb5C,EACAC,EACA4C,EAAS,GACW,CACpB,IAAMtE,EAA2B,CAAC,EAK5B2B,EAAOH,GAAcC,EAASC,CAAc,EAC5CM,EAAUJ,GAAiBC,EAAaJ,EAAQ,QAASC,EAAe,OAAO,EAG/E6C,EACJ9C,EAAQ,SAAW,MACf,CACE,GAAGA,EAAQ,KACX,GAAGC,EAAe,IACpB,EACA,CAAC,EAEDR,EAAmC,CACvC,GAAGmC,EACH,GAAG5B,EAAQ,gBACX,GAAG8C,CACL,EAMA,GAJIjB,EAAa,QACfpC,EAAgB,iBAAiB,EAAIoC,EAAa,OAGhD5B,GAAkBA,EAAe,gBACnC,QAAWH,KAAO,OAAO,KAAKG,EAAe,eAAe,EAKxD,CAACA,EAAe,gBAAgBH,CAAG,GACnC,OAAO,UAAU,SAAS,KAAKG,EAAe,gBAAgBH,CAAG,CAAC,IAAM,kBAExEL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAEzDL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAAE,SAAS,EAK1E,IAAI4C,EAAgB,EAEdK,EAAQ,MACZC,EACAC,IACuB,CAIvB,IAAM1D,EAAOyD,EAAe,IAAI,EAChC,GAAIzD,IAAS,OACX,MAAM,IAAId,GAAW4C,GAA6B9C,CAAU,CAAC,EAG/D,IAAM2E,EAAU,CAAE,GAAGpB,EAAU,GAAG7B,EAAe,QAAS,EAEpDkD,EAAsB,CAC1B,KAAAjD,EACA,QAAAK,EACA,OAAQP,EAAQ,OAChB,IAAKV,GAAaC,EAAMS,EAAQ,KAAMP,CAAe,EACrD,eAAgBwD,EAAWP,EAAeQ,EAAQ,OAAO,EACzD,gBAAiBD,EAAWP,EAAeG,EAASK,EAAQ,KAAOA,EAAQ,KAAK,CAClF,EAOME,EAAoBvE,GAAmC,CAC3D,IAAMkC,EAAyB,CAC7B,QAASoC,EACT,SAAAtE,EACA,KAAAU,EACA,UAAWyD,EAAe,MAC5B,EAEA,OAAAzE,EAAW,KAAKwC,CAAU,EAEnBA,CACT,EAEMlC,EAAW,MAAMkD,EAAU,KAAKoB,CAAO,EAE7C,GAAIhC,GAAYtC,CAAQ,EAAG,CACzB,IAAMkC,EAAaqC,EAAiBvE,CAAQ,EAG5C,OAAIA,EAAS,YACX6D,IAOFf,EAAO,KAAK,oBAAqBL,EAA6BP,CAAU,CAAC,EAOzE,MAAMW,EAAW,IAAInC,EAAM+C,EAAmB/C,EAAMV,EAAS,WAAa,YAAc,MAAM,CAAC,EAExFkE,EAAMC,EAAgBC,CAAU,CACzC,CAEA,GAAI7B,GAAUvC,CAAQ,EACpB,OAAO8B,GAAmB9B,CAAQ,EAGpC,MAAAuE,EAAiBvE,CAAQ,EACnBgC,GAAmBhC,EAAUN,CAAU,CAC/C,EAUM4D,EAAkBV,EAAM,OAC3BlC,GAASA,EAAK,SAAW,cAAgBsD,EAAStD,EAAK,SAAW,OAASA,EAAK,SAAW,QAC9F,EACM8D,EAAU,MAAMnB,EAAuBC,CAAe,EAE5D,OAAOY,EAAM,CAAC,GAAGM,EAAQ,KAAK,EAAE,QAAQ,EAAGA,EAAQ,UAAU,CAC/D,CAEA,SAASC,EAAyBtD,EAAkBC,EAAiC,CAAC,EAAuB,CAK3G,IAAM4C,EAAS7C,EAAQ,oBAAsBA,EAAQ,SAAW,MAChE,GAAI,CAAC6C,EAKH,OAAOD,EAA4B5C,EAASC,EAAgB4C,CAAM,EAGpE,IAAMU,EAAyB,IAMtBX,EAA4B5C,EAASC,CAAc,EAc5D,IANkBA,EAAe,WAAaD,EAAQ,aAMpC,GAChB,OAAOuD,EAAuB,EAQhC,IAAMzD,EAAM,CACV,QAAAE,EACA,eAAAC,EACA,YAAa,CACX,gBAAiB2B,EACjB,QAASxB,CACX,CACF,EAMA,OAAO6B,EAAe,IACpBnC,EACA,IAKSkC,EAAc,IAAIlC,EAAK,IAM5BkC,EACG,IAAIlC,EAAKyD,EAAuB,CAAC,EACjC,KACE1E,GAAa,QAAQ,IAAI,CAACmD,EAAc,OAAOlC,CAAG,EAAGjB,CAAQ,CAAC,EAC9D2E,GAAQ,QAAQ,IAAI,CAACxB,EAAc,OAAOlC,CAAG,EAAG,QAAQ,OAAO0D,CAAG,CAAC,CAAC,CACvE,EACC,KAAK,CAAC,CAACC,EAAG5E,CAAQ,IAAMA,CAAQ,CACrC,EAEF,CAME,KAAOA,GAAaoD,EAAe,IAAInC,EAAKjB,CAAQ,CACtD,CACF,CACF,CAEA,MAAO,CACL,WAAA6C,EACA,UAAAK,EACA,SAAAD,EACA,OAAAH,EACA,aAAAE,EACA,YAAAzB,EACA,oBAAAwB,EACA,MAAAH,EACA,QAAS6B,EACT,cAAAtB,EACA,eAAAC,CACF,CACF,CEzKO,IAAMyB,EAAmB,SAEhC,SAASC,GAAgBC,EAAuB,CAC9C,MACE,CACE,CACE,IAAK,GAAGA,CAAK,mBACb,OAAQ,OACR,SAAU,OACZ,EACA,CACE,IAAK,GAAGA,CAAK,eACb,OAAQ,QACR,SAAU,OACZ,CACF,EACA,OACAC,EAAQ,CACN,CACE,IAAK,GAAGD,CAAK,oBACb,OAAQ,YACR,SAAU,OACZ,EACA,CACE,IAAK,GAAGA,CAAK,oBACb,OAAQ,YACR,SAAU,OACZ,EACA,CACE,IAAK,GAAGA,CAAK,oBACb,OAAQ,YACR,SAAU,OACZ,CACF,CAAC,CACH,CACF,CAEO,SAASE,EAAmB,CACjC,MAAOC,EACP,OAAQC,EACR,SAAAC,EACA,cAAAC,EACA,GAAGC,CACL,EAAwB,CACtB,IAAMC,EAAOC,EAAWN,EAAaC,EAAcC,CAAQ,EACrDK,EAAcC,EAAkB,CACpC,MAAOZ,GAAgBI,CAAW,EAClC,GAAGI,EACH,aAAcK,EAAgB,CAC5B,cAAAN,EACA,OAAQ,SACR,QAASR,CACX,CAAC,EACD,YAAa,CACX,eAAgB,aAChB,GAAGU,EAAK,QAAQ,EAChB,GAAGD,EAAQ,WACb,EACA,oBAAqB,CACnB,GAAGC,EAAK,gBAAgB,EACxB,GAAGD,EAAQ,mBACb,CACF,CAAC,EAED,MAAO,CACL,YAAAG,EAKA,MAAOP,EAKP,OAAQC,EAKR,YAA4B,CAC1B,OAAO,QAAQ,IAAI,CAACM,EAAY,cAAc,MAAM,EAAGA,EAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,IAAG,EAAY,CAClH,EAKA,IAAI,KAAc,CAChB,OAAOA,EAAY,aAAa,KAClC,EAQA,gBAAgBG,EAAiBC,EAAoC,CACnEJ,EAAY,aAAa,IAAI,CAAE,QAAAG,EAAS,QAAAC,CAAQ,CAAC,CACnD,EAQA,gBAAgB,CAAE,OAAAC,CAAO,EAA6B,CAChD,CAACV,GAAYA,IAAa,gBAC5BK,EAAY,YAAY,mBAAmB,EAAIK,EAE/CL,EAAY,oBAAoB,mBAAmB,EAAIK,CAE3D,EAaA,YACE,CACE,UAAAC,EACA,OAAAC,EACA,WAAAC,EAAa,GACb,QAAAC,EAAWC,GAA+B,KAAK,IAAIA,EAAa,IAAK,GAAI,CAC3E,EACAC,EAC0B,CAC1B,IAAID,EAAa,EAEjB,OAAOE,EAAsB,CAC3B,KAAM,IAAM,KAAK,QAAQ,CAAE,UAAAN,EAAW,OAAAC,CAAO,EAAGI,CAAc,EAC9D,SAAWE,GAAaA,EAAS,SAAW,YAC5C,WAAY,IAAOH,GAAc,EACjC,MAAO,CACL,SAAU,IAAMA,GAAcF,EAC9B,QAAS,IAAM,4CAA4CE,CAAU,IAAIF,CAAU,GACrF,EACA,QAAS,IAAMC,EAAQC,CAAU,CACnC,CAAC,CACH,EAYA,eACE,CACE,OAAAH,EACA,WAAAC,EAAa,GACb,QAAAC,EAAWC,GAA+B,KAAK,IAAIA,EAAa,IAAK,GAAI,CAC3E,EACAC,EAC0B,CAC1B,IAAID,EAAa,EAEjB,OAAOE,EAAsB,CAC3B,KAAM,IAAM,KAAK,WAAW,CAAE,OAAAL,CAAO,EAAGI,CAAc,EACtD,SAAWE,GAAaA,EAAS,SAAW,YAC5C,WAAY,IAAOH,GAAc,EACjC,MAAO,CACL,SAAU,IAAMA,GAAcF,EAC9B,QAAS,IAAM,4CAA4CE,CAAU,IAAIF,CAAU,GACrF,EACA,QAAS,IAAMC,EAAQC,CAAU,CACnC,CAAC,CACH,EAcA,cACE,CACE,UAAAI,EACA,IAAAC,EACA,OAAAV,EACA,WAAAG,EAAa,GACb,QAAAC,EAAWC,GAA+B,KAAK,IAAIA,EAAa,IAAK,GAAI,CAC3E,EACAC,EACwC,CACxC,IAAID,EAAa,EACXM,EAAsE,CAC1E,WAAY,IAAON,GAAc,EACjC,MAAO,CACL,SAAU,IAAMA,GAAcF,EAC9B,QAAS,IAAM,4CAA4CE,CAAU,IAAIF,CAAU,GACrF,EACA,QAAS,IAAMC,EAAQC,CAAU,CACnC,EAEA,GAAII,IAAc,SAAU,CAC1B,GAAI,CAACT,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,OAAOO,EAAsB,CAC3B,GAAGI,EACH,KAAM,IAAM,KAAK,UAAU,CAAE,IAAAD,CAAI,EAAGJ,CAAc,EAClD,SAAWE,GAAa,CACtB,QAAWI,KAAS,OAAO,KAAKZ,CAAM,EAAG,CACvC,IAAMa,EAAQb,EAAOY,CAAqB,EACpCE,EAAWN,EAASI,CAAqB,EAC/C,GAAI,MAAM,QAAQC,CAAK,GAAK,MAAM,QAAQC,CAAQ,GAChD,GAAID,EAAM,SAAWC,EAAS,QAAUD,EAAM,KAAK,CAACE,EAAGC,IAAUD,IAAMD,EAASE,CAAK,CAAC,EACpF,MAAO,WAEAH,IAAUC,EACnB,MAAO,EAEX,CACA,MAAO,EACT,CACF,CAAC,CACH,CAEA,OAAOP,EAAsB,CAC3B,GAAGI,EACH,KAAM,IACJ,KAAK,UAAU,CAAE,IAAAD,CAAI,EAAGJ,CAAc,EAAE,MAAOW,GAAoB,CACjE,GAAIA,EAAM,SAAW,IAIrB,MAAMA,CACR,CAAC,EACH,SAAWT,GAAcC,IAAc,MAAQD,IAAa,OAAYA,IAAa,MACvF,CAAC,CACH,EAaA,cACE,CAAE,UAAAP,EAAW,aAAAiB,EAAc,GAAGC,CAAqB,EACnDb,EAC4B,CAC5B,OAAOC,EAAyC,CAC9C,KAAOa,GACE,KAAK,OACV,CACE,UAAAnB,EACA,aAAc,CACZ,OAAQmB,EAAmBA,EAAiB,OAAS,OACrD,YAAa,IACb,GAAGF,CACL,CACF,EACAZ,CACF,EAEF,SAAWE,GAAaA,EAAS,SAAW,OAC5C,GAAGW,CACL,CAAC,CACH,EAaA,YACE,CAAE,UAAAlB,EAAW,kBAAAoB,EAAmB,GAAGC,CAAmB,EACtDhB,EAC8B,CAC9B,IAAMiB,EAAS,CACb,GAAGF,EACH,YAAaA,GAAmB,aAAe,GACjD,EAEA,OAAOd,EAA2C,CAChD,KAAOa,GACE,KAAK,YACV,CACE,UAAAnB,EACA,kBAAmB,CACjB,GAAGsB,EACH,KAAMH,EAAmBA,EAAiB,KAAO,EAAIG,EAAO,MAAQ,CACtE,CACF,EACAjB,CACF,EAEF,SAAWE,GAAaA,EAAS,KAAK,OAASe,EAAO,YACtD,GAAGD,CACL,CAAC,CACH,EAaA,eACE,CACE,UAAArB,EACA,qBAAAuB,EACA,GAAGC,CACL,EACAnB,EACiC,CACjC,IAAMiB,EAAS,CACb,GAAGC,EACH,KAAMA,GAAsB,MAAQ,EACpC,YAAa,GACf,EAEA,OAAOjB,EAA8C,CACnD,KAAOmB,GAAM,CACX,IAAMC,EAAO,KAAK,eAChB,CACE,UAAA1B,EACA,qBAAsB,CACpB,GAAGsB,EACH,KAAMA,EAAO,IACf,CACF,EACAjB,CACF,EACA,OAAAiB,EAAO,MAAQ,EACRI,CACT,EACA,SAAWnB,GAAaA,EAAS,KAAK,OAASe,EAAO,YACtD,GAAGE,CACL,CAAC,CACH,EAcA,MAAM,aACJ,CAAE,UAAAxB,EAAW,QAAA2B,EAAS,OAAAC,EAAS,YAAa,aAAAC,EAAc,UAAAC,EAAY,GAAK,EAC3EzB,EAC+B,CAC/B,IAAI0B,EAAgC,CAAC,EAC/BC,EAAkC,CAAC,EAEnCC,EAAgBN,EAAQ,QAAQ,EACtC,OAAW,CAACO,EAAGC,CAAG,IAAKF,EACrBF,EAAS,KAAK,CAAE,OAAAH,EAAQ,KAAMO,CAAI,CAAC,GAC/BJ,EAAS,SAAWD,GAAaI,IAAMP,EAAQ,OAAS,KAC1DK,EAAU,KAAK,MAAM,KAAK,MAAM,CAAE,UAAAhC,EAAW,iBAAkB,CAAE,SAAA+B,CAAS,CAAE,EAAG1B,CAAc,CAAC,EAC9F0B,EAAW,CAAC,GAIhB,GAAIF,EACF,QAAWH,KAAQM,EACjB,MAAM,KAAK,YAAY,CAAE,UAAAhC,EAAW,OAAQ0B,EAAK,MAAO,CAAC,EAI7D,OAAOM,CACT,EAaA,MAAM,YACJ,CAAE,UAAAhC,EAAW,QAAA2B,EAAS,aAAAE,EAAc,UAAAC,CAAU,EAC9CzB,EAC0B,CAC1B,OAAO,MAAM,KAAK,aAChB,CAAE,UAAAL,EAAW,QAAA2B,EAAS,OAAQ,YAAa,aAAAE,EAAc,UAAAC,CAAU,EACnEzB,CACF,CACF,EAaA,MAAM,cACJ,CAAE,UAAAL,EAAW,UAAAoC,EAAW,aAAAP,EAAc,UAAAC,CAAU,EAChDzB,EAC0B,CAC1B,OAAO,MAAM,KAAK,aAChB,CACE,UAAAL,EACA,QAASoC,EAAU,IAAKC,IAAc,CAAE,SAAAA,CAAS,EAAE,EACnD,OAAQ,eACR,aAAAR,EACA,UAAAC,CACF,EACAzB,CACF,CACF,EAcA,MAAM,qBACJ,CAAE,UAAAL,EAAW,QAAA2B,EAAS,kBAAAW,EAAmB,aAAAT,EAAc,UAAAC,CAAU,EACjEzB,EAC0B,CAC1B,OAAO,MAAM,KAAK,aAChB,CACE,UAAAL,EACA,QAAA2B,EACA,OAAQW,EAAoB,sBAAwB,8BACpD,UAAAR,EACA,aAAAD,CACF,EACAxB,CACF,CACF,EAcA,MAAM,kBACJ,CAAE,UAAAL,EAAW,QAAA2B,EAAS,UAAAG,EAAW,OAAAS,CAAO,EACxClC,EACoC,CACpC,IAAMmC,EAAe,KAAK,MAAM,KAAK,OAAO,EAAI,GAAO,EAAI,IACrDC,EAAe,GAAGzC,CAAS,QAAQwC,CAAY,GAEjDD,IAAW,SACbA,EAAS,CAAC,WAAY,QAAS,UAAU,GAG3C,GAAI,CACF,IAAIG,EAAwB,MAAM,KAAK,eACrC,CACE,UAAA1C,EACA,qBAAsB,CACpB,UAAW,OACX,YAAayC,EACb,MAAOF,CACT,CACF,EACAlC,CACF,EAEMsC,EAAiB,MAAM,KAAK,aAChC,CAAE,UAAWF,EAAc,QAAAd,EAAS,aAAc,GAAM,UAAAG,CAAU,EAClEzB,CACF,EAEA,MAAM,KAAK,YAAY,CACrB,UAAWoC,EACX,OAAQC,EAAsB,MAChC,CAAC,EAEDA,EAAwB,MAAM,KAAK,eACjC,CACE,UAAA1C,EACA,qBAAsB,CACpB,UAAW,OACX,YAAayC,EACb,MAAOF,CACT,CACF,EACAlC,CACF,EACA,MAAM,KAAK,YAAY,CACrB,UAAWoC,EACX,OAAQC,EAAsB,MAChC,CAAC,EAED,IAAME,EAAwB,MAAM,KAAK,eACvC,CACE,UAAWH,EACX,qBAAsB,CAAE,UAAW,OAAQ,YAAazC,CAAU,CACpE,EACAK,CACF,EACA,aAAM,KAAK,YAAY,CACrB,UAAWoC,EACX,OAAQG,EAAsB,MAChC,CAAC,EAEM,CAAE,sBAAAF,EAAuB,eAAAC,EAAgB,sBAAAC,CAAsB,CACxE,OAAS5B,EAAO,CACd,YAAM,KAAK,YAAY,CAAE,UAAWyB,CAAa,CAAC,EAE5CzB,CACR,CACF,EAEA,MAAM,YAAY,CAAE,UAAAhB,CAAU,EAAuC,CACnE,GAAI,CACF,MAAM,KAAK,YAAY,CAAE,UAAAA,CAAU,CAAC,CACtC,OAASgB,EAAO,CACd,GAAIA,aAAiB6B,GAAY7B,EAAM,SAAW,IAChD,MAAO,GAET,MAAMA,CACR,CAEA,MAAO,EACT,EAUA,cACE8B,EACAzC,EACgD,CAChD,OAAO,KAAK,OAAOyC,EAAoBzC,CAAc,CACvD,EAUA,gBACEyC,EACAzC,EAC2D,CAC3D,OAAO,KAAK,OAAOyC,EAAoBzC,CAAc,CAGvD,EASA,UAAUN,EAAgBM,EAA6D,CACrF,GAAI,CAACN,EACH,MAAM,IAAI,MAAM,0DAA0D,EAG5E,GAAI,CAACA,EAAO,IACV,MAAM,IAAI,MAAM,8DAA8D,EAOhF,IAAMgD,EAAmB,CACvB,OAAQ,OACR,KANkB,UAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMhD,CACR,EAEA,OAAOL,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,kBACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,KAAAW,CAAK,EAC5B3C,EACwC,CACxC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,qEAAqE,EAGvF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,oEAAoE,EAGtF,GAAI,CAACW,EACH,MAAM,IAAI,MAAM,gEAAgE,EASlF,IAAMD,EAAmB,CACvB,OAAQ,MACR,KARkB,oCACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAOnD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMW,CACR,EAEA,OAAOtD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,aAAa4C,EAAgB5C,EAA6D,CACxF,GAAI,CAAC4C,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,GAAI,CAACA,EAAO,OACV,MAAM,IAAI,MAAM,oEAAoE,EAOtF,IAAMF,EAAmB,CACvB,OAAQ,OACR,KANkB,6BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAME,CACR,EAEA,OAAOvD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,aACE,CAAE,eAAA6C,EAAgB,mBAAAC,CAAmB,EACrC9C,EAC4B,CAC5B,GAAI,CAAC6C,EACH,MAAM,IAAI,MAAM,qEAAqE,EAGvF,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,yEAAyE,EAG3F,GAAI,CAACA,EAAmB,QACtB,MAAM,IAAI,MAAM,iFAAiF,EAGnG,IAAMC,EAAc,sBACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCJ,IAAmB,SACrBG,EAAQ,mBAAmB,EAAIH,EAAe,SAAS,GAGzD,IAAMH,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMF,CACR,EAEA,OAAOzD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,MAAM,CAAE,UAAAL,EAAW,iBAAAuD,CAAiB,EAAelD,EAAyD,CAC1G,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,yDAAyD,EAG3E,GAAI,CAACuD,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,GAAI,CAACA,EAAiB,SACpB,MAAM,IAAI,MAAM,yEAAyE,EAO3F,IAAMR,EAAmB,CACvB,OAAQ,OACR,KANkB,+BAA+B,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOrG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMuD,CACR,EAEA,OAAO7D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,mBACE,CAAE,eAAA6C,EAAgB,yBAAAM,CAAyB,EAC3CnD,EAC4B,CAC5B,GAAI,CAAC6C,EACH,MAAM,IAAI,MAAM,2EAA2E,EAG7F,GAAI,CAACM,EACH,MAAM,IAAI,MAAM,qFAAqF,EAGvG,GAAI,CAACA,EAAyB,QAC5B,MAAM,IAAI,MAAM,6FAA6F,EAE/G,GAAI,CAACA,EAAyB,MAC5B,MAAM,IAAI,MAAM,2FAA2F,EAG7G,IAAMJ,EAAc,4BACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCJ,IAAmB,SACrBG,EAAQ,mBAAmB,EAAIH,EAAe,SAAS,GAGzD,IAAMH,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMG,CACR,EAEA,OAAO9D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,uBACE,CAAE,eAAAoD,EAAgB,6BAAAC,CAA6B,EAC/CrD,EAC4B,CAC5B,GAAI,CAACoD,EACH,MAAM,IAAI,MAAM,+EAA+E,EAGjG,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,6FAA6F,EAG/G,GAAI,CAACA,EAA6B,SAChC,MAAM,IAAI,MACR,sGACF,EAUF,IAAMX,EAAmB,CACvB,OAAQ,OACR,KATkB,yCAAyC,QAC3D,mBACA,mBAAmBU,CAAc,CACnC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMC,CACR,EAEA,OAAOhE,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,OAAU,CAAE,UAAAL,EAAW,aAAAiB,CAAa,EAAgBZ,EAA6D,CAC/G,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,0DAA0D,EAO5E,IAAM+C,EAAmB,CACvB,OAAQ,OACR,KANkB,gCAAgC,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOtG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMiB,GAA8B,CAAC,EACrC,mBAAoB,EACtB,EAEA,OAAOvB,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,aAAa,CAAE,UAAAL,CAAU,EAAsBK,EAA6D,CAC1G,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,gEAAgE,EAOlF,IAAM+C,EAAmB,CACvB,OAAQ,OACR,KANkB,+BAA+B,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOrG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAON,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,WACE,CAAE,UAAAL,EAAW,kBAAA2D,CAAkB,EAC/BtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,IAAMoD,EAAc,qCAAqC,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACvGqD,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,cACE,CAAE,UAAAL,EAAW,kBAAA2D,CAAkB,EAC/BtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,iEAAiE,EAGnF,IAAMoD,EAAc,wCAAwC,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EAC1GqD,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,aACE,CAAE,KAAAuD,EAAM,WAAAC,CAAW,EACnBxD,EACkC,CAClC,GAAI,CAACuD,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAMb,EAAmB,CACvB,OAAQ,SACR,KANkB,UAAU,QAAQ,SAAUa,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAOnE,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,UAAU,CAAE,KAAAuD,EAAM,WAAAC,CAAW,EAAmBxD,EAAmE,CACjH,GAAI,CAACuD,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMb,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUa,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAOnE,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,WACE,CAAE,KAAAuD,EAAM,WAAAC,EAAY,KAAAb,CAAK,EACzB3C,EACkC,CAClC,GAAI,CAACuD,EACH,MAAM,IAAI,MAAM,yDAAyD,EAO3E,IAAMb,EAAmB,CACvB,OAAQ,OACR,KANkB,UAAU,QAAQ,SAAUa,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMb,GAAc,CAAC,CACvB,EAEA,OAAOtD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,UACE,CAAE,KAAAuD,EAAM,WAAAC,EAAY,KAAAb,CAAK,EACzB3C,EACkC,CAClC,GAAI,CAACuD,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMb,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUa,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMb,GAAc,CAAC,CACvB,EAEA,OAAOtD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,aAAa,CAAE,IAAAI,CAAI,EAAsBJ,EAAgE,CACvG,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,0DAA0D,EAO5E,IAAMsC,EAAmB,CACvB,OAAQ,SACR,KANkB,gBAAgB,QAAQ,QAAS,mBAAmBtC,CAAG,CAAC,EAO1E,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOf,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,SACE,CAAE,UAAAL,EAAW,eAAA8D,CAAe,EAC5BzD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,4DAA4D,EAG9E,GAAI,CAAC8D,EACH,MAAM,IAAI,MAAM,iEAAiE,EAOnF,IAAMf,EAAmB,CACvB,OAAQ,OACR,KANkB,uCAAuC,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAO7G,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAM8D,CACR,EAEA,OAAOpE,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,YAAY,CAAE,UAAAL,CAAU,EAAqBK,EAA6D,CACxG,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAM+C,EAAmB,CACvB,OAAQ,SACR,KANkB,yBAAyB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAO/F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAON,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,aACE,CAAE,UAAAL,EAAW,SAAAqC,CAAS,EACtBhC,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,+DAA+D,EASjF,IAAMU,EAAmB,CACvB,OAAQ,SACR,KARkB,oCACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAOnD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAO3C,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,WACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,kBAAAsB,CAAkB,EACzCtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,IAAMe,EAAc,0CACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,SACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,aAAa,CAAE,OAAA4C,CAAO,EAAsB5C,EAAgE,CAC1G,GAAI,CAAC4C,EACH,MAAM,IAAI,MAAM,6DAA6D,EAO/E,IAAMF,EAAmB,CACvB,OAAQ,SACR,KANkB,+BAA+B,QAAQ,WAAY,mBAAmBE,CAAM,CAAC,EAO/F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOvD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,cACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,kBAAAsB,CAAkB,EACzCtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,iEAAiE,EAGnF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,IAAMe,EAAc,6CACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,SACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAQA,UAAU,CAAE,IAAAI,CAAI,EAAmBJ,EAA6D,CAC9F,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,uDAAuD,EAOzE,IAAMsC,EAAmB,CACvB,OAAQ,MACR,KANkB,gBAAgB,QAAQ,QAAS,mBAAmBtC,CAAG,CAAC,EAO1E,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOf,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,WAAW,CAAE,OAAAJ,CAAO,EAAoBI,EAA2D,CACjG,GAAI,CAACJ,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAM8C,EAAmB,CACvB,OAAQ,MACR,KANkB,mBAAmB,QAAQ,WAAY,mBAAmB9C,CAAM,CAAC,EAOnF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOP,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,uBAAuBA,EAAoF,CAKzG,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,8BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,sBAAsBA,EAAqF,CAKzG,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,6BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,QACE,CAAE,OAAA0D,EAAQ,OAAAC,EAAQ,UAAAhE,EAAW,KAAAiE,CAAK,EAAkB,CAAC,EACrD5D,EAA6C,OACnB,CAC1B,IAAM+C,EAAc,UACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCS,IAAW,SACbT,EAAgB,OAAYS,EAAO,SAAS,GAG1CC,IAAW,SACbV,EAAgB,OAAYU,EAAO,SAAS,GAG1ChE,IAAc,SAChBsD,EAAgB,UAAetD,EAAU,SAAS,GAGhDiE,IAAS,SACXX,EAAgB,KAAUW,EAAK,SAAS,GAG1C,IAAMlB,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,UACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,qBAAA6B,CAAqB,EAC5C7D,EACkC,CAClC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,4DAA4D,EAG9E,IAAMe,EAAc,oCACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCY,IAAyB,SAC3BZ,EAAgB,qBAA0BY,EAAqB,SAAS,GAG1E,IAAMnB,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,WAAc8D,EAAoC9D,EAAiE,CACjH,GAAI,CAAC8D,EACH,MAAM,IAAI,MAAM,qEAAqE,EAGvF,GAAI,CAACA,EAAiB,SACpB,MAAM,IAAI,MAAM,8EAA8E,EAOhG,IAAMpB,EAAmB,CACvB,OAAQ,OACR,KANkB,uBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMoB,EACN,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAOzE,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,QAAQ,CAAE,UAAAL,EAAW,SAAAqC,CAAS,EAAiBhC,EAAgD,CAC7F,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,2DAA2D,EAG7E,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,0DAA0D,EAS5E,IAAMU,EAAmB,CACvB,OAAQ,MACR,KARkB,0CACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAOnD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAO3C,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,YAAY,CAAE,UAAAL,CAAU,EAAqBK,EAA4D,CACvG,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAM+C,EAAmB,CACvB,OAAQ,MACR,KANkB,kCAAkC,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOxG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAON,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,WAAWA,EAAqE,CAK9E,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,sBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,WAAW,CAAE,UAAAL,EAAW,SAAAqC,CAAS,EAAoBhC,EAAsD,CACzG,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,6DAA6D,EAS/E,IAAMU,EAAmB,CACvB,OAAQ,MACR,KARkB,6CACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAOnD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAO3C,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,QAAQ,CAAE,UAAAL,EAAW,OAAAC,CAAO,EAAiBI,EAA2D,CACtG,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,2DAA2D,EAG7E,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,wDAAwD,EAS1E,IAAM8C,EAAmB,CACvB,OAAQ,MACR,KARkB,uCACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,WAAY,mBAAmBC,CAAM,CAAC,EAO/C,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOP,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,cAAcA,EAA6E,CAKzF,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,0BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,UAAU,CAAE,OAAA+D,CAAO,EAAmB/D,EAAkD,CACtF,GAAI,CAAC+D,EACH,MAAM,IAAI,MAAM,0DAA0D,EAO5E,IAAMrB,EAAmB,CACvB,OAAQ,MACR,KANkB,+BAA+B,QAAQ,WAAY,mBAAmBqB,CAAM,CAAC,EAO/F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAO1E,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,mBACE,CAAE,YAAAgE,CAAY,EAA6B,CAAC,EAC5ChE,EAA6C,OACR,CACrC,IAAM+C,EAAc,8BACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCe,IAAgB,SAClBf,EAAgB,YAAiBe,EAAY,SAAS,GAGxD,IAAMtB,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EASA,YAAYA,EAA2E,CAKrF,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,UAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,aAAaA,EAA4E,CAKvF,IAAM0C,EAAmB,CACvB,OAAQ,MACR,KANkB,cAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,YACE,CAAE,KAAAiE,EAAM,YAAAC,CAAY,EAAsB,CAAC,EAC3ClE,EAA6C,OACf,CAC9B,IAAM+C,EAAc,aACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCgB,IAAS,SACXhB,EAAgB,KAAUgB,EAAK,SAAS,GAGtCC,IAAgB,SAClBjB,EAAgB,YAAiBiB,EAAY,SAAS,GAGxD,IAAMxB,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,YACE,CAAE,KAAAiE,EAAM,YAAAC,CAAY,EAAsB,CAAC,EAC3ClE,EAA6C,OACf,CAC9B,IAAM+C,EAAc,sBACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCgB,IAAS,SACXhB,EAAgB,KAAUgB,EAAK,SAAS,GAGtCC,IAAgB,SAClBjB,EAAgB,YAAiBiB,EAAY,SAAS,GAGxD,IAAMxB,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO3D,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAOA,cAAcmE,EAA0BnE,EAAiE,CACvG,GAAI,CAACmE,EACH,MAAM,IAAI,MAAM,mEAAmE,EAGrF,GAAI,CAACA,EAAY,SACf,MAAM,IAAI,MAAM,4EAA4E,EAO9F,IAAMzB,EAAmB,CACvB,OAAQ,OACR,KANkB,qBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMyB,CACR,EAEA,OAAO9E,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,eACE,CAAE,UAAAL,EAAW,qBAAAyE,CAAqB,EAClCpE,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,kEAAkE,EAGpF,GAAI,CAACyE,EACH,MAAM,IAAI,MAAM,6EAA6E,EAG/F,GAAI,CAACA,EAAqB,UACxB,MAAM,IAAI,MAAM,uFAAuF,EAEzG,GAAI,CAACA,EAAqB,YACxB,MAAM,IAAI,MAAM,yFAAyF,EAO3G,IAAM1B,EAAmB,CACvB,OAAQ,OACR,KANkB,mCAAmC,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOzG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMyE,CACR,EAEA,OAAO/E,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,oBACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,mBAAAqC,EAAoB,kBAAApC,CAAkB,EAC7DjC,EACwC,CACxC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,uEAAuE,EAGzF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,sEAAsE,EAGxF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,gFAAgF,EAGlG,IAAMtB,EAAc,4CACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtChB,IAAsB,SACxBgB,EAAgB,kBAAuBhB,EAAkB,SAAS,GAGpE,IAAMS,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMqB,CACR,EAEA,OAAOhF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,aAAa,CAAE,OAAA+D,CAAO,EAAsB/D,EAAgE,CAC1G,GAAI,CAAC+D,EACH,MAAM,IAAI,MAAM,6DAA6D,EAO/E,IAAMrB,EAAmB,CACvB,OAAQ,SACR,KANkB,+BAA+B,QAAQ,WAAY,mBAAmBqB,CAAM,CAAC,EAO/F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAO1E,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,eAAe,CAAE,OAAA4C,CAAO,EAAwB5C,EAAiE,CAC/G,GAAI,CAAC4C,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAMF,EAAmB,CACvB,OAAQ,MACR,KANkB,sBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAME,CACR,EAEA,OAAOvD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAWA,cAAc,CAAE,IAAAI,CAAI,EAAuBJ,EAA6D,CACtG,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAMsC,EAAmB,CACvB,OAAQ,OACR,KANkB,wBAAwB,QAAQ,QAAS,mBAAmBtC,CAAG,CAAC,EAOlF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOf,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,WACE,CAAE,UAAAL,EAAW,KAAAgD,CAAK,EAClB3C,EAC6B,CAC7B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,GAAI,CAACgD,EACH,MAAM,IAAI,MAAM,yDAAyD,EAO3E,IAAMD,EAAmB,CACvB,OAAQ,OACR,KANkB,yBAAyB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAO/F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgD,CACR,EAEA,OAAOtD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,SACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,KAAAsC,EAAM,kBAAAhB,CAAkB,EAC/CtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,4DAA4D,EAG9E,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,2DAA2D,EAG7E,GAAI,CAACsC,EACH,MAAM,IAAI,MAAM,uDAAuD,EAGzE,GAAI,CAACA,EAAK,SACR,MAAM,IAAI,MAAM,gEAAgE,EAElF,GAAI,CAACA,EAAK,YACR,MAAM,IAAI,MAAM,mEAAmE,EAGrF,IAAMvB,EAAc,0CACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMsB,CACR,EAEA,OAAOjF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,UACE,CAAE,UAAAL,EAAW,MAAA4E,EAAO,kBAAAjB,EAAmB,mBAAAkB,CAAmB,EAC1DxE,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,GAAI,CAAC4E,EACH,MAAM,IAAI,MAAM,yDAAyD,EAG3E,IAAMxB,EAAc,qCAAqC,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACvGqD,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGhEkB,IAAuB,SACzBvB,EAAgB,mBAAwBuB,EAAmB,SAAS,GAGtE,IAAM9B,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMuB,CACR,EAEA,OAAOlF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,YACE,CAAE,UAAAL,EAAW,SAAAqC,EAAU,WAAAyC,EAAY,kBAAAnB,CAAkB,EACrDtD,EAC8B,CAC9B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,+DAA+D,EAGjF,GAAI,CAACqC,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,GAAI,CAACyC,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,GAAI,CAACA,EAAW,SACd,MAAM,IAAI,MAAM,yEAAyE,EAE3F,GAAI,CAACA,EAAW,KACd,MAAM,IAAI,MAAM,qEAAqE,EAGvF,IAAM1B,EAAc,6CACjB,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpD,QAAQ,aAAc,mBAAmBqC,CAAQ,CAAC,EAC/CgB,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMyB,CACR,EAEA,OAAOpF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAcA,aACE,CAAE,UAAAL,EAAW,WAAA8E,EAAY,kBAAAnB,EAAmB,wBAAAoB,CAAwB,EACpE1E,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,GAAI,CAAC8E,EACH,MAAM,IAAI,MAAM,iEAAiE,EAGnF,IAAM1B,EAAc,wCAAwC,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EAC1GqD,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGhEoB,IAA4B,SAC9BzB,EAAgB,wBAA6ByB,EAAwB,SAAS,GAGhF,IAAMhC,EAAmB,CACvB,OAAQ,OACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMyB,CACR,EAEA,OAAOpF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,OACEyC,EACAzC,EAC6B,CAyB7B,GAxBIyC,GAAsB,MAAM,QAAQA,CAAkB,IAqBxDA,EApBgD,CAC9C,SAAUA,EAAmB,IAAI,CAAC,CAAE,OAAAxB,EAAQ,GAAG0D,CAAc,IACvDA,EAAc,OAAS,QAClB,CACL,GAAGA,EACH,GAAG1D,EACH,KAAM,OACR,EAGK,CACL,GAAG0D,EACH,GAAG1D,EACH,MAAO,OACP,aAAc,OACd,WAAY,MACd,CACD,CACH,GAKE,CAACwB,EACH,MAAM,IAAI,MAAM,mEAAmE,EAGrF,GAAI,CAACA,EAAmB,SACtB,MAAM,IAAI,MAAM,4EAA4E,EAO9F,IAAMC,EAAmB,CACvB,OAAQ,OACR,KANkB,uBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMD,EACN,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAOpD,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,wBACE,CAAE,eAAAoD,EAAgB,8BAAAwB,CAA8B,EAChD5E,EAC0C,CAC1C,GAAI,CAACoD,EACH,MAAM,IAAI,MAAM,gFAAgF,EAGlG,GAAI,CAACwB,EACH,MAAM,IAAI,MACR,+FACF,EAGF,GAAI,CAACA,EAA8B,MACjC,MAAM,IAAI,MACR,qGACF,EAUF,IAAMlC,EAAmB,CACvB,OAAQ,OACR,KATkB,0CAA0C,QAC5D,mBACA,mBAAmBU,CAAc,CACnC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMwB,EACN,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAOvF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,qBACE,CAAE,UAAAL,EAAW,UAAAkF,EAAW,4BAAAC,CAA4B,EACpD9E,EACuC,CACvC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,wEAAwE,EAG1F,GAAI,CAACkF,EACH,MAAM,IAAI,MAAM,wEAAwE,EAS1F,IAAMnC,EAAmB,CACvB,OAAQ,OACR,KARkB,kDACjB,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EACpD,QAAQ,cAAe,mBAAmBkF,CAAS,CAAC,EAOrD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMC,GAA4D,CAAC,EACnE,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAOzF,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,YACE,CAAE,UAAAL,EAAW,kBAAAoB,CAAkB,EAC/Bf,EAC8B,CAC9B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAM+C,EAAmB,CACvB,OAAQ,OACR,KANkB,sCAAsC,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAO5G,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMoB,GAAwC,CAAC,EAC/C,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAO1B,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,kBACE,CAAE,UAAAL,EAAW,aAAAoF,CAAa,EAC1B/E,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,qEAAqE,EAOvF,IAAM+C,EAAmB,CACvB,OAAQ,OACR,KANkB,+BAA+B,QAAQ,cAAe,mBAAmB/C,CAAS,CAAC,EAOrG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMoF,GAA8B,CAAC,EACrC,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAO1F,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,eACE,CAAE,UAAAL,EAAW,qBAAAuB,CAAqB,EAClClB,EACiC,CACjC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,kEAAkE,EAUpF,IAAM+C,EAAmB,CACvB,OAAQ,OACR,KATkB,yCAAyC,QAC3D,cACA,mBAAmB/C,CAAS,CAC9B,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMuB,GAA8C,CAAC,EACrD,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAO7B,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,cACEgF,EACAhF,EACgC,CAChC,GAAI,CAACgF,EACH,MAAM,IAAI,MAAM,2EAA2E,EAG7F,GAAI,CAACA,EAAoB,MACvB,MAAM,IAAI,MAAM,iFAAiF,EAOnG,IAAMtC,EAAmB,CACvB,OAAQ,OACR,KANkB,6BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMsC,EACN,mBAAoB,GACpB,UAAW,EACb,EAEA,OAAO3F,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAUA,sBACEiF,EACAjF,EAC4B,CAC5B,GAAI,CAACiF,EACH,MAAM,IAAI,MAAM,wFAAwF,EAG1G,GAAI,CAACA,EAAyB,uBAC5B,MAAM,IAAI,MACR,+GACF,EAOF,IAAMvC,EAAmB,CACvB,OAAQ,MACR,KANkB,6BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMuC,CACR,EAEA,OAAO5F,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAaA,YACE,CAAE,UAAAL,EAAW,cAAAuF,EAAe,kBAAA5B,CAAkB,EAC9CtD,EAC4B,CAC5B,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,+DAA+D,EAGjF,GAAI,CAACuF,EACH,MAAM,IAAI,MAAM,mEAAmE,EAGrF,IAAMnC,EAAc,kCAAkC,QAAQ,cAAe,mBAAmBpD,CAAS,CAAC,EACpGqD,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCK,IAAsB,SACxBL,EAAgB,kBAAuBK,EAAkB,SAAS,GAGpE,IAAMZ,EAAmB,CACvB,OAAQ,MACR,KAAMK,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMkC,CACR,EAEA,OAAO7F,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,EAYA,aAAa,CAAE,IAAAI,EAAK,OAAAV,CAAO,EAAsBM,EAAgE,CAC/G,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,0DAA0D,EAG5E,GAAI,CAACV,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,GAAI,CAACA,EAAO,IACV,MAAM,IAAI,MAAM,iEAAiE,EAOnF,IAAMgD,EAAmB,CACvB,OAAQ,MACR,KANkB,gBAAgB,QAAQ,QAAS,mBAAmBtC,CAAG,CAAC,EAO1E,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMV,CACR,EAEA,OAAOL,EAAY,QAAQqD,EAAS1C,CAAc,CACpD,CACF,CACF,CCl5FO,SAASmF,GAAaC,EAAeC,EAAgBC,EAAmD,CAC7G,GAAI,CAACF,GAAS,OAAOA,GAAU,SAC7B,MAAM,IAAI,MAAM,qBAAqB,EAGvC,GAAI,CAACC,GAAU,OAAOA,GAAW,SAC/B,MAAM,IAAI,MAAM,sBAAsB,EAGxC,OAAOE,EAAmB,CACxB,MAAAH,EACA,OAAAC,EACA,SAAU,CACR,QAAS,IACT,KAAM,IACN,MAAO,GACT,EACA,OAAQG,EAAiB,EACzB,UAAWC,EAAmB,EAC9B,cAAe,CAAC,CAAE,QAAS,SAAU,CAAC,EACtC,SAAU,wBACV,eAAgBC,EAAkB,EAClC,cAAeA,EAAkB,CAAE,aAAc,EAAM,CAAC,EACxD,WAAYC,EAAwB,CAClC,OAAQ,CAACC,EAA+B,CAAE,IAAK,GAAGC,CAAgB,IAAIT,CAAK,EAAG,CAAC,EAAGM,EAAkB,CAAC,CACvG,CAAC,EACD,GAAGJ,CACL,CAAC,CACH", "names": ["createXhrRequester", "send", "request", "resolve", "baseRequester", "key", "createTimeout", "timeout", "content", "connectTimeout", "responseTimeout", "createBrowserLocalStorageCache", "options", "storage", "namespaceKey", "getStorage", "getNamespace", "setNamespace", "namespace", "removeOutdatedCacheItems", "timeToLive", "filteredNamespaceWithoutOldFormattedCacheItems", "cacheItem", "filteredNamespaceWithoutExpiredItems", "currentTimestamp", "key", "defaultValue", "events", "value", "exists", "createNullCache", "_key", "result", "createFallbackableCache", "caches", "current", "createMemoryCache", "cache", "keyAsString", "promise", "createAlgoliaAgent", "version", "algoliaAgent", "options", "addedAlgoliaAgent", "createAuth", "appId", "<PERSON><PERSON><PERSON><PERSON>", "authMode", "credentials", "createIterablePromise", "func", "validate", "aggregator", "error", "timeout", "retry", "previousResponse", "resolve", "reject", "response", "err", "getAlgoliaAgent", "algoliaAgents", "client", "defaultAlgoliaAgent", "createNullLogger", "_message", "_args", "EXPIRATION_DELAY", "createStatefulHost", "host", "status", "lastUpdate", "isUp", "isTimedOut", "AlgoliaError", "message", "name", "ErrorWithStackTrace", "AlgoliaError", "message", "stackTrace", "name", "RetryError", "ApiError", "status", "DeserializationError", "response", "DetailedApiError", "error", "shuffle", "array", "<PERSON><PERSON><PERSON><PERSON>", "c", "b", "a", "serializeUrl", "host", "path", "queryParameters", "queryParametersAsString", "serializeQueryParameters", "url", "parameters", "key", "serializeData", "request", "requestOptions", "data", "serializeHeaders", "baseHeaders", "requestHeaders", "requestOptionsHeaders", "headers", "serializedHeaders", "header", "value", "deserializeSuccess", "e", "deserializeFailure", "content", "stackFrame", "parsed", "isNetworkError", "isTimedOut", "isRetryable", "isSuccess", "stackTraceWithoutCredentials", "stackFrameWithoutCredentials", "modifiedHeaders", "createTransporter", "hosts", "hostsCache", "logger", "baseQueryParameters", "algoliaAgent", "timeouts", "requester", "requestsCache", "responsesCache", "createRetryableOptions", "compatibleHosts", "statefulHosts", "compatibleHost", "createStatefulHost", "hostsUp", "hostsTimedOut", "hostsAvailable", "timeoutsCount", "baseTimeout", "retryableRequest", "isRead", "dataQueryParameters", "retry", "retryableHosts", "getTimeout", "timeout", "payload", "pushToStackTrace", "options", "createRequest", "createRetryableRequest", "err", "_", "apiClientVersion", "getDefaultHosts", "appId", "shuffle", "createSearchClient", "appIdOption", "apiKeyOption", "authMode", "algoliaAgents", "options", "auth", "createAuth", "transporter", "createTransporter", "getAlgoliaAgent", "segment", "version", "<PERSON><PERSON><PERSON><PERSON>", "indexName", "taskID", "maxRetries", "timeout", "retryCount", "requestOptions", "createIterablePromise", "response", "operation", "key", "baseIteratorOptions", "field", "value", "resValue", "v", "index", "error", "browseParams", "browseObjectsOptions", "previousResponse", "searchRulesParams", "browseRulesOptions", "params", "searchSynonymsParams", "browseSynonymsOptions", "_", "resp", "objects", "action", "waitForTasks", "batchSize", "requests", "responses", "objectEntries", "i", "obj", "objectIDs", "objectID", "createIfNotExists", "scopes", "randomSuffix", "tmpIndexName", "copyOperationResponse", "batchResponses", "moveOperationResponse", "ApiError", "searchMethodParams", "request", "body", "source", "xAlgoliaUserID", "assignUserIdParams", "requestPath", "headers", "queryParameters", "batchWriteParams", "batchAssignUserIdsParams", "dictionaryName", "batchDictionaryEntriesParams", "forwardToReplicas", "path", "parameters", "deleteByParams", "offset", "length", "type", "attributesToRetrieve", "getObjectsParams", "userID", "getClusters", "page", "hitsPerPage", "batchParams", "operationIndexParams", "attributesToUpdate", "rule", "rules", "clearExistingRules", "synonymHit", "replaceExistingSynonyms", "legacyRequest", "searchDictionaryEntriesParams", "facetName", "searchForFacetValuesRequest", "searchParams", "searchUserIdsParams", "dictionarySettingsParams", "indexSettings", "searchClient", "appId", "<PERSON><PERSON><PERSON><PERSON>", "options", "createSearchClient", "createNullLogger", "m", "createMemoryCache", "createFallbackableCache", "createBrowserLocalStorageCache", "apiClientVersion"]}