(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@algolia/monitoring"] = {}));
})(this, (function (exports) { 'use strict';

	function H(){function r(e){return new Promise(s=>{let t=new XMLHttpRequest;t.open(e.method,e.url,true),Object.keys(e.headers).forEach(o=>t.setRequestHeader(o,e.headers[o]));let a=(o,n)=>setTimeout(()=>{t.abort(),s({status:0,content:n,isTimedOut:true});},o),d=a(e.connectTimeout,"Connection timeout"),i;t.onreadystatechange=()=>{t.readyState>t.OPENED&&i===void 0&&(clearTimeout(d),i=a(e.responseTimeout,"Socket timeout"));},t.onerror=()=>{t.status===0&&(clearTimeout(d),clearTimeout(i),s({content:t.responseText||"Network request failed",status:t.status,isTimedOut:false}));},t.onload=()=>{clearTimeout(d),clearTimeout(i),s({content:t.responseText,status:t.status,isTimedOut:false});},t.send(e.data);})}return {send:r}}function M(r){let e,s=`algolia-client-js-${r.key}`;function t(){return e===void 0&&(e=r.localStorage||window.localStorage),e}function a(){return JSON.parse(t().getItem(s)||"{}")}function d(o){t().setItem(s,JSON.stringify(o));}function i(){let o=r.timeToLive?r.timeToLive*1e3:null,n=a(),c=Object.fromEntries(Object.entries(n).filter(([,m])=>m.timestamp!==void 0));if(d(c),!o)return;let u=Object.fromEntries(Object.entries(c).filter(([,m])=>{let h=new Date().getTime();return !(m.timestamp+o<h)}));d(u);}return {get(o,n,c={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(i(),a()[JSON.stringify(o)])).then(u=>Promise.all([u?u.value:n(),u!==void 0])).then(([u,m])=>Promise.all([u,m||c.miss(u)])).then(([u])=>u)},set(o,n){return Promise.resolve().then(()=>{let c=a();return c[JSON.stringify(o)]={timestamp:new Date().getTime(),value:n},t().setItem(s,JSON.stringify(c)),n})},delete(o){return Promise.resolve().then(()=>{let n=a();delete n[JSON.stringify(o)],t().setItem(s,JSON.stringify(n));})},clear(){return Promise.resolve().then(()=>{t().removeItem(s);})}}}function K(){return {get(r,e,s={miss:()=>Promise.resolve()}){return e().then(a=>Promise.all([a,s.miss(a)])).then(([a])=>a)},set(r,e){return Promise.resolve(e)},delete(r){return Promise.resolve()},clear(){return Promise.resolve()}}}function T(r){let e=[...r.caches],s=e.shift();return s===void 0?K():{get(t,a,d={miss:()=>Promise.resolve()}){return s.get(t,a,d).catch(()=>T({caches:e}).get(t,a,d))},set(t,a){return s.set(t,a).catch(()=>T({caches:e}).set(t,a))},delete(t){return s.delete(t).catch(()=>T({caches:e}).delete(t))},clear(){return s.clear().catch(()=>T({caches:e}).clear())}}}function I(r={serializable:true}){let e={};return {get(s,t,a={miss:()=>Promise.resolve()}){let d=JSON.stringify(s);if(d in e)return Promise.resolve(r.serializable?JSON.parse(e[d]):e[d]);let i=t();return i.then(o=>a.miss(o)).then(()=>i)},set(s,t){return e[JSON.stringify(s)]=r.serializable?JSON.stringify(t):t,Promise.resolve(t)},delete(s){return delete e[JSON.stringify(s)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function V(r){let e={value:`Algolia for JavaScript (${r})`,add(s){let t=`; ${s.segment}${s.version!==void 0?` (${s.version})`:""}`;return e.value.indexOf(t)===-1&&(e.value=`${e.value}${t}`),e}};return e}function Q(r,e,s="WithinHeaders"){let t={"x-algolia-api-key":e,"x-algolia-application-id":r};return {headers(){return s==="WithinHeaders"?t:{}},queryParameters(){return s==="WithinQueryParameters"?t:{}}}}function $({algoliaAgents:r,client:e,version:s}){let t=V(s).add({segment:e,version:s});return r.forEach(a=>t.add(a)),t}function W(){return {debug(r,e){return Promise.resolve()},info(r,e){return Promise.resolve()},error(r,e){return Promise.resolve()}}}var k=2*60*1e3;function L(r,e="up"){let s=Date.now();function t(){return e==="up"||Date.now()-s>k}function a(){return e==="timed out"&&Date.now()-s<=k}return {...r,status:e,lastUpdate:s,isUp:t,isTimedOut:a}}var j=class extends Error{name="AlgoliaError";constructor(r,e){super(r),e&&(this.name=e);}};var J=class extends j{stackTrace;constructor(r,e,s){super(r,s),this.stackTrace=e;}},Y=class extends J{constructor(r){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",r,"RetryError");}},b=class extends J{status;constructor(r,e,s,t="ApiError"){super(r,s,t),this.status=e;}},Z=class extends j{response;constructor(r,e){super(r,"DeserializationError"),this.response=e;}},ee=class extends b{error;constructor(r,e,s,t){super(r,e,t,"DetailedApiError"),this.error=s;}};function re(r,e,s){let t=te(s),a=`${r.protocol}://${r.url}${r.port?`:${r.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return t.length&&(a+=`?${t}`),a}function te(r){return Object.keys(r).filter(e=>r[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(r[e])==="[object Array]"?r[e].join(","):r[e]).replace(/\+/g,"%20")}`).join("&")}function se(r,e){if(r.method==="GET"||r.data===void 0&&e.data===void 0)return;let s=Array.isArray(r.data)?r.data:{...r.data,...e.data};return JSON.stringify(s)}function oe(r,e,s){let t={Accept:"application/json",...r,...e,...s},a={};return Object.keys(t).forEach(d=>{let i=t[d];a[d.toLowerCase()]=i;}),a}function ne(r){try{return JSON.parse(r.content)}catch(e){throw new Z(e.message,r)}}function ae({content:r,status:e},s){try{let t=JSON.parse(r);return "error"in t?new ee(t.message,e,t.error,s):new b(t.message,e,s)}catch{}return new b(r,e,s)}function ie({isTimedOut:r,status:e}){return !r&&~~e===0}function ce({isTimedOut:r,status:e}){return r||ie({isTimedOut:r,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function ue({status:r}){return ~~(r/100)===2}function le(r){return r.map(e=>z(e))}function z(r){let e=r.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...r,request:{...r.request,headers:{...r.request.headers,...e}}}}function F({hosts:r,hostsCache:e,baseHeaders:s,logger:t,baseQueryParameters:a,algoliaAgent:d,timeouts:i,requester:o,requestsCache:n,responsesCache:c}){async function u(l){let p=await Promise.all(l.map(f=>e.get(f,()=>Promise.resolve(L(f))))),E=p.filter(f=>f.isUp()),g=p.filter(f=>f.isTimedOut()),w=[...E,...g];return {hosts:w.length>0?w:l,getTimeout(f,q){return (g.length===0&&f===0?1:g.length+3+f)*q}}}async function m(l,p,E=true){let g=[],w=se(l,p),y=oe(s,l.headers,p.headers),f=l.method==="GET"?{...l.data,...p.data}:{},q={...a,...l.queryParameters,...f};if(d.value&&(q["x-algolia-agent"]=d.value),p&&p.queryParameters)for(let P of Object.keys(p.queryParameters))!p.queryParameters[P]||Object.prototype.toString.call(p.queryParameters[P])==="[object Object]"?q[P]=p.queryParameters[P]:q[P]=p.queryParameters[P].toString();let v=0,N=async(P,C)=>{let x=P.pop();if(x===void 0)throw new Y(le(g));let A={...i,...p.timeouts},_={data:w,headers:y,method:l.method,url:re(x,l.path,q),connectTimeout:C(v,A.connect),responseTimeout:C(v,E?A.read:A.write)},G=S=>{let U={request:_,response:S,host:x,triesLeft:P.length};return g.push(U),U},R=await o.send(_);if(ce(R)){let S=G(R);return R.isTimedOut&&v++,t.info("Retryable failure",z(S)),await e.set(x,L(x,R.isTimedOut?"timed out":"down")),N(P,C)}if(ue(R))return ne(R);throw G(R),ae(R,g)},X=r.filter(P=>P.accept==="readWrite"||(E?P.accept==="read":P.accept==="write")),D=await u(X);return N([...D.hosts].reverse(),D.getTimeout)}function h(l,p={}){let E=l.useReadTransporter||l.method==="GET";if(!E)return m(l,p,E);let g=()=>m(l,p);if((p.cacheable||l.cacheable)!==true)return g();let y={request:l,requestOptions:p,transporter:{queryParameters:a,headers:s}};return c.get(y,()=>n.get(y,()=>n.set(y,g()).then(f=>Promise.all([n.delete(y),f]),f=>Promise.all([n.delete(y),Promise.reject(f)])).then(([f,q])=>q)),{miss:f=>c.set(y,f)})}return {hostsCache:e,requester:o,timeouts:i,logger:t,algoliaAgent:d,baseHeaders:s,baseQueryParameters:a,hosts:r,request:h,requestsCache:n,responsesCache:c}}var O="1.30.0";function me(){return [{url:"status.algolia.com",accept:"readWrite",protocol:"https"}]}function B({appId:r,apiKey:e,authMode:s,algoliaAgents:t,...a}){let d=Q(r,e,s),i=F({hosts:me(),...a,algoliaAgent:$({algoliaAgents:t,client:"Monitoring",version:O}),baseHeaders:{"content-type":"text/plain",...d.headers(),...a.baseHeaders},baseQueryParameters:{...d.queryParameters(),...a.baseQueryParameters}});return {transporter:i,appId:r,apiKey:e,clearCache(){return Promise.all([i.requestsCache.clear(),i.responsesCache.clear()]).then(()=>{})},get _ua(){return i.algoliaAgent.value},addAlgoliaAgent(o,n){i.algoliaAgent.add({segment:o,version:n});},setClientApiKey({apiKey:o}){!s||s==="WithinHeaders"?i.baseHeaders["x-algolia-api-key"]=o:i.baseQueryParameters["x-algolia-api-key"]=o;},customDelete({path:o,parameters:n},c){if(!o)throw new Error("Parameter `path` is required when calling `customDelete`.");let l={method:"DELETE",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{}};return i.request(l,c)},customGet({path:o,parameters:n},c){if(!o)throw new Error("Parameter `path` is required when calling `customGet`.");let l={method:"GET",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{}};return i.request(l,c)},customPost({path:o,parameters:n,body:c},u){if(!o)throw new Error("Parameter `path` is required when calling `customPost`.");let p={method:"POST",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{},data:c||{}};return i.request(p,u)},customPut({path:o,parameters:n,body:c},u){if(!o)throw new Error("Parameter `path` is required when calling `customPut`.");let p={method:"PUT",path:"/{path}".replace("{path}",o),queryParameters:n||{},headers:{},data:c||{}};return i.request(p,u)},getClusterIncidents({clusters:o},n){if(!o)throw new Error("Parameter `clusters` is required when calling `getClusterIncidents`.");let h={method:"GET",path:"/1/incidents/{clusters}".replace("{clusters}",encodeURIComponent(o)),queryParameters:{},headers:{}};return i.request(h,n)},getClusterStatus({clusters:o},n){if(!o)throw new Error("Parameter `clusters` is required when calling `getClusterStatus`.");let h={method:"GET",path:"/1/status/{clusters}".replace("{clusters}",encodeURIComponent(o)),queryParameters:{},headers:{}};return i.request(h,n)},getIncidents(o){let m={method:"GET",path:"/1/incidents",queryParameters:{},headers:{}};return i.request(m,o)},getIndexingTime({clusters:o},n){if(!o)throw new Error("Parameter `clusters` is required when calling `getIndexingTime`.");let h={method:"GET",path:"/1/indexing/{clusters}".replace("{clusters}",encodeURIComponent(o)),queryParameters:{},headers:{}};return i.request(h,n)},getLatency({clusters:o},n){if(!o)throw new Error("Parameter `clusters` is required when calling `getLatency`.");let h={method:"GET",path:"/1/latency/{clusters}".replace("{clusters}",encodeURIComponent(o)),queryParameters:{},headers:{}};return i.request(h,n)},getMetrics({metric:o,period:n},c){if(!o)throw new Error("Parameter `metric` is required when calling `getMetrics`.");if(!n)throw new Error("Parameter `period` is required when calling `getMetrics`.");let l={method:"GET",path:"/1/infrastructure/{metric}/period/{period}".replace("{metric}",encodeURIComponent(o)).replace("{period}",encodeURIComponent(n)),queryParameters:{},headers:{}};return i.request(l,c)},getReachability({clusters:o},n){if(!o)throw new Error("Parameter `clusters` is required when calling `getReachability`.");let h={method:"GET",path:"/1/reachability/{clusters}/probes".replace("{clusters}",encodeURIComponent(o)),queryParameters:{},headers:{}};return i.request(h,n)},getServers(o){let m={method:"GET",path:"/1/inventory/servers",queryParameters:{},headers:{}};return i.request(m,o)},getStatus(o){let m={method:"GET",path:"/1/status",queryParameters:{},headers:{}};return i.request(m,o)}}}function Ke(r,e,s){if(!r||typeof r!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");return B({appId:r,apiKey:e,timeouts:{connect:1e3,read:2e3,write:3e4},logger:W(),requester:H(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:I(),requestsCache:I({serializable:false}),hostsCache:T({caches:[M({key:`${O}-${r}`}),I()]}),...s})}

	exports.apiClientVersion = O;
	exports.monitoringClient = Ke;

}));
