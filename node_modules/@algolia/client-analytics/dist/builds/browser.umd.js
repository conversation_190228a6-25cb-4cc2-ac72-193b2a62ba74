(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@algolia/client-analytics"] = {}));
})(this, (function (exports) { 'use strict';

	function Q(){function o(t){return new Promise(d=>{let u=new XMLHttpRequest;u.open(t.method,t.url,true),Object.keys(t.headers).forEach(f=>u.setRequestHeader(f,t.headers[f]));let h=(f,e)=>setTimeout(()=>{u.abort(),d({status:0,content:e,isTimedOut:true});},f),R=h(t.connectTimeout,"Connection timeout"),y;u.onreadystatechange=()=>{u.readyState>u.OPENED&&y===void 0&&(clearTimeout(R),y=h(t.responseTimeout,"Socket timeout"));},u.onerror=()=>{u.status===0&&(clearTimeout(R),clearTimeout(y),d({content:u.responseText||"Network request failed",status:u.status,isTimedOut:false}));},u.onload=()=>{clearTimeout(R),clearTimeout(y),d({content:u.responseText,status:u.status,isTimedOut:false});},u.send(t.data);})}return {send:o}}function L(o){let t,d=`algolia-client-js-${o.key}`;function u(){return t===void 0&&(t=o.localStorage||window.localStorage),t}function h(){return JSON.parse(u().getItem(d)||"{}")}function R(f){u().setItem(d,JSON.stringify(f));}function y(){let f=o.timeToLive?o.timeToLive*1e3:null,e=h(),s=Object.fromEntries(Object.entries(e).filter(([,i])=>i.timestamp!==void 0));if(R(s),!f)return;let n=Object.fromEntries(Object.entries(s).filter(([,i])=>{let l=new Date().getTime();return !(i.timestamp+f<l)}));R(n);}return {get(f,e,s={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(y(),h()[JSON.stringify(f)])).then(n=>Promise.all([n?n.value:e(),n!==void 0])).then(([n,i])=>Promise.all([n,i||s.miss(n)])).then(([n])=>n)},set(f,e){return Promise.resolve().then(()=>{let s=h();return s[JSON.stringify(f)]={timestamp:new Date().getTime(),value:e},u().setItem(d,JSON.stringify(s)),e})},delete(f){return Promise.resolve().then(()=>{let e=h();delete e[JSON.stringify(f)],u().setItem(d,JSON.stringify(e));})},clear(){return Promise.resolve().then(()=>{u().removeItem(d);})}}}function V(){return {get(o,t,d={miss:()=>Promise.resolve()}){return t().then(h=>Promise.all([h,d.miss(h)])).then(([h])=>h)},set(o,t){return Promise.resolve(t)},delete(o){return Promise.resolve()},clear(){return Promise.resolve()}}}function E(o){let t=[...o.caches],d=t.shift();return d===void 0?V():{get(u,h,R={miss:()=>Promise.resolve()}){return d.get(u,h,R).catch(()=>E({caches:t}).get(u,h,R))},set(u,h){return d.set(u,h).catch(()=>E({caches:t}).set(u,h))},delete(u){return d.delete(u).catch(()=>E({caches:t}).delete(u))},clear(){return d.clear().catch(()=>E({caches:t}).clear())}}}function x(o={serializable:true}){let t={};return {get(d,u,h={miss:()=>Promise.resolve()}){let R=JSON.stringify(d);if(R in t)return Promise.resolve(o.serializable?JSON.parse(t[R]):t[R]);let y=u();return y.then(f=>h.miss(f)).then(()=>y)},set(d,u){return t[JSON.stringify(d)]=o.serializable?JSON.stringify(u):u,Promise.resolve(u)},delete(d){return delete t[JSON.stringify(d)],Promise.resolve()},clear(){return t={},Promise.resolve()}}}function Y(o){let t={value:`Algolia for JavaScript (${o})`,add(d){let u=`; ${d.segment}${d.version!==void 0?` (${d.version})`:""}`;return t.value.indexOf(u)===-1&&(t.value=`${t.value}${u}`),t}};return t}function $(o,t,d="WithinHeaders"){let u={"x-algolia-api-key":t,"x-algolia-application-id":o};return {headers(){return d==="WithinHeaders"?u:{}},queryParameters(){return d==="WithinQueryParameters"?u:{}}}}function j({algoliaAgents:o,client:t,version:d}){let u=Y(d).add({segment:t,version:d});return o.forEach(h=>u.add(h)),u}function W(){return {debug(o,t){return Promise.resolve()},info(o,t){return Promise.resolve()},error(o,t){return Promise.resolve()}}}var _=2*60*1e3;function U(o,t="up"){let d=Date.now();function u(){return t==="up"||Date.now()-d>_}function h(){return t==="timed out"&&Date.now()-d<=_}return {...o,status:t,lastUpdate:d,isUp:u,isTimedOut:h}}var J=class extends Error{name="AlgoliaError";constructor(o,t){super(o),t&&(this.name=t);}};var z=class extends J{stackTrace;constructor(o,t,d){super(o,d),this.stackTrace=t;}},Z=class extends z{constructor(o){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",o,"RetryError");}},k=class extends z{status;constructor(o,t,d,u="ApiError"){super(o,d,u),this.status=t;}},ee=class extends J{response;constructor(o,t){super(o,"DeserializationError"),this.response=t;}},te=class extends k{error;constructor(o,t,d,u){super(o,t,u,"DetailedApiError"),this.error=d;}};function re(o,t,d){let u=oe(d),h=`${o.protocol}://${o.url}${o.port?`:${o.port}`:""}/${t.charAt(0)==="/"?t.substring(1):t}`;return u.length&&(h+=`?${u}`),h}function oe(o){return Object.keys(o).filter(t=>o[t]!==void 0).sort().map(t=>`${t}=${encodeURIComponent(Object.prototype.toString.call(o[t])==="[object Array]"?o[t].join(","):o[t]).replace(/\+/g,"%20")}`).join("&")}function se(o,t){if(o.method==="GET"||o.data===void 0&&t.data===void 0)return;let d=Array.isArray(o.data)?o.data:{...o.data,...t.data};return JSON.stringify(d)}function ne(o,t,d){let u={Accept:"application/json",...o,...t,...d},h={};return Object.keys(u).forEach(R=>{let y=u[R];h[R.toLowerCase()]=y;}),h}function ie(o){try{return JSON.parse(o.content)}catch(t){throw new ee(t.message,o)}}function ae({content:o,status:t},d){try{let u=JSON.parse(o);return "error"in u?new te(u.message,t,u.error,d):new k(u.message,t,d)}catch{}return new k(o,t,d)}function ue({isTimedOut:o,status:t}){return !o&&~~t===0}function ce({isTimedOut:o,status:t}){return o||ue({isTimedOut:o,status:t})||~~(t/100)!==2&&~~(t/100)!==4}function de({status:o}){return ~~(o/100)===2}function le(o){return o.map(t=>M(t))}function M(o){let t=o.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...o,request:{...o.request,headers:{...o.request.headers,...t}}}}function B({hosts:o,hostsCache:t,baseHeaders:d,logger:u,baseQueryParameters:h,algoliaAgent:R,timeouts:y,requester:f,requestsCache:e,responsesCache:s}){async function n(a){let c=await Promise.all(a.map(P=>t.get(P,()=>Promise.resolve(U(P))))),r=c.filter(P=>P.isUp()),m=c.filter(P=>P.isTimedOut()),p=[...r,...m];return {hosts:p.length>0?p:a,getTimeout(P,S){return (m.length===0&&P===0?1:m.length+3+P)*S}}}async function i(a,c,r=true){let m=[],p=se(a,c),g=ne(d,a.headers,c.headers),P=a.method==="GET"?{...a.data,...c.data}:{},S={...h,...a.queryParameters,...P};if(R.value&&(S["x-algolia-agent"]=R.value),c&&c.queryParameters)for(let T of Object.keys(c.queryParameters))!c.queryParameters[T]||Object.prototype.toString.call(c.queryParameters[T])==="[object Object]"?S[T]=c.queryParameters[T]:S[T]=c.queryParameters[T].toString();let q=0,G=async(T,A)=>{let C=T.pop();if(C===void 0)throw new Z(le(m));let N={...y,...c.timeouts},F={data:p,headers:g,method:a.method,url:re(C,a.path,S),connectTimeout:A(q,N.connect),responseTimeout:A(q,r?N.read:N.write)},I=O=>{let D={request:F,response:O,host:C,triesLeft:T.length};return m.push(D),D},w=await f.send(F);if(ce(w)){let O=I(w);return w.isTimedOut&&q++,u.info("Retryable failure",M(O)),await t.set(C,U(C,w.isTimedOut?"timed out":"down")),G(T,A)}if(de(w))return ie(w);throw I(w),ae(w,m)},K=o.filter(T=>T.accept==="readWrite"||(r?T.accept==="read":T.accept==="write")),H=await n(K);return G([...H.hosts].reverse(),H.getTimeout)}function l(a,c={}){let r=a.useReadTransporter||a.method==="GET";if(!r)return i(a,c,r);let m=()=>i(a,c);if((c.cacheable||a.cacheable)!==true)return m();let g={request:a,requestOptions:c,transporter:{queryParameters:h,headers:d}};return s.get(g,()=>e.get(g,()=>e.set(g,m()).then(P=>Promise.all([e.delete(g),P]),P=>Promise.all([e.delete(g),Promise.reject(P)])).then(([P,S])=>S)),{miss:P=>s.set(g,P)})}return {hostsCache:t,requester:f,timeouts:y,logger:u,algoliaAgent:R,baseHeaders:d,baseQueryParameters:h,hosts:o,request:l,requestsCache:e,responsesCache:s}}var v="5.30.0",b=["de","us"];function fe(o){return [{url:o?"analytics.{region}.algolia.com".replace("{region}",o):"analytics.algolia.com",accept:"readWrite",protocol:"https"}]}function X({appId:o,apiKey:t,authMode:d,algoliaAgents:u,region:h,...R}){let y=$(o,t,d),f=B({hosts:fe(h),...R,algoliaAgent:j({algoliaAgents:u,client:"Analytics",version:v}),baseHeaders:{"content-type":"text/plain",...y.headers(),...R.baseHeaders},baseQueryParameters:{...y.queryParameters(),...R.baseQueryParameters}});return {transporter:f,appId:o,apiKey:t,clearCache(){return Promise.all([f.requestsCache.clear(),f.responsesCache.clear()]).then(()=>{})},get _ua(){return f.algoliaAgent.value},addAlgoliaAgent(e,s){f.algoliaAgent.add({segment:e,version:s});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?f.baseHeaders["x-algolia-api-key"]=e:f.baseQueryParameters["x-algolia-api-key"]=e;},customDelete({path:e,parameters:s},n){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let c={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:s||{},headers:{}};return f.request(c,n)},customGet({path:e,parameters:s},n){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let c={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:s||{},headers:{}};return f.request(c,n)},customPost({path:e,parameters:s,body:n},i){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let r={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:s||{},headers:{},data:n||{}};return f.request(r,i)},customPut({path:e,parameters:s,body:n},i){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let r={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:s||{},headers:{},data:n||{}};return f.request(r,i)},getAddToCartRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getAddToCartRate`.");let a="/2/conversions/addToCartRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getAverageClickPosition({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getAverageClickPosition`.");let a="/2/clicks/averageClickPosition",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getClickPositions({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getClickPositions`.");let a="/2/clicks/positions",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getClickThroughRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getClickThroughRate`.");let a="/2/clicks/clickThroughRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getConversionRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getConversionRate`.");let a="/2/conversions/conversionRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getNoClickRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getNoClickRate`.");let a="/2/searches/noClickRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getNoResultsRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getNoResultsRate`.");let a="/2/searches/noResultRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getPurchaseRate({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getPurchaseRate`.");let a="/2/conversions/purchaseRate",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getRevenue({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getRevenue`.");let a="/2/conversions/revenue",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getSearchesCount({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesCount`.");let a="/2/searches/count",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)},getSearchesNoClicks({index:e,startDate:s,endDate:n,limit:i,offset:l,tags:a},c){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesNoClicks`.");let r="/2/searches/noClicks",m={},p={};e!==void 0&&(p.index=e.toString()),s!==void 0&&(p.startDate=s.toString()),n!==void 0&&(p.endDate=n.toString()),i!==void 0&&(p.limit=i.toString()),l!==void 0&&(p.offset=l.toString()),a!==void 0&&(p.tags=a.toString());let g={method:"GET",path:r,queryParameters:p,headers:m};return f.request(g,c)},getSearchesNoResults({index:e,startDate:s,endDate:n,limit:i,offset:l,tags:a},c){if(!e)throw new Error("Parameter `index` is required when calling `getSearchesNoResults`.");let r="/2/searches/noResults",m={},p={};e!==void 0&&(p.index=e.toString()),s!==void 0&&(p.startDate=s.toString()),n!==void 0&&(p.endDate=n.toString()),i!==void 0&&(p.limit=i.toString()),l!==void 0&&(p.offset=l.toString()),a!==void 0&&(p.tags=a.toString());let g={method:"GET",path:r,queryParameters:p,headers:m};return f.request(g,c)},getStatus({index:e},s){if(!e)throw new Error("Parameter `index` is required when calling `getStatus`.");let n="/2/status",i={},l={};e!==void 0&&(l.index=e.toString());let a={method:"GET",path:n,queryParameters:l,headers:i};return f.request(a,s)},getTopCountries({index:e,startDate:s,endDate:n,limit:i,offset:l,tags:a},c){if(!e)throw new Error("Parameter `index` is required when calling `getTopCountries`.");let r="/2/countries",m={},p={};e!==void 0&&(p.index=e.toString()),s!==void 0&&(p.startDate=s.toString()),n!==void 0&&(p.endDate=n.toString()),i!==void 0&&(p.limit=i.toString()),l!==void 0&&(p.offset=l.toString()),a!==void 0&&(p.tags=a.toString());let g={method:"GET",path:r,queryParameters:p,headers:m};return f.request(g,c)},getTopFilterAttributes({index:e,search:s,startDate:n,endDate:i,limit:l,offset:a,tags:c},r){if(!e)throw new Error("Parameter `index` is required when calling `getTopFilterAttributes`.");let m="/2/filters",p={},g={};e!==void 0&&(g.index=e.toString()),s!==void 0&&(g.search=s.toString()),n!==void 0&&(g.startDate=n.toString()),i!==void 0&&(g.endDate=i.toString()),l!==void 0&&(g.limit=l.toString()),a!==void 0&&(g.offset=a.toString()),c!==void 0&&(g.tags=c.toString());let P={method:"GET",path:m,queryParameters:g,headers:p};return f.request(P,r)},getTopFilterForAttribute({attribute:e,index:s,search:n,startDate:i,endDate:l,limit:a,offset:c,tags:r},m){if(!e)throw new Error("Parameter `attribute` is required when calling `getTopFilterForAttribute`.");if(!s)throw new Error("Parameter `index` is required when calling `getTopFilterForAttribute`.");let p="/2/filters/{attribute}".replace("{attribute}",encodeURIComponent(e)),g={},P={};s!==void 0&&(P.index=s.toString()),n!==void 0&&(P.search=n.toString()),i!==void 0&&(P.startDate=i.toString()),l!==void 0&&(P.endDate=l.toString()),a!==void 0&&(P.limit=a.toString()),c!==void 0&&(P.offset=c.toString()),r!==void 0&&(P.tags=r.toString());let S={method:"GET",path:p,queryParameters:P,headers:g};return f.request(S,m)},getTopFiltersNoResults({index:e,search:s,startDate:n,endDate:i,limit:l,offset:a,tags:c},r){if(!e)throw new Error("Parameter `index` is required when calling `getTopFiltersNoResults`.");let m="/2/filters/noResults",p={},g={};e!==void 0&&(g.index=e.toString()),s!==void 0&&(g.search=s.toString()),n!==void 0&&(g.startDate=n.toString()),i!==void 0&&(g.endDate=i.toString()),l!==void 0&&(g.limit=l.toString()),a!==void 0&&(g.offset=a.toString()),c!==void 0&&(g.tags=c.toString());let P={method:"GET",path:m,queryParameters:g,headers:p};return f.request(P,r)},getTopHits({index:e,search:s,clickAnalytics:n,revenueAnalytics:i,startDate:l,endDate:a,limit:c,offset:r,tags:m},p){if(!e)throw new Error("Parameter `index` is required when calling `getTopHits`.");let g="/2/hits",P={},S={};e!==void 0&&(S.index=e.toString()),s!==void 0&&(S.search=s.toString()),n!==void 0&&(S.clickAnalytics=n.toString()),i!==void 0&&(S.revenueAnalytics=i.toString()),l!==void 0&&(S.startDate=l.toString()),a!==void 0&&(S.endDate=a.toString()),c!==void 0&&(S.limit=c.toString()),r!==void 0&&(S.offset=r.toString()),m!==void 0&&(S.tags=m.toString());let q={method:"GET",path:g,queryParameters:S,headers:P};return f.request(q,p)},getTopSearches({index:e,clickAnalytics:s,revenueAnalytics:n,startDate:i,endDate:l,orderBy:a,direction:c,limit:r,offset:m,tags:p},g){if(!e)throw new Error("Parameter `index` is required when calling `getTopSearches`.");let P="/2/searches",S={},q={};e!==void 0&&(q.index=e.toString()),s!==void 0&&(q.clickAnalytics=s.toString()),n!==void 0&&(q.revenueAnalytics=n.toString()),i!==void 0&&(q.startDate=i.toString()),l!==void 0&&(q.endDate=l.toString()),a!==void 0&&(q.orderBy=a.toString()),c!==void 0&&(q.direction=c.toString()),r!==void 0&&(q.limit=r.toString()),m!==void 0&&(q.offset=m.toString()),p!==void 0&&(q.tags=p.toString());let G={method:"GET",path:P,queryParameters:q,headers:S};return f.request(G,g)},getUsersCount({index:e,startDate:s,endDate:n,tags:i},l){if(!e)throw new Error("Parameter `index` is required when calling `getUsersCount`.");let a="/2/users/count",c={},r={};e!==void 0&&(r.index=e.toString()),s!==void 0&&(r.startDate=s.toString()),n!==void 0&&(r.endDate=n.toString()),i!==void 0&&(r.tags=i.toString());let m={method:"GET",path:a,queryParameters:r,headers:c};return f.request(m,l)}}}function At(o,t,d,u){if(!o||typeof o!="string")throw new Error("`appId` is missing.");if(!t||typeof t!="string")throw new Error("`apiKey` is missing.");if(d&&(typeof d!="string"||!b.includes(d)))throw new Error(`\`region\` must be one of the following: ${b.join(", ")}`);return X({appId:o,apiKey:t,region:d,timeouts:{connect:1e3,read:2e3,write:3e4},logger:W(),requester:Q(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:x(),requestsCache:x({serializable:false}),hostsCache:E({caches:[L({key:`${v}-${o}`}),x()]}),...u})}

	exports.analyticsClient = At;
	exports.apiClientVersion = v;

}));
