import * as _algolia_client_common from '@algolia/client-common';
import { CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

type DailyAddToCartRates = {
    /**
     * Add-to-cart rate: calculated as the number of tracked searches with at least one add-to-cart event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of add-to-cart events from this search.
     */
    addToCartCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetAddToCartRateResponse = {
    /**
     * Add-to-cart rate: calculated as the number of tracked searches with at least one add-to-cart event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of add-to-cart events from this search.
     */
    addToCartCount: number;
    /**
     * Daily add-to-cart rates.
     */
    dates: Array<DailyAddToCartRates>;
};

type DailyAverageClicks = {
    /**
     * Average position of a clicked search result in the list of search results. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    average: number | null;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetAverageClickPositionResponse = {
    /**
     * Average position of a clicked search result in the list of search results. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    average: number | null;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Daily average click positions.
     */
    dates: Array<DailyAverageClicks>;
};

/**
 * Click position.
 */
type ClickPosition = {
    /**
     * Range of positions in the search results, using the pattern `[start,end]`.  For positions 11 and up, click events are summed over the specified range. `-1` indicates the end of the list of search results.
     */
    position?: Array<number> | undefined;
    /**
     * Number of times this search has been clicked at that position.
     */
    clickCount?: number | undefined;
};

type GetClickPositionsResponse = {
    /**
     * List of positions in the search results and clicks associated with this search.
     */
    positions: Array<ClickPosition>;
};

type DailyClickThroughRates = {
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetClickThroughRateResponse = {
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Daily click-through rates.
     */
    dates: Array<DailyClickThroughRates>;
};

type DailyConversionRates = {
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetConversionRateResponse = {
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
    /**
     * Daily conversion rates.
     */
    dates: Array<DailyConversionRates>;
};

type DailyNoClickRates = {
    /**
     * No click rate: calculated as the number of tracked searches without clicks divided by the number of tracked searches.
     */
    rate: number;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    count: number;
    /**
     * Number of times this search was returned as a result without any click.
     */
    noClickCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetNoClickRateResponse = {
    /**
     * No click rate: calculated as the number of tracked searches without clicks divided by the number of tracked searches.
     */
    rate: number;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    count: number;
    /**
     * Number of times this search was returned as a result without any click.
     */
    noClickCount: number;
    /**
     * Daily no click rates.
     */
    dates: Array<DailyNoClickRates>;
};

type DailyNoResultsRates = {
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
    /**
     * Number of searches without any results.
     */
    noResultCount: number;
    /**
     * Number of searches.
     */
    count: number;
    /**
     * No results rate: calculated as the number of searches with zero results divided by the total number of searches.
     */
    rate: number;
};

type GetNoResultsRateResponse = {
    /**
     * No results rate: calculated as the number of searches with zero results divided by the total number of searches.
     */
    rate: number;
    /**
     * Number of searches.
     */
    count: number;
    /**
     * Number of searches without any results.
     */
    noResultCount: number;
    /**
     * Daily no results rates.
     */
    dates: Array<DailyNoResultsRates>;
};

type DailyPurchaseRates = {
    /**
     * Purchase rate: calculated as the number of tracked searches with at least one purchase event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of purchase events from this search.
     */
    purchaseCount: number;
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetPurchaseRateResponse = {
    /**
     * Purchase rate: calculated as the number of tracked searches with at least one purchase event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    rate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of purchase events from this search.
     */
    purchaseCount: number;
    /**
     * Daily purchase rates.
     */
    dates: Array<DailyPurchaseRates>;
};

/**
 * Currency code.
 */
type CurrencyCode = {
    /**
     * Currency code.
     */
    currency?: string | undefined;
    /**
     * Revenue associated with this search in this currency.
     */
    revenue?: number | undefined;
};

type DailyRevenue = {
    /**
     * Revenue associated with this search: broken down by currency.
     */
    currencies: {
        [key: string]: CurrencyCode;
    };
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
};

type GetRevenue = {
    /**
     * Revenue associated with this search: broken down by currency.
     */
    currencies: {
        [key: string]: CurrencyCode;
    };
    /**
     * Daily revenue.
     */
    dates: Array<DailyRevenue>;
};

type DailySearches = {
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
    /**
     * Number of occurrences.
     */
    count: number;
};

type GetSearchesCountResponse = {
    /**
     * Number of occurrences.
     */
    count: number;
    /**
     * Daily number of searches.
     */
    dates: Array<DailySearches>;
};

type DailySearchesNoClicks = {
    /**
     * Search query.
     */
    search: string;
    /**
     * Number of tracked searches.
     */
    count: number;
    /**
     * Number of results (hits).
     */
    nbHits: number;
};

type GetSearchesNoClicksResponse = {
    /**
     * Searches without any clicks.
     */
    searches: Array<DailySearchesNoClicks>;
};

type DailySearchesNoResults = {
    /**
     * Search query.
     */
    search: string;
    /**
     * Number of occurrences.
     */
    count: number;
    /**
     * Number of searches for this term with applied filters.
     */
    withFilterCount: number;
};

type GetSearchesNoResultsResponse = {
    /**
     * Searches without results.
     */
    searches: Array<DailySearchesNoResults>;
};

type GetStatusResponse = {
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string | null;
};

type TopCountry = {
    /**
     * Country code.
     */
    country: string;
    /**
     * Number of occurrences.
     */
    count: number;
};

type GetTopCountriesResponse = {
    /**
     * Countries and number of searches.
     */
    countries: Array<TopCountry>;
};

type GetTopFilterAttribute = {
    /**
     * Attribute name.
     */
    attribute: string;
    /**
     * Number of occurrences.
     */
    count: number;
};

type GetTopFilterAttributesResponse = {
    /**
     * Most frequent filters.
     */
    attributes: Array<GetTopFilterAttribute>;
};

/**
 * Character that characterizes how the filter is applied.  For example, for a facet filter `facet:value`, `:` is the operator. For a numeric filter `count>50`, `>` is the operator.
 */
type Operator = ':' | '<' | '<=' | '=' | '!=' | '>' | '>=';

type GetTopFilterForAttribute = {
    /**
     * Attribute name.
     */
    attribute: string;
    operator: Operator;
    /**
     * Attribute value.
     */
    value: string;
    /**
     * Number of occurrences.
     */
    count: number;
};

type GetTopFilterForAttributeResponse = {
    /**
     * Filter values for an attribute.
     */
    values: Array<GetTopFilterForAttribute>;
};

type GetTopFiltersNoResultsValue = {
    /**
     * Attribute name.
     */
    attribute: string;
    operator: Operator;
    /**
     * Attribute value.
     */
    value: string;
};

type GetTopFiltersNoResultsValues = {
    /**
     * Number of occurrences.
     */
    count: number;
    /**
     * Filters with no results.
     */
    values: Array<GetTopFiltersNoResultsValue>;
};

type GetTopFiltersNoResultsResponse = {
    /**
     * Filters for searches without any results. If null, the search term specified with the `search` parameter isn\'t a search without results, or the `search` parameter is absent from the request.
     */
    values: Array<GetTopFiltersNoResultsValues> | null;
};

type TopHit = {
    /**
     * Object ID of a record returned as a search result.
     */
    hit: string;
    /**
     * Number of occurrences.
     */
    count: number;
};

type TopHitsResponse = {
    /**
     * Most frequent search results.
     */
    hits: Array<TopHit>;
};

type TopHitWithAnalytics = {
    /**
     * Object ID of a record returned as a search result.
     */
    hit: string;
    /**
     * Number of occurrences.
     */
    count: number;
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    clickThroughRate: number | null;
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    conversionRate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedHitCount: number;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
};

type TopHitsResponseWithAnalytics = {
    /**
     * Most frequent search results with click and conversion metrics.
     */
    hits: Array<TopHitWithAnalytics>;
};

type TopHitWithRevenueAnalytics = {
    /**
     * Object ID of a record returned as a search result.
     */
    hit: string;
    /**
     * Number of occurrences.
     */
    count: number;
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    clickThroughRate: number | null;
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    conversionRate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedHitCount: number;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
    /**
     * Add-to-cart rate: calculated as the number of tracked searches with at least one add-to-cart event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    addToCartRate: number | null;
    /**
     * Number of add-to-cart events from this search.
     */
    addToCartCount: number;
    /**
     * Purchase rate: calculated as the number of tracked searches with at least one purchase event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    purchaseRate: number | null;
    /**
     * Number of purchase events from this search.
     */
    purchaseCount: number;
    /**
     * Revenue associated with this search: broken down by currency.
     */
    currencies: {
        [key: string]: CurrencyCode;
    };
};

type TopHitsResponseWithRevenueAnalytics = {
    /**
     * Most frequent search results with click, conversion, and revenue metrics.
     */
    hits: Array<TopHitWithRevenueAnalytics>;
};

type GetTopHitsResponse = TopHitsResponse | TopHitsResponseWithAnalytics | TopHitsResponseWithRevenueAnalytics;

type TopSearch = {
    /**
     * Search query.
     */
    search: string;
    /**
     * Number of searches.
     */
    count: number;
    /**
     * Number of results (hits).
     */
    nbHits: number;
};

type TopSearchesResponse = {
    /**
     * Most popular searches and their number of search results (hits).
     */
    searches: Array<TopSearch>;
};

type TopSearchWithAnalytics = {
    /**
     * Search query.
     */
    search: string;
    /**
     * Number of searches.
     */
    count: number;
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    clickThroughRate: number | null;
    /**
     * Average position of a clicked search result in the list of search results. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    averageClickPosition: number | null;
    /**
     * List of positions in the search results and clicks associated with this search.
     */
    clickPositions: Array<ClickPosition>;
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    conversionRate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
    /**
     * Number of results (hits).
     */
    nbHits: number;
};

type TopSearchesResponseWithAnalytics = {
    /**
     * Most popular searches and their associated click and conversion metrics.
     */
    searches: Array<TopSearchWithAnalytics>;
};

type TopSearchWithRevenueAnalytics = {
    /**
     * Search query.
     */
    search: string;
    /**
     * Number of searches.
     */
    count: number;
    /**
     * Click-through rate: calculated as the number of tracked searches with at least one click event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    clickThroughRate: number | null;
    /**
     * Average position of a clicked search result in the list of search results. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    averageClickPosition: number | null;
    /**
     * List of positions in the search results and clicks associated with this search.
     */
    clickPositions: Array<ClickPosition>;
    /**
     * Conversion rate: calculated as the number of tracked searches with at least one conversion event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    conversionRate: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount: number;
    /**
     * Number of clicks associated with this search.
     */
    clickCount: number;
    /**
     * Number of conversions from this search.
     */
    conversionCount: number;
    /**
     * Number of results (hits).
     */
    nbHits: number;
    /**
     * Revenue associated with this search: broken down by currency.
     */
    currencies: {
        [key: string]: CurrencyCode;
    };
    /**
     * Add-to-cart rate: calculated as the number of tracked searches with at least one add-to-cart event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    addToCartRate: number | null;
    /**
     * Number of add-to-cart events from this search.
     */
    addToCartCount: number;
    /**
     * Purchase rate: calculated as the number of tracked searches with at least one purchase event divided by the number of tracked searches. If null, Algolia didn\'t receive any search requests with `clickAnalytics` set to true.
     */
    purchaseRate: number | null;
    /**
     * Number of purchase events from this search.
     */
    purchaseCount: number;
};

type TopSearchesResponseWithRevenueAnalytics = {
    /**
     * Most popular searches, including their click and revenue metrics.
     */
    searches: Array<TopSearchWithRevenueAnalytics>;
};

type GetTopSearchesResponse = TopSearchesResponse | TopSearchesResponseWithAnalytics | TopSearchesResponseWithRevenueAnalytics;

type DailyUsers = {
    /**
     * Date in the format YYYY-MM-DD.
     */
    date: string;
    /**
     * Number of unique users.
     */
    count: number;
};

type GetUsersCountResponse = {
    /**
     * Number of unique users.
     */
    count: number;
    /**
     * Daily number of unique users.
     */
    dates: Array<DailyUsers>;
};

type Direction = 'asc' | 'desc';

/**
 * Attribute by which to order the response items.  If the `clickAnalytics` parameter is false, only `searchCount` is available.
 */
type OrderBy = 'searchCount' | 'clickThroughRate' | 'conversionRate' | 'averageClickPosition';

/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `getAddToCartRate` method.
 */
type GetAddToCartRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getAverageClickPosition` method.
 */
type GetAverageClickPositionProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getClickPositions` method.
 */
type GetClickPositionsProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getClickThroughRate` method.
 */
type GetClickThroughRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getConversionRate` method.
 */
type GetConversionRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getNoClickRate` method.
 */
type GetNoClickRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getNoResultsRate` method.
 */
type GetNoResultsRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getPurchaseRate` method.
 */
type GetPurchaseRateProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getRevenue` method.
 */
type GetRevenueProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getSearchesCount` method.
 */
type GetSearchesCountProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getSearchesNoClicks` method.
 */
type GetSearchesNoClicksProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getSearchesNoResults` method.
 */
type GetSearchesNoResultsProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getStatus` method.
 */
type GetStatusProps = {
    /**
     * Index name.
     */
    index: string;
};
/**
 * Properties for the `getTopCountries` method.
 */
type GetTopCountriesProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getTopFilterAttributes` method.
 */
type GetTopFilterAttributesProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Search query.
     */
    search?: string | undefined;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getTopFilterForAttribute` method.
 */
type GetTopFilterForAttributeProps = {
    /**
     * Attribute name.
     */
    attribute: string;
    /**
     * Index name.
     */
    index: string;
    /**
     * Search query.
     */
    search?: string | undefined;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getTopFiltersNoResults` method.
 */
type GetTopFiltersNoResultsProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Search query.
     */
    search?: string | undefined;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getTopHits` method.
 */
type GetTopHitsProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Search query.
     */
    search?: string | undefined;
    /**
     * Whether to include metrics related to click and conversion events in the response.
     */
    clickAnalytics?: boolean | undefined;
    /**
     * Whether to include metrics related to revenue events in the response.
     */
    revenueAnalytics?: boolean | undefined;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getTopSearches` method.
 */
type GetTopSearchesProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Whether to include metrics related to click and conversion events in the response.
     */
    clickAnalytics?: boolean | undefined;
    /**
     * Whether to include metrics related to revenue events in the response.
     */
    revenueAnalytics?: boolean | undefined;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Attribute by which to order the response items.  If the `clickAnalytics` parameter is false, only `searchCount` is available.
     */
    orderBy?: OrderBy | undefined;
    /**
     * Sorting direction of the results: ascending or descending.
     */
    direction?: Direction | undefined;
    /**
     * Number of items to return.
     */
    limit?: number | undefined;
    /**
     * Position of the first item to return.
     */
    offset?: number | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};
/**
 * Properties for the `getUsersCount` method.
 */
type GetUsersCountProps = {
    /**
     * Index name.
     */
    index: string;
    /**
     * Start date of the period to analyze, in `YYYY-MM-DD` format.
     */
    startDate?: string | undefined;
    /**
     * End date of the period to analyze, in `YYYY-MM-DD` format.
     */
    endDate?: string | undefined;
    /**
     * Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    tags?: string | undefined;
};

declare const apiClientVersion = "5.30.0";
declare const REGIONS: readonly ["de", "us"];
type Region = (typeof REGIONS)[number];
type RegionOptions = {
    region?: Region | undefined;
};
declare function createAnalyticsClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, region: regionOption, ...options }: CreateClientOptions & RegionOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string | undefined): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Retrieves the add-to-cart rate for all your searches with at least one add-to-cart event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  The rate is the number of add-to-cart conversion events divided by the number of tracked searches. A search is tracked if it returns a queryID (`clickAnalytics` is `true`). This differs from the response\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`.  **There\'s a difference between a 0 and null add-to-cart rate when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, the add-to-cart rate is null. - **0** mean there _were_ queries but no [add-to-cart events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getAddToCartRate - The getAddToCartRate object.
     * @param getAddToCartRate.index - Index name.
     * @param getAddToCartRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getAddToCartRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getAddToCartRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getAddToCartRate({ index, startDate, endDate, tags }: GetAddToCartRateProps, requestOptions?: RequestOptions): Promise<GetAddToCartRateResponse>;
    /**
     * Retrieves the average click position of your search results, including a daily breakdown.  The average click position is the average of all clicked search result positions. For example, if users only ever click on the first result for any search, the average click position is 1. By default, the analyzed period includes the last eight days including the current day.  An average of `null` when `clickAnalytics` is enabled means Algolia didn\'t receive any [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) for the queries. The average is `null` until Algolia receives at least one click event.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getAverageClickPosition - The getAverageClickPosition object.
     * @param getAverageClickPosition.index - Index name.
     * @param getAverageClickPosition.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getAverageClickPosition.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getAverageClickPosition.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getAverageClickPosition({ index, startDate, endDate, tags }: GetAverageClickPositionProps, requestOptions?: RequestOptions): Promise<GetAverageClickPositionResponse>;
    /**
     * Retrieves the positions in the search results and their associated number of clicks.  This lets you check how many clicks the first, second, or tenth search results receive.  An average of `0` when `clickAnalytics` is enabled means Algolia didn\'t receive any [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) for the queries.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getClickPositions - The getClickPositions object.
     * @param getClickPositions.index - Index name.
     * @param getClickPositions.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getClickPositions.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getClickPositions.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getClickPositions({ index, startDate, endDate, tags }: GetClickPositionsProps, requestOptions?: RequestOptions): Promise<GetClickPositionsResponse>;
    /**
     * Retrieves the click-through rate (CTR) for all your searches with at least one click event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  **There\'s a difference between a 0 and null CTR when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, CTR is null. - **0** mean there _were_ queries but no [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getClickThroughRate - The getClickThroughRate object.
     * @param getClickThroughRate.index - Index name.
     * @param getClickThroughRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getClickThroughRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getClickThroughRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getClickThroughRate({ index, startDate, endDate, tags }: GetClickThroughRateProps, requestOptions?: RequestOptions): Promise<GetClickThroughRateResponse>;
    /**
     * Retrieves the conversion rate (CR) for all your searches with at least one conversion event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  **There\'s a difference between a 0 and null CR when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, CR is null. - **0** mean there _were_ queries but no [conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getConversionRate - The getConversionRate object.
     * @param getConversionRate.index - Index name.
     * @param getConversionRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getConversionRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getConversionRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getConversionRate({ index, startDate, endDate, tags }: GetConversionRateProps, requestOptions?: RequestOptions): Promise<GetConversionRateResponse>;
    /**
     * Retrieves the fraction of searches that didn\'t lead to any click within a time range, including a daily breakdown. It also returns the number of tracked searches and tracked searches without clicks.  By default, the analyzed period includes the last eight days including the current day.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getNoClickRate - The getNoClickRate object.
     * @param getNoClickRate.index - Index name.
     * @param getNoClickRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getNoClickRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getNoClickRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getNoClickRate({ index, startDate, endDate, tags }: GetNoClickRateProps, requestOptions?: RequestOptions): Promise<GetNoClickRateResponse>;
    /**
     * Retrieves the fraction of searches that didn\'t return any results within a time range, including a daily breakdown. It also returns the count of searches and searches without results used to compute the rates.  By default, the analyzed period includes the last eight days including the current day.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getNoResultsRate - The getNoResultsRate object.
     * @param getNoResultsRate.index - Index name.
     * @param getNoResultsRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getNoResultsRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getNoResultsRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getNoResultsRate({ index, startDate, endDate, tags }: GetNoResultsRateProps, requestOptions?: RequestOptions): Promise<GetNoResultsRateResponse>;
    /**
     * Retrieves the purchase rate for all your searches with at least one purchase event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  The rate is the number of purchase conversion events divided by the number of tracked searches. A search is tracked if it returns a query ID (`clickAnalytics` is `true`). This differs from the response\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`.  **There\'s a difference between a 0 and null purchase rate when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, the purchase rate is null. - **0** mean there _were_ queries but no [purchase conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getPurchaseRate - The getPurchaseRate object.
     * @param getPurchaseRate.index - Index name.
     * @param getPurchaseRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getPurchaseRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getPurchaseRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getPurchaseRate({ index, startDate, endDate, tags }: GetPurchaseRateProps, requestOptions?: RequestOptions): Promise<GetPurchaseRateResponse>;
    /**
     * Retrieves revenue-related metrics, such as the total revenue or the average order value.  To retrieve revenue-related metrics, send purchase events. By default, the analyzed period includes the last eight days including the current day.  Revenue is based on purchase conversion events (a conversion event with an `eventSubtype` attribute of `purchase`). The revenue is the `price` attribute multiplied by the `quantity` attribute for each object in the event\'s `objectData` array.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getRevenue - The getRevenue object.
     * @param getRevenue.index - Index name.
     * @param getRevenue.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getRevenue.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getRevenue.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getRevenue({ index, startDate, endDate, tags }: GetRevenueProps, requestOptions?: RequestOptions): Promise<GetRevenue>;
    /**
     * Retrieves the number of searches within a time range, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getSearchesCount - The getSearchesCount object.
     * @param getSearchesCount.index - Index name.
     * @param getSearchesCount.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesCount.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesCount.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSearchesCount({ index, startDate, endDate, tags }: GetSearchesCountProps, requestOptions?: RequestOptions): Promise<GetSearchesCountResponse>;
    /**
     * Retrieves the most popular searches that didn\'t lead to any clicks, from the 1,000 most frequent searches.  For each search, it also returns the number of displayed search results that remained unclicked.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getSearchesNoClicks - The getSearchesNoClicks object.
     * @param getSearchesNoClicks.index - Index name.
     * @param getSearchesNoClicks.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesNoClicks.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesNoClicks.limit - Number of items to return.
     * @param getSearchesNoClicks.offset - Position of the first item to return.
     * @param getSearchesNoClicks.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSearchesNoClicks({ index, startDate, endDate, limit, offset, tags }: GetSearchesNoClicksProps, requestOptions?: RequestOptions): Promise<GetSearchesNoClicksResponse>;
    /**
     * Retrieves the 1,000 most frequent searches that produced zero results.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getSearchesNoResults - The getSearchesNoResults object.
     * @param getSearchesNoResults.index - Index name.
     * @param getSearchesNoResults.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesNoResults.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getSearchesNoResults.limit - Number of items to return.
     * @param getSearchesNoResults.offset - Position of the first item to return.
     * @param getSearchesNoResults.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getSearchesNoResults({ index, startDate, endDate, limit, offset, tags }: GetSearchesNoResultsProps, requestOptions?: RequestOptions): Promise<GetSearchesNoResultsResponse>;
    /**
     * Retrieves the time when the Analytics data for the specified index was last updated.  If the index has been recently created or no search has been performed yet the updated time is `null`.  The Analytics data is updated every 5&nbsp;minutes.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getStatus - The getStatus object.
     * @param getStatus.index - Index name.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getStatus({ index }: GetStatusProps, requestOptions?: RequestOptions): Promise<GetStatusResponse>;
    /**
     * Retrieves the countries with the most searches in your index.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopCountries - The getTopCountries object.
     * @param getTopCountries.index - Index name.
     * @param getTopCountries.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopCountries.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopCountries.limit - Number of items to return.
     * @param getTopCountries.offset - Position of the first item to return.
     * @param getTopCountries.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopCountries({ index, startDate, endDate, limit, offset, tags }: GetTopCountriesProps, requestOptions?: RequestOptions): Promise<GetTopCountriesResponse>;
    /**
     * Retrieves the 1,000 most frequently used filter attributes.  These are attributes of your records that you included in the `attributesForFaceting` setting.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopFilterAttributes - The getTopFilterAttributes object.
     * @param getTopFilterAttributes.index - Index name.
     * @param getTopFilterAttributes.search - Search query.
     * @param getTopFilterAttributes.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFilterAttributes.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFilterAttributes.limit - Number of items to return.
     * @param getTopFilterAttributes.offset - Position of the first item to return.
     * @param getTopFilterAttributes.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopFilterAttributes({ index, search, startDate, endDate, limit, offset, tags }: GetTopFilterAttributesProps, requestOptions?: RequestOptions): Promise<GetTopFilterAttributesResponse>;
    /**
     * Retrieves the 1,000 most frequent filter (facet) values for a filter attribute.  These are attributes of your records that you included in the `attributesForFaceting` setting.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopFilterForAttribute - The getTopFilterForAttribute object.
     * @param getTopFilterForAttribute.attribute - Attribute name.
     * @param getTopFilterForAttribute.index - Index name.
     * @param getTopFilterForAttribute.search - Search query.
     * @param getTopFilterForAttribute.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFilterForAttribute.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFilterForAttribute.limit - Number of items to return.
     * @param getTopFilterForAttribute.offset - Position of the first item to return.
     * @param getTopFilterForAttribute.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopFilterForAttribute({ attribute, index, search, startDate, endDate, limit, offset, tags }: GetTopFilterForAttributeProps, requestOptions?: RequestOptions): Promise<GetTopFilterForAttributeResponse>;
    /**
     * Retrieves the 1,000 most frequently used filters for a search that didn\'t return any results.  To get the most frequent searches without results, use the [Retrieve searches without results](#tag/search/operation/getSearchesNoResults) operation.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopFiltersNoResults - The getTopFiltersNoResults object.
     * @param getTopFiltersNoResults.index - Index name.
     * @param getTopFiltersNoResults.search - Search query.
     * @param getTopFiltersNoResults.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFiltersNoResults.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopFiltersNoResults.limit - Number of items to return.
     * @param getTopFiltersNoResults.offset - Position of the first item to return.
     * @param getTopFiltersNoResults.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopFiltersNoResults({ index, search, startDate, endDate, limit, offset, tags }: GetTopFiltersNoResultsProps, requestOptions?: RequestOptions): Promise<GetTopFiltersNoResultsResponse>;
    /**
     * Retrieves the object IDs of the 1,000 most frequent search results.  If you set the `clickAnalytics` query parameter to true, the response also includes:  - Tracked searches count. Tracked searches are Search API requests with the `clickAnalytics` parameter set to `true`. This differs from the response\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`. - Click count - Click-through rate (CTR) - Conversion count - Conversion rate (CR) - Average click position  If you set the `revenueAnalytics` parameter to `true`, the response also includes:  - Add-to-cart count - Add-to-cart rate (ATCR) - Purchase count - Purchase rate - Revenue details for each currency  **There\'s a difference between 0% rates and null rates:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, the rates (CTR, CR, ATCR, purchase rate) are null. - **0% rates** mean there _were_ queries but no [click or conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopHits - The getTopHits object.
     * @param getTopHits.index - Index name.
     * @param getTopHits.search - Search query.
     * @param getTopHits.clickAnalytics - Whether to include metrics related to click and conversion events in the response.
     * @param getTopHits.revenueAnalytics - Whether to include metrics related to revenue events in the response.
     * @param getTopHits.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopHits.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopHits.limit - Number of items to return.
     * @param getTopHits.offset - Position of the first item to return.
     * @param getTopHits.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopHits({ index, search, clickAnalytics, revenueAnalytics, startDate, endDate, limit, offset, tags }: GetTopHitsProps, requestOptions?: RequestOptions): Promise<GetTopHitsResponse>;
    /**
     * Returns the most popular searches. For each search, it also includes the average number of hits.  If you set the `clickAnalytics` query parameter to `true`, the response also includes  - Tracked searches count. Tracked searches are Search API requests with the `clickAnalytics` parameter set to `true`. This differs from the response\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`. - Click count - Click-through rate (CTR) - Conversion count - Conversion rate (CR) - Average click position  If you set the `revenueAnalytics` query parameter to `true`, the response also includes:  - Add-to-cart count - Add-to-cart rate (ATCR) - Purchase count - Purchase rate - Revenue details for each currency  **There\'s a difference between 0% rates and null rates:**  - **Null** means there were no queries: since Algolia didn\'t receive any events, the rates (CTR, CR, ATCR, purchase rate) are null. - **0% rates** mean there _were_ queries but no [click or conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getTopSearches - The getTopSearches object.
     * @param getTopSearches.index - Index name.
     * @param getTopSearches.clickAnalytics - Whether to include metrics related to click and conversion events in the response.
     * @param getTopSearches.revenueAnalytics - Whether to include metrics related to revenue events in the response.
     * @param getTopSearches.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopSearches.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getTopSearches.orderBy - Attribute by which to order the response items.  If the `clickAnalytics` parameter is false, only `searchCount` is available.
     * @param getTopSearches.direction - Sorting direction of the results: ascending or descending.
     * @param getTopSearches.limit - Number of items to return.
     * @param getTopSearches.offset - Position of the first item to return.
     * @param getTopSearches.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getTopSearches({ index, clickAnalytics, revenueAnalytics, startDate, endDate, orderBy, direction, limit, offset, tags, }: GetTopSearchesProps, requestOptions?: RequestOptions): Promise<GetTopSearchesResponse>;
    /**
     * Retrieves the number of unique users within a time range, including a daily breakdown.  Since it returns the number of unique users, the sum of the daily values might be different from the total number.  By default:  - Algolia distinguishes search users by their IP address, _unless_ you include a pseudonymous user identifier in your search requests with the `userToken` API parameter or `x-algolia-usertoken` request header. - The analyzed period includes the last eight days including the current day.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getUsersCount - The getUsersCount object.
     * @param getUsersCount.index - Index name.
     * @param getUsersCount.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getUsersCount.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.
     * @param getUsersCount.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getUsersCount({ index, startDate, endDate, tags }: GetUsersCountProps, requestOptions?: RequestOptions): Promise<GetUsersCountResponse>;
};

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string | undefined;
};

declare function analyticsClient(appId: string, apiKey: string, region?: Region | undefined, options?: ClientOptions | undefined): AnalyticsClient;
type AnalyticsClient = ReturnType<typeof createAnalyticsClient>;

export { type AnalyticsClient, type ClickPosition, type CurrencyCode, type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type DailyAddToCartRates, type DailyAverageClicks, type DailyClickThroughRates, type DailyConversionRates, type DailyNoClickRates, type DailyNoResultsRates, type DailyPurchaseRates, type DailyRevenue, type DailySearches, type DailySearchesNoClicks, type DailySearchesNoResults, type DailyUsers, type Direction, type ErrorBase, type GetAddToCartRateProps, type GetAddToCartRateResponse, type GetAverageClickPositionProps, type GetAverageClickPositionResponse, type GetClickPositionsProps, type GetClickPositionsResponse, type GetClickThroughRateProps, type GetClickThroughRateResponse, type GetConversionRateProps, type GetConversionRateResponse, type GetNoClickRateProps, type GetNoClickRateResponse, type GetNoResultsRateProps, type GetNoResultsRateResponse, type GetPurchaseRateProps, type GetPurchaseRateResponse, type GetRevenue, type GetRevenueProps, type GetSearchesCountProps, type GetSearchesCountResponse, type GetSearchesNoClicksProps, type GetSearchesNoClicksResponse, type GetSearchesNoResultsProps, type GetSearchesNoResultsResponse, type GetStatusProps, type GetStatusResponse, type GetTopCountriesProps, type GetTopCountriesResponse, type GetTopFilterAttribute, type GetTopFilterAttributesProps, type GetTopFilterAttributesResponse, type GetTopFilterForAttribute, type GetTopFilterForAttributeProps, type GetTopFilterForAttributeResponse, type GetTopFiltersNoResultsProps, type GetTopFiltersNoResultsResponse, type GetTopFiltersNoResultsValue, type GetTopFiltersNoResultsValues, type GetTopHitsProps, type GetTopHitsResponse, type GetTopSearchesProps, type GetTopSearchesResponse, type GetUsersCountProps, type GetUsersCountResponse, type Operator, type OrderBy, type Region, type RegionOptions, type TopCountry, type TopHit, type TopHitWithAnalytics, type TopHitWithRevenueAnalytics, type TopHitsResponse, type TopHitsResponseWithAnalytics, type TopHitsResponseWithRevenueAnalytics, type TopSearch, type TopSearchWithAnalytics, type TopSearchWithRevenueAnalytics, type TopSearchesResponse, type TopSearchesResponseWithAnalytics, type TopSearchesResponseWithRevenueAnalytics, analyticsClient, apiClientVersion };
