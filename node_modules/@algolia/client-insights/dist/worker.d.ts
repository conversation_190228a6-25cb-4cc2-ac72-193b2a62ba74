import * as _algolia_client_common from '@algolia/client-common';
import { CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

/**
 * The response of the Insights API.
 */
type EventsResponse = {
    /**
     * Details about the response, such as error messages.
     */
    message?: string | undefined;
    /**
     * The HTTP status code of the response.
     */
    status?: number | undefined;
};

type AddToCartEvent = 'addToCart';

type ConversionEvent = 'conversion';

/**
 * Absolute value of the discount for this product, in units of `currency`.
 */
type Discount = number | string;

/**
 * Total price of a product, including any discounts, in units of `currency`.
 */
type Price = number | string;

type ObjectData = {
    price?: Price | undefined;
    /**
     * Quantity of a product that has been purchased or added to the cart. The total purchase value is the sum of `quantity` multiplied with the `price` for each purchased item.
     */
    quantity?: number | undefined;
    discount?: Discount | undefined;
};

/**
 * Total monetary value of this event in units of `currency`.
 */
type Value = number | string;

/**
 * Use this event to track when users add items to their shopping cart unrelated to a previous Algolia request. For example, if you don\'t use Algolia to build your category pages, use this event.  To track add-to-cart events related to Algolia requests, use the \"Added to cart object IDs after search\" event.
 */
type AddedToCartObjectIDs = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    eventSubtype: AddToCartEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Three-letter [currency code](https://www.iso.org/iso-4217-currency-codes.html).
     */
    currency?: string | undefined;
    /**
     * Extra information about the records involved in a purchase or add-to-cart event.  If specified, it must have the same length as `objectIDs`.
     */
    objectData?: Array<ObjectData> | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
    value?: Value | undefined;
};

type ObjectDataAfterSearch = {
    /**
     * Unique identifier for a search query, used to track purchase events with multiple records that originate from different searches.
     */
    queryID?: string | undefined;
    price?: Price | undefined;
    /**
     * Quantity of a product that has been purchased or added to the cart. The total purchase value is the sum of `quantity` multiplied with the `price` for each purchased item.
     */
    quantity?: number | undefined;
    discount?: Discount | undefined;
};

/**
 * Use this event to track when users add items to their shopping cart after a previous Algolia request. If you\'re building your category pages with Algolia, you\'ll also use this event.
 */
type AddedToCartObjectIDsAfterSearch = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    eventSubtype: AddToCartEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Unique identifier for a search query.  The query ID is required for events related to search or browse requests. If you add `clickAnalytics: true` as a search request parameter, the query ID is included in the API response.
     */
    queryID: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Three-letter [currency code](https://www.iso.org/iso-4217-currency-codes.html).
     */
    currency?: string | undefined;
    /**
     * Extra information about the records involved in a purchase or add-to-cart events.  If provided, it must be the same length as `objectIDs`.
     */
    objectData?: Array<ObjectDataAfterSearch> | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
    value?: Value | undefined;
};

type ClickEvent = 'click';

/**
 * Use this event to track when users click facet filters in your user interface.
 */
type ClickedFilters = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ClickEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Applied facet filters.  Facet filters are `facet:value` pairs. Facet values must be URL-encoded, such as, `discount:10%25`.
     */
    filters: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

/**
 * Use this event to track when users click items unrelated to a previous Algolia request. For example, if you don\'t use Algolia to build your category pages, use this event.  To track click events related to Algolia requests, use the \"Clicked object IDs after search\" event.
 */
type ClickedObjectIDs = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ClickEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

/**
 * Click event after an Algolia request.  Use this event to track when users click items in the search results. If you\'re building your category pages with Algolia, you\'ll also use this event.
 */
type ClickedObjectIDsAfterSearch = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ClickEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Position of the clicked item the search results.  You must provide 1 `position` for each `objectID`.
     */
    positions: Array<number>;
    /**
     * Unique identifier for a search query.  The query ID is required for events related to search or browse requests. If you add `clickAnalytics: true` as a search request parameter, the query ID is included in the API response.
     */
    queryID: string;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

type ConvertedFilters = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Applied facet filters.  Facet filters are `facet:value` pairs. Facet values must be URL-encoded, such as, `discount:10%25`.
     */
    filters: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

/**
 * Use this event to track when users convert on items unrelated to a previous Algolia request. For example, if you don\'t use Algolia to build your category pages, use this event.  To track conversion events related to Algolia requests, use the \"Converted object IDs after search\" event.
 */
type ConvertedObjectIDs = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

/**
 * Use this event to track when users convert after a previous Algolia request. For example, a user clicks on an item in the search results to view the product detail page. Then, the user adds the item to their shopping cart.  If you\'re building your category pages with Algolia, you\'ll also use this event.
 */
type ConvertedObjectIDsAfterSearch = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Unique identifier for a search query.  The query ID is required for events related to search or browse requests. If you add `clickAnalytics: true` as a search request parameter, the query ID is included in the API response.
     */
    queryID: string;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

type PurchaseEvent = 'purchase';

/**
 * Use this event to track when users make a purchase unrelated to a previous Algolia request. For example, if you don\'t use Algolia to build your category pages, use this event.  To track purchase events related to Algolia requests, use the \"Purchased object IDs after search\" event.
 */
type PurchasedObjectIDs = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    eventSubtype: PurchaseEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Three-letter [currency code](https://www.iso.org/iso-4217-currency-codes.html).
     */
    currency?: string | undefined;
    /**
     * Extra information about the records involved in a purchase or add-to-cart event.  If specified, it must have the same length as `objectIDs`.
     */
    objectData?: Array<ObjectData> | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
    value?: Value | undefined;
};

/**
 * Use this event to track when users make a purchase after a previous Algolia request. If you\'re building your category pages with Algolia, you\'ll also use this event.
 */
type PurchasedObjectIDsAfterSearch = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ConversionEvent;
    eventSubtype: PurchaseEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Three-letter [currency code](https://www.iso.org/iso-4217-currency-codes.html).
     */
    currency?: string | undefined;
    /**
     * Extra information about the records involved in a purchase or add-to-cart events.  If provided, it must be the same length as `objectIDs`.
     */
    objectData: Array<ObjectDataAfterSearch>;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
    value?: Value | undefined;
};

type ViewEvent = 'view';

/**
 * Use this method to capture active filters. For example, when browsing a category page, users see content filtered on that specific category.
 */
type ViewedFilters = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ViewEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Applied facet filters.  Facet filters are `facet:value` pairs. Facet values must be URL-encoded, such as, `discount:10%25`.
     */
    filters: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

/**
 * Use this event to track when users viewed items in the search results.
 */
type ViewedObjectIDs = {
    /**
     * Event name, up to 64 ASCII characters.  Consider naming events consistently—for example, by adopting Segment\'s [object-action](https://segment.com/academy/collecting-data/naming-conventions-for-clean-data/#the-object-action-framework) framework.
     */
    eventName: string;
    eventType: ViewEvent;
    /**
     * Index name (case-sensitive) to which the event\'s items belong.
     */
    index: string;
    /**
     * Object IDs of the records that are part of the event.
     */
    objectIDs: Array<string>;
    /**
     * Anonymous or pseudonymous user identifier.  Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Identifier for authenticated users.  When the user signs in, you can get an identifier from your system and send it as `authenticatedUserToken`. This lets you keep using the `userToken` from before the user signed in, while providing a reliable way to identify users across sessions. Don\'t use personally identifiable information in user tokens. For more information, see [User token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    authenticatedUserToken?: string | undefined;
    /**
     * Timestamp of the event, measured in milliseconds since the Unix epoch. By default, the Insights API uses the time it receives an event as its timestamp.
     */
    timestamp?: number | undefined;
};

type EventsItems = ClickedObjectIDsAfterSearch | AddedToCartObjectIDsAfterSearch | PurchasedObjectIDsAfterSearch | ConvertedObjectIDsAfterSearch | ClickedObjectIDs | PurchasedObjectIDs | AddedToCartObjectIDs | ConvertedObjectIDs | ClickedFilters | ConvertedFilters | ViewedObjectIDs | ViewedFilters;

type InsightsEvents = {
    /**
     * Click and conversion events.  **All** events must be valid, otherwise the API returns an error.
     */
    events: Array<EventsItems>;
};

/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `deleteUserToken` method.
 */
type DeleteUserTokenProps = {
    /**
     * User token for which to delete all associated events.
     */
    userToken: string;
};

declare const apiClientVersion = "5.30.0";
declare const REGIONS: readonly ["de", "us"];
type Region = (typeof REGIONS)[number];
type RegionOptions = {
    region?: Region | undefined;
};
declare function createInsightsClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, region: regionOption, ...options }: CreateClientOptions & RegionOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string | undefined): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Deletes all events related to the specified user token from events metrics and analytics. The deletion is asynchronous, and processed within 48 hours. To delete a personalization user profile, see `Delete a user profile` in the Personalization API.
     *
     * Required API Key ACLs:
     *  - deleteObject
     * @param deleteUserToken - The deleteUserToken object.
     * @param deleteUserToken.userToken - User token for which to delete all associated events.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteUserToken({ userToken }: DeleteUserTokenProps, requestOptions?: RequestOptions): Promise<void>;
    /**
     * Sends a list of events to the Insights API.  You can include up to 1,000 events in a single request, but the request body must be smaller than 2&nbsp;MB.
     *
     * Required API Key ACLs:
     *  - search
     * @param insightsEvents - The insightsEvents object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    pushEvents(insightsEvents: InsightsEvents, requestOptions?: RequestOptions): Promise<EventsResponse>;
};

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string | undefined;
};

type InsightsClient = ReturnType<typeof createInsightsClient>;

declare function insightsClient(appId: string, apiKey: string, region?: Region | undefined, options?: ClientOptions | undefined): InsightsClient;

export { type AddToCartEvent, type AddedToCartObjectIDs, type AddedToCartObjectIDsAfterSearch, type ClickEvent, type ClickedFilters, type ClickedObjectIDs, type ClickedObjectIDsAfterSearch, type ConversionEvent, type ConvertedFilters, type ConvertedObjectIDs, type ConvertedObjectIDsAfterSearch, type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type DeleteUserTokenProps, type Discount, type ErrorBase, type EventsItems, type EventsResponse, type InsightsClient, type InsightsEvents, type ObjectData, type ObjectDataAfterSearch, type Price, type PurchaseEvent, type PurchasedObjectIDs, type PurchasedObjectIDsAfterSearch, type Region, type RegionOptions, type Value, type ViewEvent, type ViewedFilters, type ViewedObjectIDs, apiClientVersion, insightsClient };
