(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@algolia/client-insights"] = {}));
})(this, (function (exports) { 'use strict';

	function H(){function r(e){return new Promise(s=>{let t=new XMLHttpRequest;t.open(e.method,e.url,true),Object.keys(e.headers).forEach(a=>t.setRequestHeader(a,e.headers[a]));let n=(a,o)=>setTimeout(()=>{t.abort(),s({status:0,content:o,isTimedOut:true});},a),l=n(e.connectTimeout,"Connection timeout"),p;t.onreadystatechange=()=>{t.readyState>t.OPENED&&p===void 0&&(clearTimeout(l),p=n(e.responseTimeout,"Socket timeout"));},t.onerror=()=>{t.status===0&&(clearTimeout(l),clearTimeout(p),s({content:t.responseText||"Network request failed",status:t.status,isTimedOut:false}));},t.onload=()=>{clearTimeout(l),clearTimeout(p),s({content:t.responseText,status:t.status,isTimedOut:false});},t.send(e.data);})}return {send:r}}function W(r){let e,s=`algolia-client-js-${r.key}`;function t(){return e===void 0&&(e=r.localStorage||window.localStorage),e}function n(){return JSON.parse(t().getItem(s)||"{}")}function l(a){t().setItem(s,JSON.stringify(a));}function p(){let a=r.timeToLive?r.timeToLive*1e3:null,o=n(),i=Object.fromEntries(Object.entries(o).filter(([,h])=>h.timestamp!==void 0));if(l(i),!a)return;let m=Object.fromEntries(Object.entries(i).filter(([,h])=>{let y=new Date().getTime();return !(h.timestamp+a<y)}));l(m);}return {get(a,o,i={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(p(),n()[JSON.stringify(a)])).then(m=>Promise.all([m?m.value:o(),m!==void 0])).then(([m,h])=>Promise.all([m,h||i.miss(m)])).then(([m])=>m)},set(a,o){return Promise.resolve().then(()=>{let i=n();return i[JSON.stringify(a)]={timestamp:new Date().getTime(),value:o},t().setItem(s,JSON.stringify(i)),o})},delete(a){return Promise.resolve().then(()=>{let o=n();delete o[JSON.stringify(a)],t().setItem(s,JSON.stringify(o));})},clear(){return Promise.resolve().then(()=>{t().removeItem(s);})}}}function V(){return {get(r,e,s={miss:()=>Promise.resolve()}){return e().then(n=>Promise.all([n,s.miss(n)])).then(([n])=>n)},set(r,e){return Promise.resolve(e)},delete(r){return Promise.resolve()},clear(){return Promise.resolve()}}}function x(r){let e=[...r.caches],s=e.shift();return s===void 0?V():{get(t,n,l={miss:()=>Promise.resolve()}){return s.get(t,n,l).catch(()=>x({caches:e}).get(t,n,l))},set(t,n){return s.set(t,n).catch(()=>x({caches:e}).set(t,n))},delete(t){return s.delete(t).catch(()=>x({caches:e}).delete(t))},clear(){return s.clear().catch(()=>x({caches:e}).clear())}}}function q(r={serializable:true}){let e={};return {get(s,t,n={miss:()=>Promise.resolve()}){let l=JSON.stringify(s);if(l in e)return Promise.resolve(r.serializable?JSON.parse(e[l]):e[l]);let p=t();return p.then(a=>n.miss(a)).then(()=>p)},set(s,t){return e[JSON.stringify(s)]=r.serializable?JSON.stringify(t):t,Promise.resolve(t)},delete(s){return delete e[JSON.stringify(s)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function Y(r){let e={value:`Algolia for JavaScript (${r})`,add(s){let t=`; ${s.segment}${s.version!==void 0?` (${s.version})`:""}`;return e.value.indexOf(t)===-1&&(e.value=`${e.value}${t}`),e}};return e}function J(r,e,s="WithinHeaders"){let t={"x-algolia-api-key":e,"x-algolia-application-id":r};return {headers(){return s==="WithinHeaders"?t:{}},queryParameters(){return s==="WithinQueryParameters"?t:{}}}}function z({algoliaAgents:r,client:e,version:s}){let t=Y(s).add({segment:e,version:s});return r.forEach(n=>t.add(n)),t}function M(){return {debug(r,e){return Promise.resolve()},info(r,e){return Promise.resolve()},error(r,e){return Promise.resolve()}}}var $=2*60*1e3;function j(r,e="up"){let s=Date.now();function t(){return e==="up"||Date.now()-s>$}function n(){return e==="timed out"&&Date.now()-s<=$}return {...r,status:e,lastUpdate:s,isUp:t,isTimedOut:n}}var Q=class extends Error{name="AlgoliaError";constructor(r,e){super(r),e&&(this.name=e);}};var G=class extends Q{stackTrace;constructor(r,e,s){super(r,s),this.stackTrace=e;}},Z=class extends G{constructor(r){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",r,"RetryError");}},b=class extends G{status;constructor(r,e,s,t="ApiError"){super(r,s,t),this.status=e;}},ee=class extends Q{response;constructor(r,e){super(r,"DeserializationError"),this.response=e;}},re=class extends b{error;constructor(r,e,s,t){super(r,e,t,"DetailedApiError"),this.error=s;}};function te(r,e,s){let t=se(s),n=`${r.protocol}://${r.url}${r.port?`:${r.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return t.length&&(n+=`?${t}`),n}function se(r){return Object.keys(r).filter(e=>r[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(r[e])==="[object Array]"?r[e].join(","):r[e]).replace(/\+/g,"%20")}`).join("&")}function oe(r,e){if(r.method==="GET"||r.data===void 0&&e.data===void 0)return;let s=Array.isArray(r.data)?r.data:{...r.data,...e.data};return JSON.stringify(s)}function ne(r,e,s){let t={Accept:"application/json",...r,...e,...s},n={};return Object.keys(t).forEach(l=>{let p=t[l];n[l.toLowerCase()]=p;}),n}function ae(r){try{return JSON.parse(r.content)}catch(e){throw new ee(e.message,r)}}function ie({content:r,status:e},s){try{let t=JSON.parse(r);return "error"in t?new re(t.message,e,t.error,s):new b(t.message,e,s)}catch{}return new b(r,e,s)}function ce({isTimedOut:r,status:e}){return !r&&~~e===0}function ue({isTimedOut:r,status:e}){return r||ce({isTimedOut:r,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function le({status:r}){return ~~(r/100)===2}function me(r){return r.map(e=>F(e))}function F(r){let e=r.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...r,request:{...r.request,headers:{...r.request.headers,...e}}}}function B({hosts:r,hostsCache:e,baseHeaders:s,logger:t,baseQueryParameters:n,algoliaAgent:l,timeouts:p,requester:a,requestsCache:o,responsesCache:i}){async function m(c){let u=await Promise.all(c.map(d=>e.get(d,()=>Promise.resolve(j(d))))),g=u.filter(d=>d.isUp()),P=u.filter(d=>d.isTimedOut()),w=[...g,...P];return {hosts:w.length>0?w:c,getTimeout(d,v){return (P.length===0&&d===0?1:P.length+3+d)*v}}}async function h(c,u,g=true){let P=[],w=oe(c,u),E=ne(s,c.headers,u.headers),d=c.method==="GET"?{...c.data,...u.data}:{},v={...n,...c.queryParameters,...d};if(l.value&&(v["x-algolia-agent"]=l.value),u&&u.queryParameters)for(let f of Object.keys(u.queryParameters))!u.queryParameters[f]||Object.prototype.toString.call(u.queryParameters[f])==="[object Object]"?v[f]=u.queryParameters[f]:v[f]=u.queryParameters[f].toString();let O=0,D=async(f,S)=>{let R=f.pop();if(R===void 0)throw new Z(me(P));let C={...p,...u.timeouts},_={data:w,headers:E,method:c.method,url:te(R,c.path,v),connectTimeout:S(O,C.connect),responseTimeout:S(O,g?C.read:C.write)},U=I=>{let L={request:_,response:I,host:R,triesLeft:f.length};return P.push(L),L},T=await a.send(_);if(ue(T)){let I=U(T);return T.isTimedOut&&O++,t.info("Retryable failure",F(I)),await e.set(R,j(R,T.isTimedOut?"timed out":"down")),D(f,S)}if(le(T))return ae(T);throw U(T),ie(T,P)},K=r.filter(f=>f.accept==="readWrite"||(g?f.accept==="read":f.accept==="write")),k=await m(K);return D([...k.hosts].reverse(),k.getTimeout)}function y(c,u={}){let g=c.useReadTransporter||c.method==="GET";if(!g)return h(c,u,g);let P=()=>h(c,u);if((u.cacheable||c.cacheable)!==true)return P();let E={request:c,requestOptions:u,transporter:{queryParameters:n,headers:s}};return i.get(E,()=>o.get(E,()=>o.set(E,P()).then(d=>Promise.all([o.delete(E),d]),d=>Promise.all([o.delete(E),Promise.reject(d)])).then(([d,v])=>v)),{miss:d=>i.set(E,d)})}return {hostsCache:e,requester:a,timeouts:p,logger:t,algoliaAgent:l,baseHeaders:s,baseQueryParameters:n,hosts:r,request:y,requestsCache:o,responsesCache:i}}var A="5.30.0",N=["de","us"];function de(r){return [{url:r?"insights.{region}.algolia.io".replace("{region}",r):"insights.algolia.io",accept:"readWrite",protocol:"https"}]}function X({appId:r,apiKey:e,authMode:s,algoliaAgents:t,region:n,...l}){let p=J(r,e,s),a=B({hosts:de(n),...l,algoliaAgent:z({algoliaAgents:t,client:"Insights",version:A}),baseHeaders:{"content-type":"text/plain",...p.headers(),...l.baseHeaders},baseQueryParameters:{...p.queryParameters(),...l.baseQueryParameters}});return {transporter:a,appId:r,apiKey:e,clearCache(){return Promise.all([a.requestsCache.clear(),a.responsesCache.clear()]).then(()=>{})},get _ua(){return a.algoliaAgent.value},addAlgoliaAgent(o,i){a.algoliaAgent.add({segment:o,version:i});},setClientApiKey({apiKey:o}){!s||s==="WithinHeaders"?a.baseHeaders["x-algolia-api-key"]=o:a.baseQueryParameters["x-algolia-api-key"]=o;},customDelete({path:o,parameters:i},m){if(!o)throw new Error("Parameter `path` is required when calling `customDelete`.");let u={method:"DELETE",path:"/{path}".replace("{path}",o),queryParameters:i||{},headers:{}};return a.request(u,m)},customGet({path:o,parameters:i},m){if(!o)throw new Error("Parameter `path` is required when calling `customGet`.");let u={method:"GET",path:"/{path}".replace("{path}",o),queryParameters:i||{},headers:{}};return a.request(u,m)},customPost({path:o,parameters:i,body:m},h){if(!o)throw new Error("Parameter `path` is required when calling `customPost`.");let g={method:"POST",path:"/{path}".replace("{path}",o),queryParameters:i||{},headers:{},data:m||{}};return a.request(g,h)},customPut({path:o,parameters:i,body:m},h){if(!o)throw new Error("Parameter `path` is required when calling `customPut`.");let g={method:"PUT",path:"/{path}".replace("{path}",o),queryParameters:i||{},headers:{},data:m||{}};return a.request(g,h)},deleteUserToken({userToken:o},i){if(!o)throw new Error("Parameter `userToken` is required when calling `deleteUserToken`.");let c={method:"DELETE",path:"/1/usertokens/{userToken}".replace("{userToken}",encodeURIComponent(o)),queryParameters:{},headers:{}};return a.request(c,i)},pushEvents(o,i){if(!o)throw new Error("Parameter `insightsEvents` is required when calling `pushEvents`.");if(!o.events)throw new Error("Parameter `insightsEvents.events` is required when calling `pushEvents`.");let c={method:"POST",path:"/1/events",queryParameters:{},headers:{},data:o};return a.request(c,i)}}}function er(r,e,s,t){if(!r||typeof r!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");if(s&&(typeof s!="string"||!N.includes(s)))throw new Error(`\`region\` must be one of the following: ${N.join(", ")}`);return X({appId:r,apiKey:e,region:s,timeouts:{connect:1e3,read:2e3,write:3e4},logger:M(),requester:H(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:q(),requestsCache:q({serializable:false}),hostsCache:x({caches:[W({key:`${A}-${r}`}),q()]}),...t})}

	exports.apiClientVersion = A;
	exports.insightsClient = er;

}));
