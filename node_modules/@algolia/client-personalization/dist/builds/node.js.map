{"version": 3, "sources": ["../../builds/node.ts", "../../src/personalizationClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type PersonalizationClient = ReturnType<typeof createPersonalizationClient>;\n\nimport { createHttpRequester } from '@algolia/requester-node-http';\n\nimport { createMemoryCache, createNullCache, createNullLogger } from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { createPersonalizationClient } from '../src/personalizationClient';\n\nimport type { Region } from '../src/personalizationClient';\nimport { REGIONS } from '../src/personalizationClient';\n\nexport type { Region, RegionOptions } from '../src/personalizationClient';\n\nexport { apiClientVersion } from '../src/personalizationClient';\n\nexport * from '../model';\n\nexport function personalizationClient(\n  appId: string,\n  apiKey: string,\n  region: Region,\n  options?: ClientOptions | undefined,\n): PersonalizationClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (!region || (region && (typeof region !== 'string' || !REGIONS.includes(region)))) {\n    throw new Error(`\\`region\\` is required and must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return {\n    ...createPersonalizationClient({\n      appId,\n      apiKey,\n      region,\n      timeouts: {\n        connect: 2000,\n        read: 5000,\n        write: 30000,\n      },\n      logger: createNullLogger(),\n      requester: createHttpRequester(),\n      algoliaAgents: [{ segment: 'Node.js', version: process.versions.node }],\n      responsesCache: createNullCache(),\n      requestsCache: createNullCache(),\n      hostsCache: createMemoryCache(),\n      ...options,\n    }),\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { DeleteUserProfileResponse } from '../model/deleteUserProfileResponse';\n\nimport type { GetUserTokenResponse } from '../model/getUserTokenResponse';\nimport type { PersonalizationStrategyParams } from '../model/personalizationStrategyParams';\nimport type { SetPersonalizationStrategyResponse } from '../model/setPersonalizationStrategyResponse';\n\nimport type {\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteUserProfileProps,\n  GetUserTokenProfileProps,\n} from '../model/clientMethodProps';\n\nexport const apiClientVersion = '5.30.0';\n\nexport const REGIONS = ['eu', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region: Region };\n\nfunction getDefaultHosts(region: Region): Host[] {\n  const url = 'personalization.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\nexport function createPersonalizationClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Personalization',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a user profile.  The response includes a date and time when the user profile can safely be considered deleted.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param deleteUserProfile - The deleteUserProfile object.\n     * @param deleteUserProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteUserProfile(\n      { userToken }: DeleteUserProfileProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteUserProfileResponse> {\n      if (!userToken) {\n        throw new Error('Parameter `userToken` is required when calling `deleteUserProfile`.');\n      }\n\n      const requestPath = '/1/profiles/{userToken}'.replace('{userToken}', encodeURIComponent(userToken));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the current personalization strategy.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getPersonalizationStrategy(requestOptions?: RequestOptions | undefined): Promise<PersonalizationStrategyParams> {\n      const requestPath = '/1/strategies/personalization';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a user profile and their affinities for different facets.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param getUserTokenProfile - The getUserTokenProfile object.\n     * @param getUserTokenProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getUserTokenProfile(\n      { userToken }: GetUserTokenProfileProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetUserTokenResponse> {\n      if (!userToken) {\n        throw new Error('Parameter `userToken` is required when calling `getUserTokenProfile`.');\n      }\n\n      const requestPath = '/1/profiles/personalization/{userToken}'.replace(\n        '{userToken}',\n        encodeURIComponent(userToken),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new personalization strategy.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param personalizationStrategyParams - The personalizationStrategyParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setPersonalizationStrategy(\n      personalizationStrategyParams: PersonalizationStrategyParams,\n      requestOptions?: RequestOptions,\n    ): Promise<SetPersonalizationStrategyResponse> {\n      if (!personalizationStrategyParams) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n\n      if (!personalizationStrategyParams.eventsScoring) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.eventsScoring` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n      if (!personalizationStrategyParams.facetsScoring) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.facetsScoring` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n      if (!personalizationStrategyParams.personalizationImpact) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.personalizationImpact` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n\n      const requestPath = '/1/strategies/personalization';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: personalizationStrategyParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";AAIA,SAAS,2BAA2B;AAEpC,SAAS,mBAAmB,iBAAiB,wBAAwB;;;ACIrE,SAAS,YAAY,mBAAmB,uBAAuB;AAiBxD,IAAM,mBAAmB;AAEzB,IAAM,UAAU,CAAC,MAAM,IAAI;AAIlC,SAAS,gBAAgB,QAAwB;AAC/C,QAAM,MAAM,uCAAuC,QAAQ,YAAY,MAAM;AAE7E,SAAO,CAAC,EAAE,KAAK,QAAQ,aAAa,UAAU,QAAQ,CAAC;AACzD;AAEO,SAAS,4BAA4B;AAAA,EAC1C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL,GAAwC;AACtC,QAAM,OAAO,WAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,cAAc,kBAAkB;AAAA,IACpC,OAAO,gBAAgB,YAAY;AAAA,IACnC,GAAG;AAAA,IACH,cAAc,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,kBACE,EAAE,UAAU,GACZ,gBACoC;AACpC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,YAAM,cAAc,0BAA0B,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAClG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,2BAA2B,gBAAqF;AAC9G,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,oBACE,EAAE,UAAU,GACZ,gBAC+B;AAC/B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,YAAM,cAAc,0CAA0C;AAAA,QAC5D;AAAA,QACA,mBAAmB,SAAS;AAAA,MAC9B;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,2BACE,+BACA,gBAC6C;AAC7C,UAAI,CAAC,+BAA+B;AAClC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,8BAA8B,eAAe;AAChD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,8BAA8B,eAAe;AAChD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,8BAA8B,uBAAuB;AACxD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;AD9VO,SAAS,sBACd,OACA,QACA,QACA,SACuB;AACvB,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,MAAI,CAAC,UAAW,WAAW,OAAO,WAAW,YAAY,CAAC,QAAQ,SAAS,MAAM,IAAK;AACpF,UAAM,IAAI,MAAM,4DAA4D,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,EAClG;AAEA,SAAO;AAAA,IACL,GAAG,4BAA4B;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,WAAW,oBAAoB;AAAA,MAC/B,eAAe,CAAC,EAAE,SAAS,WAAW,SAAS,QAAQ,SAAS,KAAK,CAAC;AAAA,MACtE,gBAAgB,gBAAgB;AAAA,MAChC,eAAe,gBAAgB;AAAA,MAC/B,YAAY,kBAAkB;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;", "names": []}