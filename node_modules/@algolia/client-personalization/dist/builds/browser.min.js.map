{"version": 3, "sources": ["../../../requester-browser-xhr/src/createXhrRequester.ts", "../../../client-common/src/cache/createBrowserLocalStorageCache.ts", "../../../client-common/src/cache/createNullCache.ts", "../../../client-common/src/cache/createFallbackableCache.ts", "../../../client-common/src/cache/createMemoryCache.ts", "../../../client-common/src/constants.ts", "../../../client-common/src/createAlgoliaAgent.ts", "../../../client-common/src/createAuth.ts", "../../../client-common/src/createIterablePromise.ts", "../../../client-common/src/getAlgoliaAgent.ts", "../../../client-common/src/logger/createNullLogger.ts", "../../../client-common/src/transporter/createStatefulHost.ts", "../../../client-common/src/transporter/errors.ts", "../../../client-common/src/transporter/helpers.ts", "../../../client-common/src/transporter/responses.ts", "../../../client-common/src/transporter/stackTrace.ts", "../../../client-common/src/transporter/createTransporter.ts", "../../../client-common/src/types/logger.ts", "../../src/personalizationClient.ts", "../../builds/browser.ts"], "sourcesContent": ["import type { EndRequest, Requester, Response } from '@algolia/client-common';\n\ntype Timeout = ReturnType<typeof setTimeout>;\n\nexport function createXhrRequester(): Requester {\n  function send(request: EndRequest): Promise<Response> {\n    return new Promise((resolve) => {\n      const baseRequester = new XMLHttpRequest();\n      baseRequester.open(request.method, request.url, true);\n\n      Object.keys(request.headers).forEach((key) => baseRequester.setRequestHeader(key, request.headers[key]));\n\n      const createTimeout = (timeout: number, content: string): Timeout => {\n        return setTimeout(() => {\n          baseRequester.abort();\n\n          resolve({\n            status: 0,\n            content,\n            isTimedOut: true,\n          });\n        }, timeout);\n      };\n\n      const connectTimeout = createTimeout(request.connectTimeout, 'Connection timeout');\n\n      let responseTimeout: Timeout | undefined;\n\n      baseRequester.onreadystatechange = (): void => {\n        if (baseRequester.readyState > baseRequester.OPENED && responseTimeout === undefined) {\n          clearTimeout(connectTimeout);\n\n          responseTimeout = createTimeout(request.responseTimeout, 'Socket timeout');\n        }\n      };\n\n      baseRequester.onerror = (): void => {\n        // istanbul ignore next\n        if (baseRequester.status === 0) {\n          clearTimeout(connectTimeout);\n          clearTimeout(responseTimeout!);\n\n          resolve({\n            content: baseRequester.responseText || 'Network request failed',\n            status: baseRequester.status,\n            isTimedOut: false,\n          });\n        }\n      };\n\n      baseRequester.onload = (): void => {\n        clearTimeout(connectTimeout);\n        clearTimeout(responseTimeout!);\n\n        resolve({\n          content: baseRequester.responseText,\n          status: baseRequester.status,\n          isTimedOut: false,\n        });\n      };\n\n      baseRequester.send(request.data);\n    });\n  }\n\n  return { send };\n}\n", "import type { BrowserLocalStorageCacheItem, BrowserLocalStorageOptions, Cache, CacheEvents } from '../types';\n\nexport function createBrowserLocalStorageCache(options: BrowserLocalStorageOptions): Cache {\n  let storage: Storage;\n  // We've changed the namespace to avoid conflicts with v4, as this version is a huge breaking change\n  const namespaceKey = `algolia-client-js-${options.key}`;\n\n  function getStorage(): Storage {\n    if (storage === undefined) {\n      storage = options.localStorage || window.localStorage;\n    }\n\n    return storage;\n  }\n\n  function getNamespace<TValue>(): Record<string, TValue> {\n    return JSON.parse(getStorage().getItem(namespaceKey) || '{}');\n  }\n\n  function setNamespace(namespace: Record<string, any>): void {\n    getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n  }\n\n  function removeOutdatedCacheItems(): void {\n    const timeToLive = options.timeToLive ? options.timeToLive * 1000 : null;\n    const namespace = getNamespace<BrowserLocalStorageCacheItem>();\n\n    const filteredNamespaceWithoutOldFormattedCacheItems = Object.fromEntries(\n      Object.entries(namespace).filter(([, cacheItem]) => {\n        return cacheItem.timestamp !== undefined;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutOldFormattedCacheItems);\n\n    if (!timeToLive) {\n      return;\n    }\n\n    const filteredNamespaceWithoutExpiredItems = Object.fromEntries(\n      Object.entries(filteredNamespaceWithoutOldFormattedCacheItems).filter(([, cacheItem]) => {\n        const currentTimestamp = new Date().getTime();\n        const isExpired = cacheItem.timestamp + timeToLive < currentTimestamp;\n\n        return !isExpired;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutExpiredItems);\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: () => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return Promise.resolve()\n        .then(() => {\n          removeOutdatedCacheItems();\n\n          return getNamespace<Promise<BrowserLocalStorageCacheItem>>()[JSON.stringify(key)];\n        })\n        .then((value) => {\n          return Promise.all([value ? value.value : defaultValue(), value !== undefined]);\n        })\n        .then(([value, exists]) => {\n          return Promise.all([value, exists || events.miss(value)]);\n        })\n        .then(([value]) => value);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        namespace[JSON.stringify(key)] = {\n          timestamp: new Date().getTime(),\n          value,\n        };\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n\n        return value;\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        delete namespace[JSON.stringify(key)];\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n      });\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve().then(() => {\n        getStorage().removeItem(namespaceKey);\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents } from '../types';\n\nexport function createNullCache(): Cache {\n  return {\n    get<TValue>(\n      _key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const value = defaultValue();\n\n      return value.then((result) => Promise.all([result, events.miss(result)])).then(([result]) => result);\n    },\n\n    set<TValue>(_key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve(value);\n    },\n\n    delete(_key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Cache, CacheEvents, FallbackableCacheOptions } from '../types';\nimport { createNullCache } from './createNullCache';\n\nexport function createFallbackableCache(options: FallbackableCacheOptions): Cache {\n  const caches = [...options.caches];\n  const current = caches.shift();\n\n  if (current === undefined) {\n    return createNullCache();\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return current.get(key, defaultValue, events).catch(() => {\n        return createFallbackableCache({ caches }).get(key, defaultValue, events);\n      });\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return current.set(key, value).catch(() => {\n        return createFallbackableCache({ caches }).set(key, value);\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return current.delete(key).catch(() => {\n        return createFallbackableCache({ caches }).delete(key);\n      });\n    },\n\n    clear(): Promise<void> {\n      return current.clear().catch(() => {\n        return createFallbackableCache({ caches }).clear();\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents, MemoryCacheOptions } from '../types';\n\nexport function createMemoryCache(options: MemoryCacheOptions = { serializable: true }): Cache {\n  let cache: Record<string, any> = {};\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const keyAsString = JSON.stringify(key);\n\n      if (keyAsString in cache) {\n        return Promise.resolve(options.serializable ? JSON.parse(cache[keyAsString]) : cache[keyAsString]);\n      }\n\n      const promise = defaultValue();\n\n      return promise.then((value: TValue) => events.miss(value)).then(() => promise);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      cache[JSON.stringify(key)] = options.serializable ? JSON.stringify(value) : value;\n\n      return Promise.resolve(value);\n    },\n\n    delete(key: Record<string, unknown> | string): Promise<void> {\n      delete cache[JSON.stringify(key)];\n\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      cache = {};\n\n      return Promise.resolve();\n    },\n  };\n}\n", "export const DEFAULT_CONNECT_TIMEOUT_BROWSER = 1000;\nexport const DEFAULT_READ_TIMEOUT_BROWSER = 2000;\nexport const DEFAULT_WRITE_TIMEOUT_BROWSER = 30000;\n\nexport const DEFAULT_CONNECT_TIMEOUT_NODE = 2000;\nexport const DEFAULT_READ_TIMEOUT_NODE = 5000;\nexport const DEFAULT_WRITE_TIMEOUT_NODE = 30000;\n", "import type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport function createAlgoliaAgent(version: string): AlgoliaAgent {\n  const algoliaAgent = {\n    value: `Algolia for JavaScript (${version})`,\n    add(options: AlgoliaAgentOptions): AlgoliaAgent {\n      const addedAlgoliaAgent = `; ${options.segment}${options.version !== undefined ? ` (${options.version})` : ''}`;\n\n      if (algoliaAgent.value.indexOf(addedAlgoliaAgent) === -1) {\n        algoliaAgent.value = `${algoliaAgent.value}${addedAlgoliaAgent}`;\n      }\n\n      return algoliaAgent;\n    },\n  };\n\n  return algoliaAgent;\n}\n", "import type { AuthMode, Headers, QueryParameters } from './types';\n\nexport function createAuth(\n  appId: string,\n  apiKey: string,\n  authMode: AuthMode = 'WithinHeaders',\n): {\n  readonly headers: () => Headers;\n  readonly queryParameters: () => QueryParameters;\n} {\n  const credentials = {\n    'x-algolia-api-key': apiKey,\n    'x-algolia-application-id': appId,\n  };\n\n  return {\n    headers(): Headers {\n      return authMode === 'WithinHeaders' ? credentials : {};\n    },\n\n    queryParameters(): QueryParameters {\n      return authMode === 'WithinQueryParameters' ? credentials : {};\n    },\n  };\n}\n", "import type { CreateIterablePromise } from './types/createIterablePromise';\n\n/**\n * Helper: Returns the promise of a given `func` to iterate on, based on a given `validate` condition.\n *\n * @param createIterator - The createIterator options.\n * @param createIterator.func - The function to run, which returns a promise.\n * @param createIterator.validate - The validator function. It receives the resolved return of `func`.\n * @param createIterator.aggregator - The function that runs right after the `func` method has been executed, allows you to do anything with the response before `validate`.\n * @param createIterator.error - The `validate` condition to throw an error, and its message.\n * @param createIterator.timeout - The function to decide how long to wait between iterations.\n */\nexport function createIterablePromise<TResponse>({\n  func,\n  validate,\n  aggregator,\n  error,\n  timeout = (): number => 0,\n}: CreateIterablePromise<TResponse>): Promise<TResponse> {\n  const retry = (previousResponse?: TResponse | undefined): Promise<TResponse> => {\n    return new Promise<TResponse>((resolve, reject) => {\n      func(previousResponse)\n        .then(async (response) => {\n          if (aggregator) {\n            await aggregator(response);\n          }\n\n          if (await validate(response)) {\n            return resolve(response);\n          }\n\n          if (error && (await error.validate(response))) {\n            return reject(new Error(await error.message(response)));\n          }\n\n          return setTimeout(\n            () => {\n              retry(response).then(resolve).catch(reject);\n            },\n            await timeout(),\n          );\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  };\n\n  return retry();\n}\n", "import { createAlgoliaAgent } from './createAlgoliaAgent';\nimport type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport type GetAlgoliaAgent = {\n  algoliaAgents: AlgoliaAgentOptions[];\n  client: string;\n  version: string;\n};\n\nexport function getAlgoliaAgent({ algoliaAgents, client, version }: GetAlgoliaAgent): AlgoliaAgent {\n  const defaultAlgoliaAgent = createAlgoliaAgent(version).add({\n    segment: client,\n    version,\n  });\n\n  algoliaAgents.forEach((algoliaAgent) => defaultAlgoliaAgent.add(algoliaAgent));\n\n  return defaultAlgoliaAgent;\n}\n", "import type { Logger } from '../types/logger';\n\nexport function createNullLogger(): Logger {\n  return {\n    debug(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    info(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    error(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Host, StatefulHost } from '../types';\n\n// By default, API Clients at Algolia have expiration delay of 5 mins.\n// In the JavaScript client, we have 2 mins.\nconst EXPIRATION_DELAY = 2 * 60 * 1000;\n\nexport function createStatefulHost(host: Host, status: StatefulHost['status'] = 'up'): StatefulHost {\n  const lastUpdate = Date.now();\n\n  function isUp(): boolean {\n    return status === 'up' || Date.now() - lastUpdate > EXPIRATION_DELAY;\n  }\n\n  function isTimedOut(): boolean {\n    return status === 'timed out' && Date.now() - lastUpdate <= EXPIRATION_DELAY;\n  }\n\n  return { ...host, status, lastUpdate, isUp, isTimedOut };\n}\n", "import type { Response, StackFrame } from '../types';\n\nexport class AlgoliaError extends Error {\n  override name: string = 'AlgoliaError';\n\n  constructor(message: string, name: string) {\n    super(message);\n\n    if (name) {\n      this.name = name;\n    }\n  }\n}\n\nexport class IndexNotFoundError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} does not exist`, 'IndexNotFoundError');\n  }\n}\n\nexport class IndicesInSameAppError extends AlgoliaError {\n  constructor() {\n    super('Indices are in the same application. Use operationIndex instead.', 'IndicesInSameAppError');\n  }\n}\n\nexport class IndexAlreadyExistsError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} index already exists.`, 'IndexAlreadyExistsError');\n  }\n}\n\nexport class ErrorWithStackTrace extends AlgoliaError {\n  stackTrace: StackFrame[];\n\n  constructor(message: string, stackTrace: StackFrame[], name: string) {\n    super(message, name);\n    // the array and object should be frozen to reflect the stackTrace at the time of the error\n    this.stackTrace = stackTrace;\n  }\n}\n\nexport class RetryError extends ErrorWithStackTrace {\n  constructor(stackTrace: StackFrame[]) {\n    super(\n      'Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.',\n      stackTrace,\n      'RetryError',\n    );\n  }\n}\n\nexport class ApiError extends ErrorWithStackTrace {\n  status: number;\n\n  constructor(message: string, status: number, stackTrace: StackFrame[], name = 'ApiError') {\n    super(message, stackTrace, name);\n    this.status = status;\n  }\n}\n\nexport class DeserializationError extends AlgoliaError {\n  response: Response;\n\n  constructor(message: string, response: Response) {\n    super(message, 'DeserializationError');\n    this.response = response;\n  }\n}\n\nexport type DetailedErrorWithMessage = {\n  message: string;\n  label: string;\n};\n\nexport type DetailedErrorWithTypeID = {\n  id: string;\n  type: string;\n  name?: string | undefined;\n};\n\nexport type DetailedError = {\n  code: string;\n  details?: DetailedErrorWithMessage[] | DetailedErrorWithTypeID[] | undefined;\n};\n\n// DetailedApiError is only used by the ingestion client to return more informative error, other clients will use ApiClient.\nexport class DetailedApiError extends ApiError {\n  error: DetailedError;\n\n  constructor(message: string, status: number, error: DetailedError, stackTrace: StackFrame[]) {\n    super(message, status, stackTrace, 'DetailedApiError');\n    this.error = error;\n  }\n}\n", "import type { Headers, Host, QueryParameters, Request, RequestOptions, Response, StackFrame } from '../types';\nimport { ApiError, DeserializationError, DetailedApiError } from './errors';\n\nexport function shuffle<TData>(array: TData[]): TData[] {\n  const shuffledArray = array;\n\n  for (let c = array.length - 1; c > 0; c--) {\n    const b = Math.floor(Math.random() * (c + 1));\n    const a = array[c];\n\n    shuffledArray[c] = array[b];\n    shuffledArray[b] = a;\n  }\n\n  return shuffledArray;\n}\n\nexport function serializeUrl(host: Host, path: string, queryParameters: QueryParameters): string {\n  const queryParametersAsString = serializeQueryParameters(queryParameters);\n  let url = `${host.protocol}://${host.url}${host.port ? `:${host.port}` : ''}/${\n    path.charAt(0) === '/' ? path.substring(1) : path\n  }`;\n\n  if (queryParametersAsString.length) {\n    url += `?${queryParametersAsString}`;\n  }\n\n  return url;\n}\n\nexport function serializeQueryParameters(parameters: QueryParameters): string {\n  return Object.keys(parameters)\n    .filter((key) => parameters[key] !== undefined)\n    .sort()\n    .map(\n      (key) =>\n        `${key}=${encodeURIComponent(\n          Object.prototype.toString.call(parameters[key]) === '[object Array]'\n            ? parameters[key].join(',')\n            : parameters[key],\n        ).replace(/\\+/g, '%20')}`,\n    )\n    .join('&');\n}\n\nexport function serializeData(request: Request, requestOptions: RequestOptions): string | undefined {\n  if (request.method === 'GET' || (request.data === undefined && requestOptions.data === undefined)) {\n    return undefined;\n  }\n\n  const data = Array.isArray(request.data) ? request.data : { ...request.data, ...requestOptions.data };\n\n  return JSON.stringify(data);\n}\n\nexport function serializeHeaders(\n  baseHeaders: Headers,\n  requestHeaders: Headers,\n  requestOptionsHeaders?: Headers | undefined,\n): Headers {\n  const headers: Headers = {\n    Accept: 'application/json',\n    ...baseHeaders,\n    ...requestHeaders,\n    ...requestOptionsHeaders,\n  };\n  const serializedHeaders: Headers = {};\n\n  Object.keys(headers).forEach((header) => {\n    const value = headers[header];\n    serializedHeaders[header.toLowerCase()] = value;\n  });\n\n  return serializedHeaders;\n}\n\nexport function deserializeSuccess<TObject>(response: Response): TObject {\n  try {\n    return JSON.parse(response.content);\n  } catch (e) {\n    throw new DeserializationError((e as Error).message, response);\n  }\n}\n\nexport function deserializeFailure({ content, status }: Response, stackFrame: StackFrame[]): Error {\n  try {\n    const parsed = JSON.parse(content);\n    if ('error' in parsed) {\n      return new DetailedApiError(parsed.message, status, parsed.error, stackFrame);\n    }\n    return new ApiError(parsed.message, status, stackFrame);\n  } catch {\n    // ..\n  }\n  return new ApiError(content, status, stackFrame);\n}\n", "import type { Response } from '../types';\n\nexport function isNetworkError({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return !isTimedOut && ~~status === 0;\n}\n\nexport function isRetryable({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return isTimedOut || isNetworkError({ isTimedOut, status }) || (~~(status / 100) !== 2 && ~~(status / 100) !== 4);\n}\n\nexport function isSuccess({ status }: Pick<Response, 'status'>): boolean {\n  return ~~(status / 100) === 2;\n}\n", "import type { Head<PERSON>, StackFrame } from '../types';\n\nexport function stackTraceWithoutCredentials(stackTrace: StackFrame[]): StackFrame[] {\n  return stackTrace.map((stackFrame) => stackFrameWithoutCredentials(stackFrame));\n}\n\nexport function stackFrameWithoutCredentials(stackFrame: StackFrame): StackFrame {\n  const modifiedHeaders: Headers = stackFrame.request.headers['x-algolia-api-key']\n    ? { 'x-algolia-api-key': '*****' }\n    : {};\n\n  return {\n    ...stackFrame,\n    request: {\n      ...stackFrame.request,\n      headers: {\n        ...stackFrame.request.headers,\n        ...modifiedHeaders,\n      },\n    },\n  };\n}\n", "import type {\n  EndRequest,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n  Response,\n  StackFrame,\n  Transporter,\n  TransporterOptions,\n} from '../types';\nimport { createStatefulHost } from './createStatefulHost';\nimport { RetryError } from './errors';\nimport { deserializeFailure, deserializeSuccess, serializeData, serializeHeaders, serializeUrl } from './helpers';\nimport { isRetryable, isSuccess } from './responses';\nimport { stackFrameWithoutCredentials, stackTraceWithoutCredentials } from './stackTrace';\n\ntype RetryableOptions = {\n  hosts: Host[];\n  getTimeout: (retryCount: number, timeout: number) => number;\n};\n\nexport function createTransporter({\n  hosts,\n  hostsCache,\n  baseHeaders,\n  logger,\n  baseQueryParameters,\n  algoliaAgent,\n  timeouts,\n  requester,\n  requestsCache,\n  responsesCache,\n}: TransporterOptions): Transporter {\n  async function createRetryableOptions(compatibleHosts: Host[]): Promise<RetryableOptions> {\n    const statefulHosts = await Promise.all(\n      compatibleHosts.map((compatibleHost) => {\n        return hostsCache.get(compatibleHost, () => {\n          return Promise.resolve(createStatefulHost(compatibleHost));\n        });\n      }),\n    );\n    const hostsUp = statefulHosts.filter((host) => host.isUp());\n    const hostsTimedOut = statefulHosts.filter((host) => host.isTimedOut());\n\n    // Note, we put the hosts that previously timed out on the end of the list.\n    const hostsAvailable = [...hostsUp, ...hostsTimedOut];\n    const compatibleHostsAvailable = hostsAvailable.length > 0 ? hostsAvailable : compatibleHosts;\n\n    return {\n      hosts: compatibleHostsAvailable,\n      getTimeout(timeoutsCount: number, baseTimeout: number): number {\n        /**\n         * Imagine that you have 4 hosts, if timeouts will increase\n         * on the following way: 1 (timed out) > 4 (timed out) > 5 (200).\n         *\n         * Note that, the very next request, we start from the previous timeout.\n         *\n         *  5 (timed out) > 6 (timed out) > 7 ...\n         *\n         * This strategy may need to be reviewed, but is the strategy on the our\n         * current v3 version.\n         */\n        const timeoutMultiplier =\n          hostsTimedOut.length === 0 && timeoutsCount === 0 ? 1 : hostsTimedOut.length + 3 + timeoutsCount;\n\n        return timeoutMultiplier * baseTimeout;\n      },\n    };\n  }\n\n  async function retryableRequest<TResponse>(\n    request: Request,\n    requestOptions: RequestOptions,\n    isRead = true,\n  ): Promise<TResponse> {\n    const stackTrace: StackFrame[] = [];\n\n    /**\n     * First we prepare the payload that do not depend from hosts.\n     */\n    const data = serializeData(request, requestOptions);\n    const headers = serializeHeaders(baseHeaders, request.headers, requestOptions.headers);\n\n    // On `GET`, the data is proxied to query parameters.\n    const dataQueryParameters: QueryParameters =\n      request.method === 'GET'\n        ? {\n            ...request.data,\n            ...requestOptions.data,\n          }\n        : {};\n\n    const queryParameters: QueryParameters = {\n      ...baseQueryParameters,\n      ...request.queryParameters,\n      ...dataQueryParameters,\n    };\n\n    if (algoliaAgent.value) {\n      queryParameters['x-algolia-agent'] = algoliaAgent.value;\n    }\n\n    if (requestOptions && requestOptions.queryParameters) {\n      for (const key of Object.keys(requestOptions.queryParameters)) {\n        // We want to keep `undefined` and `null` values,\n        // but also avoid stringifying `object`s, as they are\n        // handled in the `serializeUrl` step right after.\n        if (\n          !requestOptions.queryParameters[key] ||\n          Object.prototype.toString.call(requestOptions.queryParameters[key]) === '[object Object]'\n        ) {\n          queryParameters[key] = requestOptions.queryParameters[key];\n        } else {\n          queryParameters[key] = requestOptions.queryParameters[key].toString();\n        }\n      }\n    }\n\n    let timeoutsCount = 0;\n\n    const retry = async (\n      retryableHosts: Host[],\n      getTimeout: (timeoutsCount: number, timeout: number) => number,\n    ): Promise<TResponse> => {\n      /**\n       * We iterate on each host, until there is no host left.\n       */\n      const host = retryableHosts.pop();\n      if (host === undefined) {\n        throw new RetryError(stackTraceWithoutCredentials(stackTrace));\n      }\n\n      const timeout = { ...timeouts, ...requestOptions.timeouts };\n\n      const payload: EndRequest = {\n        data,\n        headers,\n        method: request.method,\n        url: serializeUrl(host, request.path, queryParameters),\n        connectTimeout: getTimeout(timeoutsCount, timeout.connect),\n        responseTimeout: getTimeout(timeoutsCount, isRead ? timeout.read : timeout.write),\n      };\n\n      /**\n       * The stackFrame is pushed to the stackTrace so we\n       * can have information about onRetry and onFailure\n       * decisions.\n       */\n      const pushToStackTrace = (response: Response): StackFrame => {\n        const stackFrame: StackFrame = {\n          request: payload,\n          response,\n          host,\n          triesLeft: retryableHosts.length,\n        };\n\n        stackTrace.push(stackFrame);\n\n        return stackFrame;\n      };\n\n      const response = await requester.send(payload);\n\n      if (isRetryable(response)) {\n        const stackFrame = pushToStackTrace(response);\n\n        // If response is a timeout, we increase the number of timeouts so we can increase the timeout later.\n        if (response.isTimedOut) {\n          timeoutsCount++;\n        }\n        /**\n         * Failures are individually sent to the logger, allowing\n         * the end user to debug / store stack frames even\n         * when a retry error does not happen.\n         */\n        logger.info('Retryable failure', stackFrameWithoutCredentials(stackFrame));\n\n        /**\n         * We also store the state of the host in failure cases. If the host, is\n         * down it will remain down for the next 2 minutes. In a timeout situation,\n         * this host will be added end of the list of hosts on the next request.\n         */\n        await hostsCache.set(host, createStatefulHost(host, response.isTimedOut ? 'timed out' : 'down'));\n\n        return retry(retryableHosts, getTimeout);\n      }\n\n      if (isSuccess(response)) {\n        return deserializeSuccess(response);\n      }\n\n      pushToStackTrace(response);\n      throw deserializeFailure(response, stackTrace);\n    };\n\n    /**\n     * Finally, for each retryable host perform request until we got a non\n     * retryable response. Some notes here:\n     *\n     * 1. The reverse here is applied so we can apply a `pop` later on => more performant.\n     * 2. We also get from the retryable options a timeout multiplier that is tailored\n     * for the current context.\n     */\n    const compatibleHosts = hosts.filter(\n      (host) => host.accept === 'readWrite' || (isRead ? host.accept === 'read' : host.accept === 'write'),\n    );\n    const options = await createRetryableOptions(compatibleHosts);\n\n    return retry([...options.hosts].reverse(), options.getTimeout);\n  }\n\n  function createRequest<TResponse>(request: Request, requestOptions: RequestOptions = {}): Promise<TResponse> {\n    /**\n     * A read request is either a `GET` request, or a request that we make\n     * via the `read` transporter (e.g. `search`).\n     */\n    const isRead = request.useReadTransporter || request.method === 'GET';\n    if (!isRead) {\n      /**\n       * On write requests, no cache mechanisms are applied, and we\n       * proxy the request immediately to the requester.\n       */\n      return retryableRequest<TResponse>(request, requestOptions, isRead);\n    }\n\n    const createRetryableRequest = (): Promise<TResponse> => {\n      /**\n       * Then, we prepare a function factory that contains the construction of\n       * the retryable request. At this point, we may *not* perform the actual\n       * request. But we want to have the function factory ready.\n       */\n      return retryableRequest<TResponse>(request, requestOptions);\n    };\n\n    /**\n     * Once we have the function factory ready, we need to determine of the\n     * request is \"cacheable\" - should be cached. Note that, once again,\n     * the user can force this option.\n     */\n    const cacheable = requestOptions.cacheable || request.cacheable;\n\n    /**\n     * If is not \"cacheable\", we immediately trigger the retryable request, no\n     * need to check cache implementations.\n     */\n    if (cacheable !== true) {\n      return createRetryableRequest();\n    }\n\n    /**\n     * If the request is \"cacheable\", we need to first compute the key to ask\n     * the cache implementations if this request is on progress or if the\n     * response already exists on the cache.\n     */\n    const key = {\n      request,\n      requestOptions,\n      transporter: {\n        queryParameters: baseQueryParameters,\n        headers: baseHeaders,\n      },\n    };\n\n    /**\n     * With the computed key, we first ask the responses cache\n     * implementation if this request was been resolved before.\n     */\n    return responsesCache.get(\n      key,\n      () => {\n        /**\n         * If the request has never resolved before, we actually ask if there\n         * is a current request with the same key on progress.\n         */\n        return requestsCache.get(key, () =>\n          /**\n           * Finally, if there is no request in progress with the same key,\n           * this `createRetryableRequest()` will actually trigger the\n           * retryable request.\n           */\n          requestsCache\n            .set(key, createRetryableRequest())\n            .then(\n              (response) => Promise.all([requestsCache.delete(key), response]),\n              (err) => Promise.all([requestsCache.delete(key), Promise.reject(err)]),\n            )\n            .then(([_, response]) => response),\n        );\n      },\n      {\n        /**\n         * Of course, once we get this response back from the server, we\n         * tell response cache to actually store the received response\n         * to be used later.\n         */\n        miss: (response) => responsesCache.set(key, response),\n      },\n    );\n  }\n\n  return {\n    hostsCache,\n    requester,\n    timeouts,\n    logger,\n    algoliaAgent,\n    baseHeaders,\n    baseQueryParameters,\n    hosts,\n    request: createRequest,\n    requestsCache,\n    responsesCache,\n  };\n}\n", "export const LogLevelEnum: Readonly<Record<string, LogLevelType>> = {\n  Debug: 1,\n  Info: 2,\n  Error: 3,\n};\n\nexport type LogLevelType = 1 | 2 | 3;\n\nexport type Logger = {\n  /**\n   * Logs debug messages.\n   */\n  debug: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs info messages.\n   */\n  info: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs error messages.\n   */\n  error: (message: string, args?: any | undefined) => Promise<void>;\n};\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { DeleteUserProfileResponse } from '../model/deleteUserProfileResponse';\n\nimport type { GetUserTokenResponse } from '../model/getUserTokenResponse';\nimport type { PersonalizationStrategyParams } from '../model/personalizationStrategyParams';\nimport type { SetPersonalizationStrategyResponse } from '../model/setPersonalizationStrategyResponse';\n\nimport type {\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteUserProfileProps,\n  GetUserTokenProfileProps,\n} from '../model/clientMethodProps';\n\nexport const apiClientVersion = '5.30.0';\n\nexport const REGIONS = ['eu', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region: Region };\n\nfunction getDefaultHosts(region: Region): Host[] {\n  const url = 'personalization.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\nexport function createPersonalizationClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Personalization',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a user profile.  The response includes a date and time when the user profile can safely be considered deleted.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param deleteUserProfile - The deleteUserProfile object.\n     * @param deleteUserProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteUserProfile(\n      { userToken }: DeleteUserProfileProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteUserProfileResponse> {\n      if (!userToken) {\n        throw new Error('Parameter `userToken` is required when calling `deleteUserProfile`.');\n      }\n\n      const requestPath = '/1/profiles/{userToken}'.replace('{userToken}', encodeURIComponent(userToken));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the current personalization strategy.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getPersonalizationStrategy(requestOptions?: RequestOptions | undefined): Promise<PersonalizationStrategyParams> {\n      const requestPath = '/1/strategies/personalization';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a user profile and their affinities for different facets.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param getUserTokenProfile - The getUserTokenProfile object.\n     * @param getUserTokenProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getUserTokenProfile(\n      { userToken }: GetUserTokenProfileProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetUserTokenResponse> {\n      if (!userToken) {\n        throw new Error('Parameter `userToken` is required when calling `getUserTokenProfile`.');\n      }\n\n      const requestPath = '/1/profiles/personalization/{userToken}'.replace(\n        '{userToken}',\n        encodeURIComponent(userToken),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new personalization strategy.\n     *\n     * Required API Key ACLs:\n     *  - recommendation\n     * @param personalizationStrategyParams - The personalizationStrategyParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setPersonalizationStrategy(\n      personalizationStrategyParams: PersonalizationStrategyParams,\n      requestOptions?: RequestOptions,\n    ): Promise<SetPersonalizationStrategyResponse> {\n      if (!personalizationStrategyParams) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n\n      if (!personalizationStrategyParams.eventsScoring) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.eventsScoring` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n      if (!personalizationStrategyParams.facetsScoring) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.facetsScoring` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n      if (!personalizationStrategyParams.personalizationImpact) {\n        throw new Error(\n          'Parameter `personalizationStrategyParams.personalizationImpact` is required when calling `setPersonalizationStrategy`.',\n        );\n      }\n\n      const requestPath = '/1/strategies/personalization';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: personalizationStrategyParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport { createXhrRequester } from '@algolia/requester-browser-xhr';\n\nimport {\n  createBrowserLocalStorageCache,\n  createF<PERSON>backableCache,\n  createMemoryCache,\n  createNullLogger,\n} from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { apiClientVersion, createPersonalizationClient } from '../src/personalizationClient';\n\nimport type { Region } from '../src/personalizationClient';\nimport { REGIONS } from '../src/personalizationClient';\n\nexport type { Region, RegionOptions } from '../src/personalizationClient';\n\nexport { apiClientVersion } from '../src/personalizationClient';\n\nexport * from '../model';\n\nexport function personalizationClient(\n  appId: string,\n  apiKey: string,\n  region: Region,\n  options?: ClientOptions | undefined,\n): PersonalizationClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (!region || (region && (typeof region !== 'string' || !REGIONS.includes(region)))) {\n    throw new Error(`\\`region\\` is required and must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return createPersonalizationClient({\n    appId,\n    apiKey,\n    region,\n    timeouts: {\n      connect: 1000,\n      read: 2000,\n      write: 30000,\n    },\n    logger: createNullLogger(),\n    requester: createXhrRequester(),\n    algoliaAgents: [{ segment: 'Browser' }],\n    authMode: 'WithinQueryParameters',\n    responsesCache: createMemoryCache(),\n    requestsCache: createMemoryCache({ serializable: false }),\n    hostsCache: createFallbackableCache({\n      caches: [createBrowserLocalStorageCache({ key: `${apiClientVersion}-${appId}` }), createMemoryCache()],\n    }),\n    ...options,\n  });\n}\n\nexport type PersonalizationClient = ReturnType<typeof createPersonalizationClient>;\n"], "mappings": "AAIO,SAASA,GAAgC,CAC9C,SAASC,EAAKC,EAAwC,CACpD,OAAO,IAAI,QAASC,GAAY,CAC9B,IAAMC,EAAgB,IAAI,eAC1BA,EAAc,KAAKF,EAAQ,OAAQA,EAAQ,IAAK,EAAI,EAEpD,OAAO,KAAKA,EAAQ,OAAO,EAAE,QAASG,GAAQD,EAAc,iBAAiBC,EAAKH,EAAQ,QAAQG,CAAG,CAAC,CAAC,EAEvG,IAAMC,EAAgB,CAACC,EAAiBC,IAC/B,WAAW,IAAM,CACtBJ,EAAc,MAAM,EAEpBD,EAAQ,CACN,OAAQ,EACR,QAAAK,EACA,WAAY,EACd,CAAC,CACH,EAAGD,CAAO,EAGNE,EAAiBH,EAAcJ,EAAQ,eAAgB,oBAAoB,EAE7EQ,EAEJN,EAAc,mBAAqB,IAAY,CACzCA,EAAc,WAAaA,EAAc,QAAUM,IAAoB,SACzE,aAAaD,CAAc,EAE3BC,EAAkBJ,EAAcJ,EAAQ,gBAAiB,gBAAgB,EAE7E,EAEAE,EAAc,QAAU,IAAY,CAE9BA,EAAc,SAAW,IAC3B,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,cAAgB,yBACvC,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,EAEL,EAEAA,EAAc,OAAS,IAAY,CACjC,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,aACvB,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,CACH,EAEAA,EAAc,KAAKF,EAAQ,IAAI,CACjC,CAAC,CACH,CAEA,MAAO,CAAE,KAAAD,CAAK,CAChB,CChEO,SAASU,EAA+BC,EAA4C,CACzF,IAAIC,EAEEC,EAAe,qBAAqBF,EAAQ,GAAG,GAErD,SAASG,GAAsB,CAC7B,OAAIF,IAAY,SACdA,EAAUD,EAAQ,cAAgB,OAAO,cAGpCC,CACT,CAEA,SAASG,GAA+C,CACtD,OAAO,KAAK,MAAMD,EAAW,EAAE,QAAQD,CAAY,GAAK,IAAI,CAC9D,CAEA,SAASG,EAAaC,EAAsC,CAC1DH,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAEA,SAASC,GAAiC,CACxC,IAAMC,EAAaR,EAAQ,WAAaA,EAAQ,WAAa,IAAO,KAC9DM,EAAYF,EAA2C,EAEvDK,EAAiD,OAAO,YAC5D,OAAO,QAAQH,CAAS,EAAE,OAAO,CAAC,CAAC,CAAEI,CAAS,IACrCA,EAAU,YAAc,MAChC,CACH,EAIA,GAFAL,EAAaI,CAA8C,EAEvD,CAACD,EACH,OAGF,IAAMG,EAAuC,OAAO,YAClD,OAAO,QAAQF,CAA8C,EAAE,OAAO,CAAC,CAAC,CAAEC,CAAS,IAAM,CACvF,IAAME,EAAmB,IAAI,KAAK,EAAE,QAAQ,EAG5C,MAAO,EAFWF,EAAU,UAAYF,EAAaI,EAGvD,CAAC,CACH,EAEAP,EAAaM,CAAoC,CACnD,CAEA,MAAO,CACL,IACEE,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAM,QAAQ,QAAQ,CAC9B,EACiB,CACjB,OAAO,QAAQ,QAAQ,EACpB,KAAK,KACJR,EAAyB,EAElBH,EAAoD,EAAE,KAAK,UAAUS,CAAG,CAAC,EACjF,EACA,KAAMG,GACE,QAAQ,IAAI,CAACA,EAAQA,EAAM,MAAQF,EAAa,EAAGE,IAAU,MAAS,CAAC,CAC/E,EACA,KAAK,CAAC,CAACA,EAAOC,CAAM,IACZ,QAAQ,IAAI,CAACD,EAAOC,GAAUF,EAAO,KAAKC,CAAK,CAAC,CAAC,CACzD,EACA,KAAK,CAAC,CAACA,CAAK,IAAMA,CAAK,CAC5B,EAEA,IAAYH,EAAmCG,EAAgC,CAC7E,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMV,EAAYF,EAAa,EAE/B,OAAAE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAAI,CAC/B,UAAW,IAAI,KAAK,EAAE,QAAQ,EAC9B,MAAAG,CACF,EAEAb,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,EAErDU,CACT,CAAC,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMP,EAAYF,EAAa,EAE/B,OAAOE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAEpCV,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAAC,CACH,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClCH,EAAW,EAAE,WAAWD,CAAY,CACtC,CAAC,CACH,CACF,CACF,CCvGO,SAASgB,GAAyB,CACvC,MAAO,CACL,IACEC,EACAL,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CAGjB,OAFcD,EAAa,EAEd,KAAMM,GAAW,QAAQ,IAAI,CAACA,EAAQL,EAAO,KAAKK,CAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,CAAM,IAAMA,CAAM,CACrG,EAEA,IAAYD,EAAoCH,EAAgC,CAC9E,OAAO,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOG,EAAmD,CACxD,OAAO,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCzBO,SAASE,EAAwBrB,EAA0C,CAChF,IAAMsB,EAAS,CAAC,GAAGtB,EAAQ,MAAM,EAC3BuB,EAAUD,EAAO,MAAM,EAE7B,OAAIC,IAAY,OACPL,EAAgB,EAGlB,CACL,IACEL,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,OAAOQ,EAAQ,IAAIV,EAAKC,EAAcC,CAAM,EAAE,MAAM,IAC3CM,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKC,EAAcC,CAAM,CACzE,CACH,EAEA,IAAYF,EAAmCG,EAAgC,CAC7E,OAAOO,EAAQ,IAAIV,EAAKG,CAAK,EAAE,MAAM,IAC5BK,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKG,CAAK,CAC1D,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAOU,EAAQ,OAAOV,CAAG,EAAE,MAAM,IACxBQ,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,OAAOT,CAAG,CACtD,CACH,EAEA,OAAuB,CACrB,OAAOU,EAAQ,MAAM,EAAE,MAAM,IACpBF,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,MAAM,CAClD,CACH,CACF,CACF,CCxCO,SAASE,EAAkBxB,EAA8B,CAAE,aAAc,EAAK,EAAU,CAC7F,IAAIyB,EAA6B,CAAC,EAElC,MAAO,CACL,IACEZ,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,IAAMW,EAAc,KAAK,UAAUb,CAAG,EAEtC,GAAIa,KAAeD,EACjB,OAAO,QAAQ,QAAQzB,EAAQ,aAAe,KAAK,MAAMyB,EAAMC,CAAW,CAAC,EAAID,EAAMC,CAAW,CAAC,EAGnG,IAAMC,EAAUb,EAAa,EAE7B,OAAOa,EAAQ,KAAMX,GAAkBD,EAAO,KAAKC,CAAK,CAAC,EAAE,KAAK,IAAMW,CAAO,CAC/E,EAEA,IAAYd,EAAmCG,EAAgC,CAC7E,OAAAS,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAAIb,EAAQ,aAAe,KAAK,UAAUgB,CAAK,EAAIA,EAErE,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOH,EAAsD,CAC3D,cAAOY,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAEzB,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAAY,EAAQ,CAAC,EAEF,QAAQ,QAAQ,CACzB,CACF,CACF,CExCO,SAASG,EAAmBC,EAA+B,CAChE,IAAMC,EAAe,CACnB,MAAO,2BAA2BD,CAAO,IACzC,IAAIE,EAA4C,CAC9C,IAAMC,EAAoB,KAAKD,EAAQ,OAAO,GAAGA,EAAQ,UAAY,OAAY,KAAKA,EAAQ,OAAO,IAAM,EAAE,GAE7G,OAAID,EAAa,MAAM,QAAQE,CAAiB,IAAM,KACpDF,EAAa,MAAQ,GAAGA,EAAa,KAAK,GAAGE,CAAiB,IAGzDF,CACT,CACF,EAEA,OAAOA,CACT,CCfO,SAASG,EACdC,EACAC,EACAC,EAAqB,gBAIrB,CACA,IAAMC,EAAc,CAClB,oBAAqBF,EACrB,2BAA4BD,CAC9B,EAEA,MAAO,CACL,SAAmB,CACjB,OAAOE,IAAa,gBAAkBC,EAAc,CAAC,CACvD,EAEA,iBAAmC,CACjC,OAAOD,IAAa,wBAA0BC,EAAc,CAAC,CAC/D,CACF,CACF,CEfO,SAASC,EAAgB,CAAE,cAAAC,EAAe,OAAAC,EAAQ,QAAAC,CAAQ,EAAkC,CACjG,IAAMC,EAAsBC,EAAmBF,CAAO,EAAE,IAAI,CAC1D,QAASD,EACT,QAAAC,CACF,CAAC,EAED,OAAAF,EAAc,QAASK,GAAiBF,EAAoB,IAAIE,CAAY,CAAC,EAEtEF,CACT,CChBO,SAASG,GAA2B,CACzC,MAAO,CACL,MAAMC,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,EACA,KAAKD,EAAkBC,EAAwC,CAC7D,OAAO,QAAQ,QAAQ,CACzB,EACA,MAAMD,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCVA,IAAMC,EAAmB,EAAI,GAAK,IAE3B,SAASC,EAAmBC,EAAYC,EAAiC,KAAoB,CAClG,IAAMC,EAAa,KAAK,IAAI,EAE5B,SAASC,GAAgB,CACvB,OAAOF,IAAW,MAAQ,KAAK,IAAI,EAAIC,EAAaJ,CACtD,CAEA,SAASM,GAAsB,CAC7B,OAAOH,IAAW,aAAe,KAAK,IAAI,EAAIC,GAAcJ,CAC9D,CAEA,MAAO,CAAE,GAAGE,EAAM,OAAAC,EAAQ,WAAAC,EAAY,KAAAC,EAAM,WAAAC,CAAW,CACzD,CChBO,IAAMC,EAAN,cAA2B,KAAM,CAC7B,KAAe,eAExB,YAAYC,EAAiBC,EAAc,CACzC,MAAMD,CAAO,EAETC,IACF,KAAK,KAAOA,EAEhB,CACF,EAoBO,IAAMC,EAAN,cAAkCC,CAAa,CACpD,WAEA,YAAYC,EAAiBC,EAA0BC,EAAc,CACnE,MAAMF,EAASE,CAAI,EAEnB,KAAK,WAAaD,CACpB,CACF,EAEaE,EAAN,cAAyBL,CAAoB,CAClD,YAAYG,EAA0B,CACpC,MACE,yJACAA,EACA,YACF,CACF,CACF,EAEaG,EAAN,cAAuBN,CAAoB,CAChD,OAEA,YAAYE,EAAiBK,EAAgBJ,EAA0BC,EAAO,WAAY,CACxF,MAAMF,EAASC,EAAYC,CAAI,EAC/B,KAAK,OAASG,CAChB,CACF,EAEaC,GAAN,cAAmCP,CAAa,CACrD,SAEA,YAAYC,EAAiBO,EAAoB,CAC/C,MAAMP,EAAS,sBAAsB,EACrC,KAAK,SAAWO,CAClB,CACF,EAmBaC,GAAN,cAA+BJ,CAAS,CAC7C,MAEA,YAAYJ,EAAiBK,EAAgBI,EAAsBR,EAA0B,CAC3F,MAAMD,EAASK,EAAQJ,EAAY,kBAAkB,EACrD,KAAK,MAAQQ,CACf,CACF,EC7EO,SAASC,GAAaC,EAAYC,EAAcC,EAA0C,CAC/F,IAAMC,EAA0BC,GAAyBF,CAAe,EACpEG,EAAM,GAAGL,EAAK,QAAQ,MAAMA,EAAK,GAAG,GAAGA,EAAK,KAAO,IAAIA,EAAK,IAAI,GAAK,EAAE,IACzEC,EAAK,OAAO,CAAC,IAAM,IAAMA,EAAK,UAAU,CAAC,EAAIA,CAC/C,GAEA,OAAIE,EAAwB,SAC1BE,GAAO,IAAIF,CAAuB,IAG7BE,CACT,CAEO,SAASD,GAAyBE,EAAqC,CAC5E,OAAO,OAAO,KAAKA,CAAU,EAC1B,OAAQC,GAAQD,EAAWC,CAAG,IAAM,MAAS,EAC7C,KAAK,EACL,IACEA,GACC,GAAGA,CAAG,IAAI,mBACR,OAAO,UAAU,SAAS,KAAKD,EAAWC,CAAG,CAAC,IAAM,iBAChDD,EAAWC,CAAG,EAAE,KAAK,GAAG,EACxBD,EAAWC,CAAG,CACpB,EAAE,QAAQ,MAAO,KAAK,CAAC,EAC3B,EACC,KAAK,GAAG,CACb,CAEO,SAASC,GAAcC,EAAkBC,EAAoD,CAClG,GAAID,EAAQ,SAAW,OAAUA,EAAQ,OAAS,QAAaC,EAAe,OAAS,OACrF,OAGF,IAAMC,EAAO,MAAM,QAAQF,EAAQ,IAAI,EAAIA,EAAQ,KAAO,CAAE,GAAGA,EAAQ,KAAM,GAAGC,EAAe,IAAK,EAEpG,OAAO,KAAK,UAAUC,CAAI,CAC5B,CAEO,SAASC,GACdC,EACAC,EACAC,EACS,CACT,IAAMC,EAAmB,CACvB,OAAQ,mBACR,GAAGH,EACH,GAAGC,EACH,GAAGC,CACL,EACME,EAA6B,CAAC,EAEpC,cAAO,KAAKD,CAAO,EAAE,QAASE,GAAW,CACvC,IAAMC,EAAQH,EAAQE,CAAM,EAC5BD,EAAkBC,EAAO,YAAY,CAAC,EAAIC,CAC5C,CAAC,EAEMF,CACT,CAEO,SAASG,GAA4BC,EAA6B,CACvE,GAAI,CACF,OAAO,KAAK,MAAMA,EAAS,OAAO,CACpC,OAAS,EAAG,CACV,MAAM,IAAIC,GAAsB,EAAY,QAASD,CAAQ,CAC/D,CACF,CAEO,SAASE,GAAmB,CAAE,QAAAC,EAAS,OAAAC,CAAO,EAAaC,EAAiC,CACjG,GAAI,CACF,IAAMC,EAAS,KAAK,MAAMH,CAAO,EACjC,MAAI,UAAWG,EACN,IAAIC,GAAiBD,EAAO,QAASF,EAAQE,EAAO,MAAOD,CAAU,EAEvE,IAAIG,EAASF,EAAO,QAASF,EAAQC,CAAU,CACxD,MAAQ,CAER,CACA,OAAO,IAAIG,EAASL,EAASC,EAAQC,CAAU,CACjD,CC7FO,SAASI,GAAe,CAAE,WAAAC,EAAY,OAAAN,CAAO,EAAuC,CACzF,MAAO,CAACM,GAAc,CAAC,CAACN,IAAW,CACrC,CAEO,SAASO,GAAY,CAAE,WAAAD,EAAY,OAAAN,CAAO,EAAuC,CACtF,OAAOM,GAAcD,GAAe,CAAE,WAAAC,EAAY,OAAAN,CAAO,CAAC,GAAM,CAAC,EAAEA,EAAS,OAAS,GAAK,CAAC,EAAEA,EAAS,OAAS,CACjH,CAEO,SAASQ,GAAU,CAAE,OAAAR,CAAO,EAAsC,CACvE,MAAO,CAAC,EAAEA,EAAS,OAAS,CAC9B,CCVO,SAASS,GAA6BC,EAAwC,CACnF,OAAOA,EAAW,IAAKT,GAAeU,EAA6BV,CAAU,CAAC,CAChF,CAEO,SAASU,EAA6BV,EAAoC,CAC/E,IAAMW,EAA2BX,EAAW,QAAQ,QAAQ,mBAAmB,EAC3E,CAAE,oBAAqB,OAAQ,EAC/B,CAAC,EAEL,MAAO,CACL,GAAGA,EACH,QAAS,CACP,GAAGA,EAAW,QACd,QAAS,CACP,GAAGA,EAAW,QAAQ,QACtB,GAAGW,CACL,CACF,CACF,CACF,CCCO,SAASC,EAAkB,CAChC,MAAAC,EACA,WAAAC,EACA,YAAA3B,EACA,OAAA4B,EACA,oBAAAC,EACA,aAAAC,EACA,SAAAC,EACA,UAAAC,EACA,cAAAC,EACA,eAAAC,CACF,EAAoC,CAClC,eAAeC,EAAuBC,EAAoD,CACxF,IAAMC,EAAgB,MAAM,QAAQ,IAClCD,EAAgB,IAAKE,GACZX,EAAW,IAAIW,EAAgB,IAC7B,QAAQ,QAAQC,EAAmBD,CAAc,CAAC,CAC1D,CACF,CACH,EACME,EAAUH,EAAc,OAAQlD,GAASA,EAAK,KAAK,CAAC,EACpDsD,EAAgBJ,EAAc,OAAQlD,GAASA,EAAK,WAAW,CAAC,EAGhEuD,EAAiB,CAAC,GAAGF,EAAS,GAAGC,CAAa,EAGpD,MAAO,CACL,MAH+BC,EAAe,OAAS,EAAIA,EAAiBN,EAI5E,WAAWO,EAAuBC,EAA6B,CAe7D,OAFEH,EAAc,SAAW,GAAKE,IAAkB,EAAI,EAAIF,EAAc,OAAS,EAAIE,GAE1DC,CAC7B,CACF,CACF,CAEA,eAAeC,EACbjD,EACAC,EACAiD,EAAS,GACW,CACpB,IAAMxB,EAA2B,CAAC,EAK5BxB,EAAOH,GAAcC,EAASC,CAAc,EAC5CM,EAAUJ,GAAiBC,EAAaJ,EAAQ,QAASC,EAAe,OAAO,EAG/EkD,EACJnD,EAAQ,SAAW,MACf,CACE,GAAGA,EAAQ,KACX,GAAGC,EAAe,IACpB,EACA,CAAC,EAEDR,EAAmC,CACvC,GAAGwC,EACH,GAAGjC,EAAQ,gBACX,GAAGmD,CACL,EAMA,GAJIjB,EAAa,QACfzC,EAAgB,iBAAiB,EAAIyC,EAAa,OAGhDjC,GAAkBA,EAAe,gBACnC,QAAWH,KAAO,OAAO,KAAKG,EAAe,eAAe,EAKxD,CAACA,EAAe,gBAAgBH,CAAG,GACnC,OAAO,UAAU,SAAS,KAAKG,EAAe,gBAAgBH,CAAG,CAAC,IAAM,kBAExEL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAEzDL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAAE,SAAS,EAK1E,IAAIiD,EAAgB,EAEdK,EAAQ,MACZC,EACAC,IACuB,CAIvB,IAAM/D,EAAO8D,EAAe,IAAI,EAChC,GAAI9D,IAAS,OACX,MAAM,IAAIgE,EAAW9B,GAA6BC,CAAU,CAAC,EAG/D,IAAM8B,EAAU,CAAE,GAAGrB,EAAU,GAAGlC,EAAe,QAAS,EAEpDwD,EAAsB,CAC1B,KAAAvD,EACA,QAAAK,EACA,OAAQP,EAAQ,OAChB,IAAKV,GAAaC,EAAMS,EAAQ,KAAMP,CAAe,EACrD,eAAgB6D,EAAWP,EAAeS,EAAQ,OAAO,EACzD,gBAAiBF,EAAWP,EAAeG,EAASM,EAAQ,KAAOA,EAAQ,KAAK,CAClF,EAOME,EAAoB9C,GAAmC,CAC3D,IAAMK,EAAyB,CAC7B,QAASwC,EACT,SAAA7C,EACA,KAAArB,EACA,UAAW8D,EAAe,MAC5B,EAEA,OAAA3B,EAAW,KAAKT,CAAU,EAEnBA,CACT,EAEML,EAAW,MAAMwB,EAAU,KAAKqB,CAAO,EAE7C,GAAIlC,GAAYX,CAAQ,EAAG,CACzB,IAAMK,EAAayC,EAAiB9C,CAAQ,EAG5C,OAAIA,EAAS,YACXmC,IAOFf,EAAO,KAAK,oBAAqBL,EAA6BV,CAAU,CAAC,EAOzE,MAAMc,EAAW,IAAIxC,EAAMoD,EAAmBpD,EAAMqB,EAAS,WAAa,YAAc,MAAM,CAAC,EAExFwC,EAAMC,EAAgBC,CAAU,CACzC,CAEA,GAAI9B,GAAUZ,CAAQ,EACpB,OAAOD,GAAmBC,CAAQ,EAGpC,MAAA8C,EAAiB9C,CAAQ,EACnBE,GAAmBF,EAAUc,CAAU,CAC/C,EAUMc,EAAkBV,EAAM,OAC3BvC,GAASA,EAAK,SAAW,cAAgB2D,EAAS3D,EAAK,SAAW,OAASA,EAAK,SAAW,QAC9F,EACMoE,EAAU,MAAMpB,EAAuBC,CAAe,EAE5D,OAAOY,EAAM,CAAC,GAAGO,EAAQ,KAAK,EAAE,QAAQ,EAAGA,EAAQ,UAAU,CAC/D,CAEA,SAASC,EAAyB5D,EAAkBC,EAAiC,CAAC,EAAuB,CAK3G,IAAMiD,EAASlD,EAAQ,oBAAsBA,EAAQ,SAAW,MAChE,GAAI,CAACkD,EAKH,OAAOD,EAA4BjD,EAASC,EAAgBiD,CAAM,EAGpE,IAAMW,EAAyB,IAMtBZ,EAA4BjD,EAASC,CAAc,EAc5D,IANkBA,EAAe,WAAaD,EAAQ,aAMpC,GAChB,OAAO6D,EAAuB,EAQhC,IAAM/D,EAAM,CACV,QAAAE,EACA,eAAAC,EACA,YAAa,CACX,gBAAiBgC,EACjB,QAAS7B,CACX,CACF,EAMA,OAAOkC,EAAe,IACpBxC,EACA,IAKSuC,EAAc,IAAIvC,EAAK,IAM5BuC,EACG,IAAIvC,EAAK+D,EAAuB,CAAC,EACjC,KACEjD,GAAa,QAAQ,IAAI,CAACyB,EAAc,OAAOvC,CAAG,EAAGc,CAAQ,CAAC,EAC9DkD,GAAQ,QAAQ,IAAI,CAACzB,EAAc,OAAOvC,CAAG,EAAG,QAAQ,OAAOgE,CAAG,CAAC,CAAC,CACvE,EACC,KAAK,CAAC,CAACC,EAAGnD,CAAQ,IAAMA,CAAQ,CACrC,EAEF,CAME,KAAOA,GAAa0B,EAAe,IAAIxC,EAAKc,CAAQ,CACtD,CACF,CACF,CAEA,MAAO,CACL,WAAAmB,EACA,UAAAK,EACA,SAAAD,EACA,OAAAH,EACA,aAAAE,EACA,YAAA9B,EACA,oBAAA6B,EACA,MAAAH,EACA,QAAS8B,EACT,cAAAvB,EACA,eAAAC,CACF,CACF,CE/RO,IAAM0B,EAAmB,SAEnBC,EAAU,CAAC,KAAM,IAAI,EAIlC,SAASC,GAAgBC,EAAwB,CAG/C,MAAO,CAAC,CAAE,IAFE,uCAAuC,QAAQ,WAAYA,CAAM,EAE9D,OAAQ,YAAa,SAAU,OAAQ,CAAC,CACzD,CAEO,SAASC,EAA4B,CAC1C,MAAOC,EACP,OAAQC,EACR,SAAAC,EACA,cAAAC,EACA,OAAQC,EACR,GAAGC,CACL,EAAwC,CACtC,IAAMC,EAAOC,EAAWP,EAAaC,EAAcC,CAAQ,EACrDM,EAAcC,EAAkB,CACpC,MAAOZ,GAAgBO,CAAY,EACnC,GAAGC,EACH,aAAcK,EAAgB,CAC5B,cAAAP,EACA,OAAQ,kBACR,QAASR,CACX,CAAC,EACD,YAAa,CACX,eAAgB,aAChB,GAAGW,EAAK,QAAQ,EAChB,GAAGD,EAAQ,WACb,EACA,oBAAqB,CACnB,GAAGC,EAAK,gBAAgB,EACxB,GAAGD,EAAQ,mBACb,CACF,CAAC,EAED,MAAO,CACL,YAAAG,EAKA,MAAOR,EAKP,OAAQC,EAKR,YAA4B,CAC1B,OAAO,QAAQ,IAAI,CAACO,EAAY,cAAc,MAAM,EAAGA,EAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,IAAG,EAAY,CAClH,EAKA,IAAI,KAAc,CAChB,OAAOA,EAAY,aAAa,KAClC,EAQA,gBAAgBG,EAAiBC,EAAoC,CACnEJ,EAAY,aAAa,IAAI,CAAE,QAAAG,EAAS,QAAAC,CAAQ,CAAC,CACnD,EAQA,gBAAgB,CAAE,OAAAC,CAAO,EAA6B,CAChD,CAACX,GAAYA,IAAa,gBAC5BM,EAAY,YAAY,mBAAmB,EAAIK,EAE/CL,EAAY,oBAAoB,mBAAmB,EAAIK,CAE3D,EASA,aACE,CAAE,KAAAC,EAAM,WAAAC,CAAW,EACnBC,EACkC,CAClC,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAMG,EAAmB,CACvB,OAAQ,SACR,KANkB,UAAU,QAAQ,SAAUH,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAOP,EAAY,QAAQS,EAASD,CAAc,CACpD,EASA,UAAU,CAAE,KAAAF,EAAM,WAAAC,CAAW,EAAmBC,EAAmE,CACjH,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMG,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUH,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAOP,EAAY,QAAQS,EAASD,CAAc,CACpD,EAUA,WACE,CAAE,KAAAF,EAAM,WAAAC,EAAY,KAAAG,CAAK,EACzBF,EACkC,CAClC,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,yDAAyD,EAO3E,IAAMG,EAAmB,CACvB,OAAQ,OACR,KANkB,UAAU,QAAQ,SAAUH,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMG,GAAc,CAAC,CACvB,EAEA,OAAOV,EAAY,QAAQS,EAASD,CAAc,CACpD,EAUA,UACE,CAAE,KAAAF,EAAM,WAAAC,EAAY,KAAAG,CAAK,EACzBF,EACkC,CAClC,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMG,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUH,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMG,GAAc,CAAC,CACvB,EAEA,OAAOV,EAAY,QAAQS,EAASD,CAAc,CACpD,EAWA,kBACE,CAAE,UAAAG,CAAU,EACZH,EACoC,CACpC,GAAI,CAACG,EACH,MAAM,IAAI,MAAM,qEAAqE,EAOvF,IAAMF,EAAmB,CACvB,OAAQ,SACR,KANkB,0BAA0B,QAAQ,cAAe,mBAAmBE,CAAS,CAAC,EAOhG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOX,EAAY,QAAQS,EAASD,CAAc,CACpD,EASA,2BAA2BA,EAAqF,CAK9G,IAAMC,EAAmB,CACvB,OAAQ,MACR,KANkB,gCAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOT,EAAY,QAAQS,EAASD,CAAc,CACpD,EAWA,oBACE,CAAE,UAAAG,CAAU,EACZH,EAC+B,CAC/B,GAAI,CAACG,EACH,MAAM,IAAI,MAAM,uEAAuE,EAUzF,IAAMF,EAAmB,CACvB,OAAQ,MACR,KATkB,0CAA0C,QAC5D,cACA,mBAAmBE,CAAS,CAC9B,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOX,EAAY,QAAQS,EAASD,CAAc,CACpD,EAUA,2BACEI,EACAJ,EAC6C,CAC7C,GAAI,CAACI,EACH,MAAM,IAAI,MACR,kGACF,EAGF,GAAI,CAACA,EAA8B,cACjC,MAAM,IAAI,MACR,gHACF,EAEF,GAAI,CAACA,EAA8B,cACjC,MAAM,IAAI,MACR,gHACF,EAEF,GAAI,CAACA,EAA8B,sBACjC,MAAM,IAAI,MACR,wHACF,EAOF,IAAMH,EAAmB,CACvB,OAAQ,OACR,KANkB,gCAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMG,CACR,EAEA,OAAOZ,EAAY,QAAQS,EAASD,CAAc,CACpD,CACF,CACF,CC3VO,SAASK,GACdC,EACAC,EACAC,EACAC,EACuB,CACvB,GAAI,CAACH,GAAS,OAAOA,GAAU,SAC7B,MAAM,IAAI,MAAM,qBAAqB,EAGvC,GAAI,CAACC,GAAU,OAAOA,GAAW,SAC/B,MAAM,IAAI,MAAM,sBAAsB,EAGxC,GAAI,CAACC,GAAWA,IAAW,OAAOA,GAAW,UAAY,CAACE,EAAQ,SAASF,CAAM,GAC/E,MAAM,IAAI,MAAM,4DAA4DE,EAAQ,KAAK,IAAI,CAAC,EAAE,EAGlG,OAAOC,EAA4B,CACjC,MAAAL,EACA,OAAAC,EACA,OAAAC,EACA,SAAU,CACR,QAAS,IACT,KAAM,IACN,MAAO,GACT,EACA,OAAQI,EAAiB,EACzB,UAAWC,EAAmB,EAC9B,cAAe,CAAC,CAAE,QAAS,SAAU,CAAC,EACtC,SAAU,wBACV,eAAgBC,EAAkB,EAClC,cAAeA,EAAkB,CAAE,aAAc,EAAM,CAAC,EACxD,WAAYC,EAAwB,CAClC,OAAQ,CAACC,EAA+B,CAAE,IAAK,GAAGC,CAAgB,IAAIX,CAAK,EAAG,CAAC,EAAGQ,EAAkB,CAAC,CACvG,CAAC,EACD,GAAGL,CACL,CAAC,CACH", "names": ["createXhrRequester", "send", "request", "resolve", "baseRequester", "key", "createTimeout", "timeout", "content", "connectTimeout", "responseTimeout", "createBrowserLocalStorageCache", "options", "storage", "namespaceKey", "getStorage", "getNamespace", "setNamespace", "namespace", "removeOutdatedCacheItems", "timeToLive", "filteredNamespaceWithoutOldFormattedCacheItems", "cacheItem", "filteredNamespaceWithoutExpiredItems", "currentTimestamp", "key", "defaultValue", "events", "value", "exists", "createNullCache", "_key", "result", "createFallbackableCache", "caches", "current", "createMemoryCache", "cache", "keyAsString", "promise", "createAlgoliaAgent", "version", "algoliaAgent", "options", "addedAlgoliaAgent", "createAuth", "appId", "<PERSON><PERSON><PERSON><PERSON>", "authMode", "credentials", "getAlgoliaAgent", "algoliaAgents", "client", "version", "defaultAlgoliaAgent", "createAlgoliaAgent", "algoliaAgent", "createNullLogger", "_message", "_args", "EXPIRATION_DELAY", "createStatefulHost", "host", "status", "lastUpdate", "isUp", "isTimedOut", "AlgoliaError", "message", "name", "ErrorWithStackTrace", "AlgoliaError", "message", "stackTrace", "name", "RetryError", "ApiError", "status", "DeserializationError", "response", "DetailedApiError", "error", "serializeUrl", "host", "path", "queryParameters", "queryParametersAsString", "serializeQueryParameters", "url", "parameters", "key", "serializeData", "request", "requestOptions", "data", "serializeHeaders", "baseHeaders", "requestHeaders", "requestOptionsHeaders", "headers", "serializedHeaders", "header", "value", "deserializeSuccess", "response", "DeserializationError", "deserializeFailure", "content", "status", "stackFrame", "parsed", "DetailedApiError", "ApiError", "isNetworkError", "isTimedOut", "isRetryable", "isSuccess", "stackTraceWithoutCredentials", "stackTrace", "stackFrameWithoutCredentials", "modifiedHeaders", "createTransporter", "hosts", "hostsCache", "logger", "baseQueryParameters", "algoliaAgent", "timeouts", "requester", "requestsCache", "responsesCache", "createRetryableOptions", "compatibleHosts", "statefulHosts", "compatibleHost", "createStatefulHost", "hostsUp", "hostsTimedOut", "hostsAvailable", "timeoutsCount", "baseTimeout", "retryableRequest", "isRead", "dataQueryParameters", "retry", "retryableHosts", "getTimeout", "RetryError", "timeout", "payload", "pushToStackTrace", "options", "createRequest", "createRetryableRequest", "err", "_", "apiClientVersion", "REGIONS", "getDefaultHosts", "region", "createPersonalizationClient", "appIdOption", "apiKeyOption", "authMode", "algoliaAgents", "regionOption", "options", "auth", "createAuth", "transporter", "createTransporter", "getAlgoliaAgent", "segment", "version", "<PERSON><PERSON><PERSON><PERSON>", "path", "parameters", "requestOptions", "request", "body", "userToken", "personalizationStrategyParams", "personalizationClient", "appId", "<PERSON><PERSON><PERSON><PERSON>", "region", "options", "REGIONS", "createPersonalizationClient", "createNullLogger", "m", "createMemoryCache", "createFallbackableCache", "createBrowserLocalStorageCache", "apiClientVersion"]}