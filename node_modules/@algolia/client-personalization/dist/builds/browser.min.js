function H(){function r(e){return new Promise(o=>{let t=new XMLHttpRequest;t.open(e.method,e.url,!0),Object.keys(e.headers).forEach(n=>t.setRequestHeader(n,e.headers[n]));let a=(n,s)=>setTimeout(()=>{t.abort(),o({status:0,content:s,isTimedOut:!0})},n),m=a(e.connectTimeout,"Connection timeout"),h;t.onreadystatechange=()=>{t.readyState>t.OPENED&&h===void 0&&(clearTimeout(m),h=a(e.responseTimeout,"Socket timeout"))},t.onerror=()=>{t.status===0&&(clearTimeout(m),clearTimeout(h),o({content:t.responseText||"Network request failed",status:t.status,isTimedOut:!1}))},t.onload=()=>{clearTimeout(m),clearTimeout(h),o({content:t.responseText,status:t.status,isTimedOut:!1})},t.send(e.data)})}return{send:r}}function j(r){let e,o=`algolia-client-js-${r.key}`;function t(){return e===void 0&&(e=r.localStorage||window.localStorage),e}function a(){return JSON.parse(t().getItem(o)||"{}")}function m(n){t().setItem(o,JSON.stringify(n))}function h(){let n=r.timeToLive?r.timeToLive*1e3:null,s=a(),i=Object.fromEntries(Object.entries(s).filter(([,d])=>d.timestamp!==void 0));if(m(i),!n)return;let u=Object.fromEntries(Object.entries(i).filter(([,d])=>{let P=new Date().getTime();return!(d.timestamp+n<P)}));m(u)}return{get(n,s,i={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(h(),a()[JSON.stringify(n)])).then(u=>Promise.all([u?u.value:s(),u!==void 0])).then(([u,d])=>Promise.all([u,d||i.miss(u)])).then(([u])=>u)},set(n,s){return Promise.resolve().then(()=>{let i=a();return i[JSON.stringify(n)]={timestamp:new Date().getTime(),value:s},t().setItem(o,JSON.stringify(i)),s})},delete(n){return Promise.resolve().then(()=>{let s=a();delete s[JSON.stringify(n)],t().setItem(o,JSON.stringify(s))})},clear(){return Promise.resolve().then(()=>{t().removeItem(o)})}}}function V(){return{get(r,e,o={miss:()=>Promise.resolve()}){return e().then(a=>Promise.all([a,o.miss(a)])).then(([a])=>a)},set(r,e){return Promise.resolve(e)},delete(r){return Promise.resolve()},clear(){return Promise.resolve()}}}function q(r){let e=[...r.caches],o=e.shift();return o===void 0?V():{get(t,a,m={miss:()=>Promise.resolve()}){return o.get(t,a,m).catch(()=>q({caches:e}).get(t,a,m))},set(t,a){return o.set(t,a).catch(()=>q({caches:e}).set(t,a))},delete(t){return o.delete(t).catch(()=>q({caches:e}).delete(t))},clear(){return o.clear().catch(()=>q({caches:e}).clear())}}}function O(r={serializable:!0}){let e={};return{get(o,t,a={miss:()=>Promise.resolve()}){let m=JSON.stringify(o);if(m in e)return Promise.resolve(r.serializable?JSON.parse(e[m]):e[m]);let h=t();return h.then(n=>a.miss(n)).then(()=>h)},set(o,t){return e[JSON.stringify(o)]=r.serializable?JSON.stringify(t):t,Promise.resolve(t)},delete(o){return delete e[JSON.stringify(o)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function Y(r){let e={value:`Algolia for JavaScript (${r})`,add(o){let t=`; ${o.segment}${o.version!==void 0?` (${o.version})`:""}`;return e.value.indexOf(t)===-1&&(e.value=`${e.value}${t}`),e}};return e}function G(r,e,o="WithinHeaders"){let t={"x-algolia-api-key":e,"x-algolia-application-id":r};return{headers(){return o==="WithinHeaders"?t:{}},queryParameters(){return o==="WithinQueryParameters"?t:{}}}}function W({algoliaAgents:r,client:e,version:o}){let t=Y(o).add({segment:e,version:o});return r.forEach(a=>t.add(a)),t}function J(){return{debug(r,e){return Promise.resolve()},info(r,e){return Promise.resolve()},error(r,e){return Promise.resolve()}}}var L=2*60*1e3;function $(r,e="up"){let o=Date.now();function t(){return e==="up"||Date.now()-o>L}function a(){return e==="timed out"&&Date.now()-o<=L}return{...r,status:e,lastUpdate:o,isUp:t,isTimedOut:a}}var Q=class extends Error{name="AlgoliaError";constructor(r,e){super(r),e&&(this.name=e)}};var M=class extends Q{stackTrace;constructor(r,e,o){super(r,o),this.stackTrace=e}},Z=class extends M{constructor(r){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",r,"RetryError")}},z=class extends M{status;constructor(r,e,o,t="ApiError"){super(r,o,t),this.status=e}},ee=class extends Q{response;constructor(r,e){super(r,"DeserializationError"),this.response=e}},re=class extends z{error;constructor(r,e,o,t){super(r,e,t,"DetailedApiError"),this.error=o}};function te(r,e,o){let t=oe(o),a=`${r.protocol}://${r.url}${r.port?`:${r.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return t.length&&(a+=`?${t}`),a}function oe(r){return Object.keys(r).filter(e=>r[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(r[e])==="[object Array]"?r[e].join(","):r[e]).replace(/\+/g,"%20")}`).join("&")}function se(r,e){if(r.method==="GET"||r.data===void 0&&e.data===void 0)return;let o=Array.isArray(r.data)?r.data:{...r.data,...e.data};return JSON.stringify(o)}function ae(r,e,o){let t={Accept:"application/json",...r,...e,...o},a={};return Object.keys(t).forEach(m=>{let h=t[m];a[m.toLowerCase()]=h}),a}function ne(r){try{return JSON.parse(r.content)}catch(e){throw new ee(e.message,r)}}function ie({content:r,status:e},o){try{let t=JSON.parse(r);return"error"in t?new re(t.message,e,t.error,o):new z(t.message,e,o)}catch{}return new z(r,e,o)}function ce({isTimedOut:r,status:e}){return!r&&~~e===0}function ue({isTimedOut:r,status:e}){return r||ce({isTimedOut:r,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function le({status:r}){return~~(r/100)===2}function me(r){return r.map(e=>F(e))}function F(r){let e=r.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...r,request:{...r.request,headers:{...r.request.headers,...e}}}}function B({hosts:r,hostsCache:e,baseHeaders:o,logger:t,baseQueryParameters:a,algoliaAgent:m,timeouts:h,requester:n,requestsCache:s,responsesCache:i}){async function u(c){let l=await Promise.all(c.map(p=>e.get(p,()=>Promise.resolve($(p))))),g=l.filter(p=>p.isUp()),y=l.filter(p=>p.isTimedOut()),R=[...g,...y];return{hosts:R.length>0?R:c,getTimeout(p,T){return(y.length===0&&p===0?1:y.length+3+p)*T}}}async function d(c,l,g=!0){let y=[],R=se(c,l),E=ae(o,c.headers,l.headers),p=c.method==="GET"?{...c.data,...l.data}:{},T={...a,...c.queryParameters,...p};if(m.value&&(T["x-algolia-agent"]=m.value),l&&l.queryParameters)for(let f of Object.keys(l.queryParameters))!l.queryParameters[f]||Object.prototype.toString.call(l.queryParameters[f])==="[object Object]"?T[f]=l.queryParameters[f]:T[f]=l.queryParameters[f].toString();let S=0,N=async(f,A)=>{let v=f.pop();if(v===void 0)throw new Z(me(y));let C={...h,...l.timeouts},k={data:R,headers:E,method:c.method,url:te(v,c.path,T),connectTimeout:A(S,C.connect),responseTimeout:A(S,g?C.read:C.write)},D=b=>{let _={request:k,response:b,host:v,triesLeft:f.length};return y.push(_),_},w=await n.send(k);if(ue(w)){let b=D(w);return w.isTimedOut&&S++,t.info("Retryable failure",F(b)),await e.set(v,$(v,w.isTimedOut?"timed out":"down")),N(f,A)}if(le(w))return ne(w);throw D(w),ie(w,y)},K=r.filter(f=>f.accept==="readWrite"||(g?f.accept==="read":f.accept==="write")),U=await u(K);return N([...U.hosts].reverse(),U.getTimeout)}function P(c,l={}){let g=c.useReadTransporter||c.method==="GET";if(!g)return d(c,l,g);let y=()=>d(c,l);if((l.cacheable||c.cacheable)!==!0)return y();let E={request:c,requestOptions:l,transporter:{queryParameters:a,headers:o}};return i.get(E,()=>s.get(E,()=>s.set(E,y()).then(p=>Promise.all([s.delete(E),p]),p=>Promise.all([s.delete(E),Promise.reject(p)])).then(([p,T])=>T)),{miss:p=>i.set(E,p)})}return{hostsCache:e,requester:n,timeouts:h,logger:t,algoliaAgent:m,baseHeaders:o,baseQueryParameters:a,hosts:r,request:P,requestsCache:s,responsesCache:i}}var x="5.30.0",I=["eu","us"];function de(r){return[{url:"personalization.{region}.algolia.com".replace("{region}",r),accept:"readWrite",protocol:"https"}]}function X({appId:r,apiKey:e,authMode:o,algoliaAgents:t,region:a,...m}){let h=G(r,e,o),n=B({hosts:de(a),...m,algoliaAgent:W({algoliaAgents:t,client:"Personalization",version:x}),baseHeaders:{"content-type":"text/plain",...h.headers(),...m.baseHeaders},baseQueryParameters:{...h.queryParameters(),...m.baseQueryParameters}});return{transporter:n,appId:r,apiKey:e,clearCache(){return Promise.all([n.requestsCache.clear(),n.responsesCache.clear()]).then(()=>{})},get _ua(){return n.algoliaAgent.value},addAlgoliaAgent(s,i){n.algoliaAgent.add({segment:s,version:i})},setClientApiKey({apiKey:s}){!o||o==="WithinHeaders"?n.baseHeaders["x-algolia-api-key"]=s:n.baseQueryParameters["x-algolia-api-key"]=s},customDelete({path:s,parameters:i},u){if(!s)throw new Error("Parameter `path` is required when calling `customDelete`.");let l={method:"DELETE",path:"/{path}".replace("{path}",s),queryParameters:i||{},headers:{}};return n.request(l,u)},customGet({path:s,parameters:i},u){if(!s)throw new Error("Parameter `path` is required when calling `customGet`.");let l={method:"GET",path:"/{path}".replace("{path}",s),queryParameters:i||{},headers:{}};return n.request(l,u)},customPost({path:s,parameters:i,body:u},d){if(!s)throw new Error("Parameter `path` is required when calling `customPost`.");let g={method:"POST",path:"/{path}".replace("{path}",s),queryParameters:i||{},headers:{},data:u||{}};return n.request(g,d)},customPut({path:s,parameters:i,body:u},d){if(!s)throw new Error("Parameter `path` is required when calling `customPut`.");let g={method:"PUT",path:"/{path}".replace("{path}",s),queryParameters:i||{},headers:{},data:u||{}};return n.request(g,d)},deleteUserProfile({userToken:s},i){if(!s)throw new Error("Parameter `userToken` is required when calling `deleteUserProfile`.");let c={method:"DELETE",path:"/1/profiles/{userToken}".replace("{userToken}",encodeURIComponent(s)),queryParameters:{},headers:{}};return n.request(c,i)},getPersonalizationStrategy(s){let P={method:"GET",path:"/1/strategies/personalization",queryParameters:{},headers:{}};return n.request(P,s)},getUserTokenProfile({userToken:s},i){if(!s)throw new Error("Parameter `userToken` is required when calling `getUserTokenProfile`.");let c={method:"GET",path:"/1/profiles/personalization/{userToken}".replace("{userToken}",encodeURIComponent(s)),queryParameters:{},headers:{}};return n.request(c,i)},setPersonalizationStrategy(s,i){if(!s)throw new Error("Parameter `personalizationStrategyParams` is required when calling `setPersonalizationStrategy`.");if(!s.eventsScoring)throw new Error("Parameter `personalizationStrategyParams.eventsScoring` is required when calling `setPersonalizationStrategy`.");if(!s.facetsScoring)throw new Error("Parameter `personalizationStrategyParams.facetsScoring` is required when calling `setPersonalizationStrategy`.");if(!s.personalizationImpact)throw new Error("Parameter `personalizationStrategyParams.personalizationImpact` is required when calling `setPersonalizationStrategy`.");let c={method:"POST",path:"/1/strategies/personalization",queryParameters:{},headers:{},data:s};return n.request(c,i)}}}function De(r,e,o,t){if(!r||typeof r!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");if(!o||o&&(typeof o!="string"||!I.includes(o)))throw new Error(`\`region\` is required and must be one of the following: ${I.join(", ")}`);return X({appId:r,apiKey:e,region:o,timeouts:{connect:1e3,read:2e3,write:3e4},logger:J(),requester:H(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:O(),requestsCache:O({serializable:!1}),hostsCache:q({caches:[j({key:`${x}-${r}`}),O()]}),...t})}export{x as apiClientVersion,De as personalizationClient};
//# sourceMappingURL=browser.min.js.map