import * as _algolia_client_common from '@algolia/client-common';
import { CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

type DeleteUserProfileResponse = {
    /**
     * Unique pseudonymous or anonymous user identifier.  This helps with analytics and click and conversion events. For more information, see [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Date and time when the user profile can be safely considered to be deleted. Any events received after the `deletedUntil` date start a new user profile.
     */
    deletedUntil: string;
};

type GetUserTokenResponse = {
    /**
     * Unique pseudonymous or anonymous user identifier.  This helps with analytics and click and conversion events. For more information, see [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken: string;
    /**
     * Date and time of the last event from this user, in RFC 3339 format.
     */
    lastEventAt: string;
    /**
     * Scores for different facet values.  Scores represent the user affinity for a user profile towards specific facet values, given the personalization strategy and past events.
     */
    scores: Record<string, unknown>;
};

/**
 * Event type.
 */
type EventType = 'click' | 'conversion' | 'view';

type EventsScoring = {
    /**
     * Event score.
     */
    score: number;
    /**
     * Event name.
     */
    eventName: string;
    eventType: EventType;
};

type FacetsScoring = {
    /**
     * Event score.
     */
    score: number;
    /**
     * Facet attribute name.
     */
    facetName: string;
};

type PersonalizationStrategyParams = {
    /**
     * Scores associated with each event.  The higher the scores, the higher the impact of those events on the personalization of search results.
     */
    eventsScoring: Array<EventsScoring>;
    /**
     * Scores associated with each facet.  The higher the scores, the higher the impact of those events on the personalization of search results.
     */
    facetsScoring: Array<FacetsScoring>;
    /**
     * Impact of personalization on the search results.  If set to 0, personalization has no impact on the search results.
     */
    personalizationImpact: number;
};

type SetPersonalizationStrategyResponse = {
    /**
     * A message confirming the strategy update.
     */
    message: string;
};

/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `deleteUserProfile` method.
 */
type DeleteUserProfileProps = {
    /**
     * Unique identifier representing a user for which to fetch the personalization profile.
     */
    userToken: string;
};
/**
 * Properties for the `getUserTokenProfile` method.
 */
type GetUserTokenProfileProps = {
    /**
     * Unique identifier representing a user for which to fetch the personalization profile.
     */
    userToken: string;
};

declare const apiClientVersion = "5.30.0";
declare const REGIONS: readonly ["eu", "us"];
type Region = (typeof REGIONS)[number];
type RegionOptions = {
    region: Region;
};
declare function createPersonalizationClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, region: regionOption, ...options }: CreateClientOptions & RegionOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string | undefined): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Deletes a user profile.  The response includes a date and time when the user profile can safely be considered deleted.
     *
     * Required API Key ACLs:
     *  - recommendation
     * @param deleteUserProfile - The deleteUserProfile object.
     * @param deleteUserProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteUserProfile({ userToken }: DeleteUserProfileProps, requestOptions?: RequestOptions): Promise<DeleteUserProfileResponse>;
    /**
     * Retrieves the current personalization strategy.
     *
     * Required API Key ACLs:
     *  - recommendation
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getPersonalizationStrategy(requestOptions?: RequestOptions | undefined): Promise<PersonalizationStrategyParams>;
    /**
     * Retrieves a user profile and their affinities for different facets.
     *
     * Required API Key ACLs:
     *  - recommendation
     * @param getUserTokenProfile - The getUserTokenProfile object.
     * @param getUserTokenProfile.userToken - Unique identifier representing a user for which to fetch the personalization profile.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getUserTokenProfile({ userToken }: GetUserTokenProfileProps, requestOptions?: RequestOptions): Promise<GetUserTokenResponse>;
    /**
     * Creates a new personalization strategy.
     *
     * Required API Key ACLs:
     *  - recommendation
     * @param personalizationStrategyParams - The personalizationStrategyParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    setPersonalizationStrategy(personalizationStrategyParams: PersonalizationStrategyParams, requestOptions?: RequestOptions): Promise<SetPersonalizationStrategyResponse>;
};

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string | undefined;
};

type PersonalizationClient = ReturnType<typeof createPersonalizationClient>;

declare function personalizationClient(appId: string, apiKey: string, region: Region, options?: ClientOptions | undefined): PersonalizationClient;

export { type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type DeleteUserProfileProps, type DeleteUserProfileResponse, type ErrorBase, type EventType, type EventsScoring, type FacetsScoring, type GetUserTokenProfileProps, type GetUserTokenResponse, type PersonalizationClient, type PersonalizationStrategyParams, type Region, type RegionOptions, type SetPersonalizationStrategyResponse, apiClientVersion, personalizationClient };
