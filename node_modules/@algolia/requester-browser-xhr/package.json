{"name": "@algolia/requester-browser-xhr", "version": "5.30.0", "description": "Promise-based request library for browser using xhr.", "repository": {"type": "git", "url": "git+https://github.com/algolia/algoliasearch-client-javascript.git"}, "homepage": "https://github.com/algolia/algoliasearch-client-javascript#readme", "license": "MIT", "author": "Algolia", "type": "module", "exports": {".": {"types": "./dist/requester.xhr.d.ts", "import": "./dist/requester.xhr.js", "module": "./dist/requester.xhr.js", "default": "./dist/requester.xhr.js"}, "./src/*": "./src/*.ts"}, "react-native": "./dist/requester.xhr.js", "files": ["dist", "index.d.ts", "index.js"], "scripts": {"build": "yarn clean && yarn tsup", "clean": "rm -rf ./dist || true", "test": "tsc --noEmit && vitest --run", "test:bundle": "publint . && attw --pack . --ignore-rules cjs-resolves-to-esm"}, "dependencies": {"@algolia/client-common": "5.30.0"}, "devDependencies": {"@arethetypeswrong/cli": "0.18.2", "@types/node": "22.15.34", "jsdom": "26.1.0", "publint": "0.3.12", "tsup": "8.5.0", "typescript": "5.8.3", "vitest": "3.2.4", "xhr-mock": "2.5.1"}, "engines": {"node": ">= 14.0.0"}}