{"name": "@algolia/client-common", "version": "5.30.0", "description": "Common package for the Algolia JavaScript API client.", "repository": {"type": "git", "url": "git+https://github.com/algolia/algoliasearch-client-javascript.git"}, "homepage": "https://github.com/algolia/algoliasearch-client-javascript#readme", "license": "MIT", "author": "Algolia", "type": "module", "files": ["dist", "index.js", "index.d.ts"], "exports": {".": {"types": {"import": "./dist/common.d.ts", "module": "./dist/common.d.ts", "require": "./dist/common.d.cts"}, "import": "./dist/common.js", "module": "./dist/common.js", "require": "./dist/common.cjs"}, "./src/*": "./src/*.ts"}, "scripts": {"build": "yarn clean && yarn tsup", "clean": "rm -rf ./dist || true", "test": "tsc --noEmit && vitest --run", "test:bundle": "publint . && attw --pack ."}, "devDependencies": {"@arethetypeswrong/cli": "0.18.2", "@types/node": "22.15.34", "jsdom": "26.1.0", "publint": "0.3.12", "ts-node": "10.9.2", "tsup": "8.5.0", "typescript": "5.8.3", "vitest": "3.2.4"}, "engines": {"node": ">= 14.0.0"}}