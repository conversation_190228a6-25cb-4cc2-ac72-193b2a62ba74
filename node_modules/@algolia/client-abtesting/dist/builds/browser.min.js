function k(){function t(e){return new Promise(s=>{let r=new XMLHttpRequest;r.open(e.method,e.url,!0),Object.keys(e.headers).forEach(n=>r.setRequestHeader(n,e.headers[n]));let i=(n,o)=>setTimeout(()=>{r.abort(),s({status:0,content:o,isTimedOut:!0})},n),m=i(e.connectTimeout,"Connection timeout"),h;r.onreadystatechange=()=>{r.readyState>r.OPENED&&h===void 0&&(clearTimeout(m),h=i(e.responseTimeout,"Socket timeout"))},r.onerror=()=>{r.status===0&&(clearTimeout(m),clearTimeout(h),s({content:r.responseText||"Network request failed",status:r.status,isTimedOut:!1}))},r.onload=()=>{clearTimeout(m),clearTimeout(h),s({content:r.responseText,status:r.status,isTimedOut:!1})},r.send(e.data)})}return{send:t}}function j(t){let e,s=`algolia-client-js-${t.key}`;function r(){return e===void 0&&(e=t.localStorage||window.localStorage),e}function i(){return JSON.parse(r().getItem(s)||"{}")}function m(n){r().setItem(s,JSON.stringify(n))}function h(){let n=t.timeToLive?t.timeToLive*1e3:null,o=i(),a=Object.fromEntries(Object.entries(o).filter(([,d])=>d.timestamp!==void 0));if(m(a),!n)return;let c=Object.fromEntries(Object.entries(a).filter(([,d])=>{let P=new Date().getTime();return!(d.timestamp+n<P)}));m(c)}return{get(n,o,a={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(h(),i()[JSON.stringify(n)])).then(c=>Promise.all([c?c.value:o(),c!==void 0])).then(([c,d])=>Promise.all([c,d||a.miss(c)])).then(([c])=>c)},set(n,o){return Promise.resolve().then(()=>{let a=i();return a[JSON.stringify(n)]={timestamp:new Date().getTime(),value:o},r().setItem(s,JSON.stringify(a)),o})},delete(n){return Promise.resolve().then(()=>{let o=i();delete o[JSON.stringify(n)],r().setItem(s,JSON.stringify(o))})},clear(){return Promise.resolve().then(()=>{r().removeItem(s)})}}}function V(){return{get(t,e,s={miss:()=>Promise.resolve()}){return e().then(i=>Promise.all([i,s.miss(i)])).then(([i])=>i)},set(t,e){return Promise.resolve(e)},delete(t){return Promise.resolve()},clear(){return Promise.resolve()}}}function w(t){let e=[...t.caches],s=e.shift();return s===void 0?V():{get(r,i,m={miss:()=>Promise.resolve()}){return s.get(r,i,m).catch(()=>w({caches:e}).get(r,i,m))},set(r,i){return s.set(r,i).catch(()=>w({caches:e}).set(r,i))},delete(r){return s.delete(r).catch(()=>w({caches:e}).delete(r))},clear(){return s.clear().catch(()=>w({caches:e}).clear())}}}function x(t={serializable:!0}){let e={};return{get(s,r,i={miss:()=>Promise.resolve()}){let m=JSON.stringify(s);if(m in e)return Promise.resolve(t.serializable?JSON.parse(e[m]):e[m]);let h=r();return h.then(n=>i.miss(n)).then(()=>h)},set(s,r){return e[JSON.stringify(s)]=t.serializable?JSON.stringify(r):r,Promise.resolve(r)},delete(s){return delete e[JSON.stringify(s)],Promise.resolve()},clear(){return e={},Promise.resolve()}}}function Y(t){let e={value:`Algolia for JavaScript (${t})`,add(s){let r=`; ${s.segment}${s.version!==void 0?` (${s.version})`:""}`;return e.value.indexOf(r)===-1&&(e.value=`${e.value}${r}`),e}};return e}function Q(t,e,s="WithinHeaders"){let r={"x-algolia-api-key":e,"x-algolia-application-id":t};return{headers(){return s==="WithinHeaders"?r:{}},queryParameters(){return s==="WithinQueryParameters"?r:{}}}}function W({algoliaAgents:t,client:e,version:s}){let r=Y(s).add({segment:e,version:s});return t.forEach(i=>r.add(i)),r}function J(){return{debug(t,e){return Promise.resolve()},info(t,e){return Promise.resolve()},error(t,e){return Promise.resolve()}}}var U=2*60*1e3;function $(t,e="up"){let s=Date.now();function r(){return e==="up"||Date.now()-s>U}function i(){return e==="timed out"&&Date.now()-s<=U}return{...t,status:e,lastUpdate:s,isUp:r,isTimedOut:i}}var G=class extends Error{name="AlgoliaError";constructor(t,e){super(t),e&&(this.name=e)}};var z=class extends G{stackTrace;constructor(t,e,s){super(t,s),this.stackTrace=e}},Z=class extends z{constructor(t){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",t,"RetryError")}},C=class extends z{status;constructor(t,e,s,r="ApiError"){super(t,s,r),this.status=e}},ee=class extends G{response;constructor(t,e){super(t,"DeserializationError"),this.response=e}},te=class extends C{error;constructor(t,e,s,r){super(t,e,r,"DetailedApiError"),this.error=s}};function re(t,e,s){let r=se(s),i=`${t.protocol}://${t.url}${t.port?`:${t.port}`:""}/${e.charAt(0)==="/"?e.substring(1):e}`;return r.length&&(i+=`?${r}`),i}function se(t){return Object.keys(t).filter(e=>t[e]!==void 0).sort().map(e=>`${e}=${encodeURIComponent(Object.prototype.toString.call(t[e])==="[object Array]"?t[e].join(","):t[e]).replace(/\+/g,"%20")}`).join("&")}function oe(t,e){if(t.method==="GET"||t.data===void 0&&e.data===void 0)return;let s=Array.isArray(t.data)?t.data:{...t.data,...e.data};return JSON.stringify(s)}function ae(t,e,s){let r={Accept:"application/json",...t,...e,...s},i={};return Object.keys(r).forEach(m=>{let h=r[m];i[m.toLowerCase()]=h}),i}function ne(t){try{return JSON.parse(t.content)}catch(e){throw new ee(e.message,t)}}function ie({content:t,status:e},s){try{let r=JSON.parse(t);return"error"in r?new te(r.message,e,r.error,s):new C(r.message,e,s)}catch{}return new C(t,e,s)}function ue({isTimedOut:t,status:e}){return!t&&~~e===0}function ce({isTimedOut:t,status:e}){return t||ue({isTimedOut:t,status:e})||~~(e/100)!==2&&~~(e/100)!==4}function le({status:t}){return~~(t/100)===2}function me(t){return t.map(e=>M(e))}function M(t){let e=t.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...t,request:{...t.request,headers:{...t.request.headers,...e}}}}function F({hosts:t,hostsCache:e,baseHeaders:s,logger:r,baseQueryParameters:i,algoliaAgent:m,timeouts:h,requester:n,requestsCache:o,responsesCache:a}){async function c(u){let l=await Promise.all(u.map(p=>e.get(p,()=>Promise.resolve($(p))))),f=l.filter(p=>p.isUp()),T=l.filter(p=>p.isTimedOut()),E=[...f,...T];return{hosts:E.length>0?E:u,getTimeout(p,A){return(T.length===0&&p===0?1:T.length+3+p)*A}}}async function d(u,l,f=!0){let T=[],E=oe(u,l),y=ae(s,u.headers,l.headers),p=u.method==="GET"?{...u.data,...l.data}:{},A={...i,...u.queryParameters,...p};if(m.value&&(A["x-algolia-agent"]=m.value),l&&l.queryParameters)for(let g of Object.keys(l.queryParameters))!l.queryParameters[g]||Object.prototype.toString.call(l.queryParameters[g])==="[object Object]"?A[g]=l.queryParameters[g]:A[g]=l.queryParameters[g].toString();let v=0,I=async(g,B)=>{let R=g.pop();if(R===void 0)throw new Z(me(T));let b={...h,...l.timeouts},_={data:E,headers:y,method:u.method,url:re(R,u.path,A),connectTimeout:B(v,b.connect),responseTimeout:B(v,f?b.read:b.write)},H=S=>{let L={request:_,response:S,host:R,triesLeft:g.length};return T.push(L),L},q=await n.send(_);if(ce(q)){let S=H(q);return q.isTimedOut&&v++,r.info("Retryable failure",M(S)),await e.set(R,$(R,q.isTimedOut?"timed out":"down")),I(g,B)}if(le(q))return ne(q);throw H(q),ie(q,T)},K=t.filter(g=>g.accept==="readWrite"||(f?g.accept==="read":g.accept==="write")),D=await c(K);return I([...D.hosts].reverse(),D.getTimeout)}function P(u,l={}){let f=u.useReadTransporter||u.method==="GET";if(!f)return d(u,l,f);let T=()=>d(u,l);if((l.cacheable||u.cacheable)!==!0)return T();let y={request:u,requestOptions:l,transporter:{queryParameters:i,headers:s}};return a.get(y,()=>o.get(y,()=>o.set(y,T()).then(p=>Promise.all([o.delete(y),p]),p=>Promise.all([o.delete(y),Promise.reject(p)])).then(([p,A])=>A)),{miss:p=>a.set(y,p)})}return{hostsCache:e,requester:n,timeouts:h,logger:r,algoliaAgent:m,baseHeaders:s,baseQueryParameters:i,hosts:t,request:P,requestsCache:o,responsesCache:a}}var O="5.30.0",N=["de","us"];function de(t){return[{url:t?"analytics.{region}.algolia.com".replace("{region}",t):"analytics.algolia.com",accept:"readWrite",protocol:"https"}]}function X({appId:t,apiKey:e,authMode:s,algoliaAgents:r,region:i,...m}){let h=Q(t,e,s),n=F({hosts:de(i),...m,algoliaAgent:W({algoliaAgents:r,client:"Abtesting",version:O}),baseHeaders:{"content-type":"text/plain",...h.headers(),...m.baseHeaders},baseQueryParameters:{...h.queryParameters(),...m.baseQueryParameters}});return{transporter:n,appId:t,apiKey:e,clearCache(){return Promise.all([n.requestsCache.clear(),n.responsesCache.clear()]).then(()=>{})},get _ua(){return n.algoliaAgent.value},addAlgoliaAgent(o,a){n.algoliaAgent.add({segment:o,version:a})},setClientApiKey({apiKey:o}){!s||s==="WithinHeaders"?n.baseHeaders["x-algolia-api-key"]=o:n.baseQueryParameters["x-algolia-api-key"]=o},addABTests(o,a){if(!o)throw new Error("Parameter `addABTestsRequest` is required when calling `addABTests`.");if(!o.name)throw new Error("Parameter `addABTestsRequest.name` is required when calling `addABTests`.");if(!o.variants)throw new Error("Parameter `addABTestsRequest.variants` is required when calling `addABTests`.");if(!o.endAt)throw new Error("Parameter `addABTestsRequest.endAt` is required when calling `addABTests`.");let u={method:"POST",path:"/2/abtests",queryParameters:{},headers:{},data:o};return n.request(u,a)},customDelete({path:o,parameters:a},c){if(!o)throw new Error("Parameter `path` is required when calling `customDelete`.");let l={method:"DELETE",path:"/{path}".replace("{path}",o),queryParameters:a||{},headers:{}};return n.request(l,c)},customGet({path:o,parameters:a},c){if(!o)throw new Error("Parameter `path` is required when calling `customGet`.");let l={method:"GET",path:"/{path}".replace("{path}",o),queryParameters:a||{},headers:{}};return n.request(l,c)},customPost({path:o,parameters:a,body:c},d){if(!o)throw new Error("Parameter `path` is required when calling `customPost`.");let f={method:"POST",path:"/{path}".replace("{path}",o),queryParameters:a||{},headers:{},data:c||{}};return n.request(f,d)},customPut({path:o,parameters:a,body:c},d){if(!o)throw new Error("Parameter `path` is required when calling `customPut`.");let f={method:"PUT",path:"/{path}".replace("{path}",o),queryParameters:a||{},headers:{},data:c||{}};return n.request(f,d)},deleteABTest({id:o},a){if(!o)throw new Error("Parameter `id` is required when calling `deleteABTest`.");let u={method:"DELETE",path:"/2/abtests/{id}".replace("{id}",encodeURIComponent(o)),queryParameters:{},headers:{}};return n.request(u,a)},estimateABTest(o,a){if(!o)throw new Error("Parameter `estimateABTestRequest` is required when calling `estimateABTest`.");if(!o.configuration)throw new Error("Parameter `estimateABTestRequest.configuration` is required when calling `estimateABTest`.");if(!o.variants)throw new Error("Parameter `estimateABTestRequest.variants` is required when calling `estimateABTest`.");let u={method:"POST",path:"/2/abtests/estimate",queryParameters:{},headers:{},data:o};return n.request(u,a)},getABTest({id:o},a){if(!o)throw new Error("Parameter `id` is required when calling `getABTest`.");let u={method:"GET",path:"/2/abtests/{id}".replace("{id}",encodeURIComponent(o)),queryParameters:{},headers:{}};return n.request(u,a)},listABTests({offset:o,limit:a,indexPrefix:c,indexSuffix:d}={},P=void 0){let u="/2/abtests",l={},f={};o!==void 0&&(f.offset=o.toString()),a!==void 0&&(f.limit=a.toString()),c!==void 0&&(f.indexPrefix=c.toString()),d!==void 0&&(f.indexSuffix=d.toString());let T={method:"GET",path:u,queryParameters:f,headers:l};return n.request(T,P)},scheduleABTest(o,a){if(!o)throw new Error("Parameter `scheduleABTestsRequest` is required when calling `scheduleABTest`.");if(!o.name)throw new Error("Parameter `scheduleABTestsRequest.name` is required when calling `scheduleABTest`.");if(!o.variants)throw new Error("Parameter `scheduleABTestsRequest.variants` is required when calling `scheduleABTest`.");if(!o.scheduledAt)throw new Error("Parameter `scheduleABTestsRequest.scheduledAt` is required when calling `scheduleABTest`.");if(!o.endAt)throw new Error("Parameter `scheduleABTestsRequest.endAt` is required when calling `scheduleABTest`.");let u={method:"POST",path:"/2/abtests/schedule",queryParameters:{},headers:{},data:o};return n.request(u,a)},stopABTest({id:o},a){if(!o)throw new Error("Parameter `id` is required when calling `stopABTest`.");let u={method:"POST",path:"/2/abtests/{id}/stop".replace("{id}",encodeURIComponent(o)),queryParameters:{},headers:{}};return n.request(u,a)}}}function Ze(t,e,s,r){if(!t||typeof t!="string")throw new Error("`appId` is missing.");if(!e||typeof e!="string")throw new Error("`apiKey` is missing.");if(s&&(typeof s!="string"||!N.includes(s)))throw new Error(`\`region\` must be one of the following: ${N.join(", ")}`);return X({appId:t,apiKey:e,region:s,timeouts:{connect:1e3,read:2e3,write:3e4},logger:J(),requester:k(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:x(),requestsCache:x({serializable:!1}),hostsCache:w({caches:[j({key:`${O}-${t}`}),x()]}),...r})}export{Ze as abtestingClient,O as apiClientVersion};
//# sourceMappingURL=browser.min.js.map