{"version": 3, "sources": ["../../builds/fetch.ts", "../../src/abtestingClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type AbtestingClient = ReturnType<typeof createAbtestingClient>;\n\nimport { createMemoryCache, createNullCache, createNullLogger } from '@algolia/client-common';\nimport { createFetchRequester } from '@algolia/requester-fetch';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { createAbtestingClient } from '../src/abtestingClient';\n\nimport type { Region } from '../src/abtestingClient';\nimport { REGIONS } from '../src/abtestingClient';\n\nexport type { Region, RegionOptions } from '../src/abtestingClient';\n\nexport { apiClientVersion } from '../src/abtestingClient';\n\nexport * from '../model';\n\nexport function abtestingClient(\n  appId: string,\n  apiKey: string,\n  region?: Region | undefined,\n  options?: ClientOptions | undefined,\n): AbtestingClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (region && (typeof region !== 'string' || !REGIONS.includes(region))) {\n    throw new Error(`\\`region\\` must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return {\n    ...createAbtestingClient({\n      appId,\n      apiKey,\n      region,\n      timeouts: {\n        connect: 2000,\n        read: 5000,\n        write: 30000,\n      },\n      logger: createNullLogger(),\n      requester: createFetchRequester(),\n      algoliaAgents: [{ segment: 'Fetch' }],\n      responsesCache: createNullCache(),\n      requestsCache: createNullCache(),\n      hostsCache: createMemoryCache(),\n      ...options,\n    }),\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { ABTest } from '../model/aBTest';\nimport type { ABTestResponse } from '../model/aBTestResponse';\nimport type { AddABTestsRequest } from '../model/addABTestsRequest';\n\nimport type { EstimateABTestRequest } from '../model/estimateABTestRequest';\nimport type { EstimateABTestResponse } from '../model/estimateABTestResponse';\nimport type { ListABTestsResponse } from '../model/listABTestsResponse';\nimport type { ScheduleABTestResponse } from '../model/scheduleABTestResponse';\nimport type { ScheduleABTestsRequest } from '../model/scheduleABTestsRequest';\n\nimport type {\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteABTestProps,\n  GetABTestProps,\n  ListABTestsProps,\n  StopABTestProps,\n} from '../model/clientMethodProps';\n\nexport const apiClientVersion = '5.30.0';\n\nexport const REGIONS = ['de', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region?: Region | undefined };\n\nfunction getDefaultHosts(region?: Region | undefined): Host[] {\n  const url = !region ? 'analytics.algolia.com' : 'analytics.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\nexport function createAbtestingClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Abtesting',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Creates a new A/B test.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param addABTestsRequest - The addABTestsRequest object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    addABTests(addABTestsRequest: AddABTestsRequest, requestOptions?: RequestOptions): Promise<ABTestResponse> {\n      if (!addABTestsRequest) {\n        throw new Error('Parameter `addABTestsRequest` is required when calling `addABTests`.');\n      }\n\n      if (!addABTestsRequest.name) {\n        throw new Error('Parameter `addABTestsRequest.name` is required when calling `addABTests`.');\n      }\n      if (!addABTestsRequest.variants) {\n        throw new Error('Parameter `addABTestsRequest.variants` is required when calling `addABTests`.');\n      }\n      if (!addABTestsRequest.endAt) {\n        throw new Error('Parameter `addABTestsRequest.endAt` is required when calling `addABTests`.');\n      }\n\n      const requestPath = '/2/abtests';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: addABTestsRequest,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes an A/B test by its ID.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteABTest - The deleteABTest object.\n     * @param deleteABTest.id - Unique A/B test identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteABTest({ id }: DeleteABTestProps, requestOptions?: RequestOptions): Promise<ABTestResponse> {\n      if (!id) {\n        throw new Error('Parameter `id` is required when calling `deleteABTest`.');\n      }\n\n      const requestPath = '/2/abtests/{id}'.replace('{id}', encodeURIComponent(id));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Given the traffic percentage and the expected effect size, this endpoint estimates the sample size and duration of an A/B test based on historical traffic.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param estimateABTestRequest - The estimateABTestRequest object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    estimateABTest(\n      estimateABTestRequest: EstimateABTestRequest,\n      requestOptions?: RequestOptions,\n    ): Promise<EstimateABTestResponse> {\n      if (!estimateABTestRequest) {\n        throw new Error('Parameter `estimateABTestRequest` is required when calling `estimateABTest`.');\n      }\n\n      if (!estimateABTestRequest.configuration) {\n        throw new Error('Parameter `estimateABTestRequest.configuration` is required when calling `estimateABTest`.');\n      }\n      if (!estimateABTestRequest.variants) {\n        throw new Error('Parameter `estimateABTestRequest.variants` is required when calling `estimateABTest`.');\n      }\n\n      const requestPath = '/2/abtests/estimate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: estimateABTestRequest,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the details for an A/B test by its ID.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getABTest - The getABTest object.\n     * @param getABTest.id - Unique A/B test identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getABTest({ id }: GetABTestProps, requestOptions?: RequestOptions): Promise<ABTest> {\n      if (!id) {\n        throw new Error('Parameter `id` is required when calling `getABTest`.');\n      }\n\n      const requestPath = '/2/abtests/{id}'.replace('{id}', encodeURIComponent(id));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists all A/B tests you configured for this application.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param listABTests - The listABTests object.\n     * @param listABTests.offset - Position of the first item to return.\n     * @param listABTests.limit - Number of items to return.\n     * @param listABTests.indexPrefix - Index name prefix. Only A/B tests for indices starting with this string are included in the response.\n     * @param listABTests.indexSuffix - Index name suffix. Only A/B tests for indices ending with this string are included in the response.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listABTests(\n      { offset, limit, indexPrefix, indexSuffix }: ListABTestsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListABTestsResponse> {\n      const requestPath = '/2/abtests';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (indexPrefix !== undefined) {\n        queryParameters['indexPrefix'] = indexPrefix.toString();\n      }\n\n      if (indexSuffix !== undefined) {\n        queryParameters['indexSuffix'] = indexSuffix.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Schedule an A/B test to be started at a later time.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param scheduleABTestsRequest - The scheduleABTestsRequest object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    scheduleABTest(\n      scheduleABTestsRequest: ScheduleABTestsRequest,\n      requestOptions?: RequestOptions,\n    ): Promise<ScheduleABTestResponse> {\n      if (!scheduleABTestsRequest) {\n        throw new Error('Parameter `scheduleABTestsRequest` is required when calling `scheduleABTest`.');\n      }\n\n      if (!scheduleABTestsRequest.name) {\n        throw new Error('Parameter `scheduleABTestsRequest.name` is required when calling `scheduleABTest`.');\n      }\n      if (!scheduleABTestsRequest.variants) {\n        throw new Error('Parameter `scheduleABTestsRequest.variants` is required when calling `scheduleABTest`.');\n      }\n      if (!scheduleABTestsRequest.scheduledAt) {\n        throw new Error('Parameter `scheduleABTestsRequest.scheduledAt` is required when calling `scheduleABTest`.');\n      }\n      if (!scheduleABTestsRequest.endAt) {\n        throw new Error('Parameter `scheduleABTestsRequest.endAt` is required when calling `scheduleABTest`.');\n      }\n\n      const requestPath = '/2/abtests/schedule';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: scheduleABTestsRequest,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Stops an A/B test by its ID.  You can\\'t restart stopped A/B tests.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param stopABTest - The stopABTest object.\n     * @param stopABTest.id - Unique A/B test identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    stopABTest({ id }: StopABTestProps, requestOptions?: RequestOptions): Promise<ABTestResponse> {\n      if (!id) {\n        throw new Error('Parameter `id` is required when calling `stopABTest`.');\n      }\n\n      const requestPath = '/2/abtests/{id}/stop'.replace('{id}', encodeURIComponent(id));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";AAIA,SAAS,mBAAmB,iBAAiB,wBAAwB;AACrE,SAAS,4BAA4B;;;ACKrC,SAAS,YAAY,mBAAmB,uBAAuB;AAuBxD,IAAM,mBAAmB;AAEzB,IAAM,UAAU,CAAC,MAAM,IAAI;AAIlC,SAAS,gBAAgB,QAAqC;AAC5D,QAAM,MAAM,CAAC,SAAS,0BAA0B,iCAAiC,QAAQ,YAAY,MAAM;AAE3G,SAAO,CAAC,EAAE,KAAK,QAAQ,aAAa,UAAU,QAAQ,CAAC;AACzD;AAEO,SAAS,sBAAsB;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL,GAAwC;AACtC,QAAM,OAAO,WAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,cAAc,kBAAkB;AAAA,IACpC,OAAO,gBAAgB,YAAY;AAAA,IACnC,GAAG;AAAA,IACH,cAAc,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WAAW,mBAAsC,gBAA0D;AACzG,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,UAAI,CAAC,kBAAkB,MAAM;AAC3B,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC7F;AACA,UAAI,CAAC,kBAAkB,UAAU;AAC/B,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AACA,UAAI,CAAC,kBAAkB,OAAO;AAC5B,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aAAa,EAAE,GAAG,GAAsB,gBAA0D;AAChG,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,kBAAkB,QAAQ,QAAQ,mBAAmB,EAAE,CAAC;AAC5E,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,eACE,uBACA,gBACiC;AACjC,UAAI,CAAC,uBAAuB;AAC1B,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,UAAI,CAAC,sBAAsB,eAAe;AACxC,cAAM,IAAI,MAAM,4FAA4F;AAAA,MAC9G;AACA,UAAI,CAAC,sBAAsB,UAAU;AACnC,cAAM,IAAI,MAAM,uFAAuF;AAAA,MACzG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,UAAU,EAAE,GAAG,GAAmB,gBAAkD;AAClF,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACxE;AAEA,YAAM,cAAc,kBAAkB,QAAQ,QAAQ,mBAAmB,EAAE,CAAC;AAC5E,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,YACE,EAAE,QAAQ,OAAO,aAAa,YAAY,IAAsB,CAAC,GACjE,iBAA6C,QACf;AAC9B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,eACE,wBACA,gBACiC;AACjC,UAAI,CAAC,wBAAwB;AAC3B,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,UAAI,CAAC,uBAAuB,MAAM;AAChC,cAAM,IAAI,MAAM,oFAAoF;AAAA,MACtG;AACA,UAAI,CAAC,uBAAuB,UAAU;AACpC,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AACA,UAAI,CAAC,uBAAuB,aAAa;AACvC,cAAM,IAAI,MAAM,2FAA2F;AAAA,MAC7G;AACA,UAAI,CAAC,uBAAuB,OAAO;AACjC,cAAM,IAAI,MAAM,qFAAqF;AAAA,MACvG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,WAAW,EAAE,GAAG,GAAoB,gBAA0D;AAC5F,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,YAAM,cAAc,uBAAuB,QAAQ,QAAQ,mBAAmB,EAAE,CAAC;AACjF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;ADvdO,SAAS,gBACd,OACA,QACA,QACA,SACiB;AACjB,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,MAAI,WAAW,OAAO,WAAW,YAAY,CAAC,QAAQ,SAAS,MAAM,IAAI;AACvE,UAAM,IAAI,MAAM,4CAA4C,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,EAClF;AAEA,SAAO;AAAA,IACL,GAAG,sBAAsB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,WAAW,qBAAqB;AAAA,MAChC,eAAe,CAAC,EAAE,SAAS,QAAQ,CAAC;AAAA,MACpC,gBAAgB,gBAAgB;AAAA,MAChC,eAAe,gBAAgB;AAAA,MAC/B,YAAY,kBAAkB;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;", "names": []}