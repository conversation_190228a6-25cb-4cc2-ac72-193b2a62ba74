{"version": 3, "sources": ["../../builds/fetch.ts", "../../src/ingestionClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type IngestionClient = ReturnType<typeof createIngestionClient>;\n\nimport { createMemoryCache, createNullCache, createNullLogger } from '@algolia/client-common';\nimport { createFetchRequester } from '@algolia/requester-fetch';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { createIngestionClient } from '../src/ingestionClient';\n\nimport type { Region } from '../src/ingestionClient';\nimport { REGIONS } from '../src/ingestionClient';\n\nexport type { Region, RegionOptions } from '../src/ingestionClient';\n\nexport { apiClientVersion, isOnDemandTrigger, isScheduleTrigger, isSubscriptionTrigger } from '../src/ingestionClient';\n\nexport * from '../model';\n\nexport function ingestionClient(\n  appId: string,\n  apiKey: string,\n  region: Region,\n  options?: ClientOptions | undefined,\n): IngestionClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (!region || (region && (typeof region !== 'string' || !REGIONS.includes(region)))) {\n    throw new Error(`\\`region\\` is required and must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return {\n    ...createIngestionClient({\n      appId,\n      apiKey,\n      region,\n      timeouts: {\n        connect: 25000,\n        read: 25000,\n        write: 25000,\n      },\n      logger: createNullLogger(),\n      requester: createFetchRequester(),\n      algoliaAgents: [{ segment: 'Fetch' }],\n      responsesCache: createNullCache(),\n      requestsCache: createNullCache(),\n      hostsCache: createMemoryCache(),\n      ...options,\n    }),\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  ApiError,\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createIterablePromise, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { Authentication } from '../model/authentication';\nimport type { AuthenticationCreate } from '../model/authenticationCreate';\nimport type { AuthenticationCreateResponse } from '../model/authenticationCreateResponse';\nimport type { AuthenticationSearch } from '../model/authenticationSearch';\n\nimport type { AuthenticationUpdateResponse } from '../model/authenticationUpdateResponse';\nimport type { DeleteResponse } from '../model/deleteResponse';\nimport type { Destination } from '../model/destination';\nimport type { DestinationCreate } from '../model/destinationCreate';\nimport type { DestinationCreateResponse } from '../model/destinationCreateResponse';\nimport type { DestinationSearch } from '../model/destinationSearch';\n\nimport type { DestinationUpdateResponse } from '../model/destinationUpdateResponse';\n\nimport type { Event } from '../model/event';\n\nimport type { ListAuthenticationsResponse } from '../model/listAuthenticationsResponse';\nimport type { ListDestinationsResponse } from '../model/listDestinationsResponse';\nimport type { ListEventsResponse } from '../model/listEventsResponse';\nimport type { ListSourcesResponse } from '../model/listSourcesResponse';\nimport type { ListTasksResponse } from '../model/listTasksResponse';\nimport type { ListTasksResponseV1 } from '../model/listTasksResponseV1';\nimport type { ListTransformationsResponse } from '../model/listTransformationsResponse';\n\nimport type { Run } from '../model/run';\nimport type { RunListResponse } from '../model/runListResponse';\nimport type { RunResponse } from '../model/runResponse';\n\nimport type { RunSourceResponse } from '../model/runSourceResponse';\n\nimport type { Source } from '../model/source';\nimport type { SourceCreate } from '../model/sourceCreate';\nimport type { SourceCreateResponse } from '../model/sourceCreateResponse';\nimport type { SourceSearch } from '../model/sourceSearch';\n\nimport type { SourceUpdateResponse } from '../model/sourceUpdateResponse';\nimport type { Task } from '../model/task';\nimport type { TaskCreate } from '../model/taskCreate';\nimport type { TaskCreateResponse } from '../model/taskCreateResponse';\nimport type { TaskCreateV1 } from '../model/taskCreateV1';\nimport type { TaskSearch } from '../model/taskSearch';\n\nimport type { TaskUpdateResponse } from '../model/taskUpdateResponse';\n\nimport type { TaskV1 } from '../model/taskV1';\nimport type { Transformation } from '../model/transformation';\nimport type { TransformationCreate } from '../model/transformationCreate';\nimport type { TransformationCreateResponse } from '../model/transformationCreateResponse';\nimport type { TransformationSearch } from '../model/transformationSearch';\n\nimport type { TransformationTry } from '../model/transformationTry';\nimport type { TransformationTryResponse } from '../model/transformationTryResponse';\nimport type { TransformationUpdateResponse } from '../model/transformationUpdateResponse';\n\nimport type { WatchResponse } from '../model/watchResponse';\n\nimport type {\n  ChunkedPushOptions,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteAuthenticationProps,\n  DeleteDestinationProps,\n  DeleteSourceProps,\n  DeleteTaskProps,\n  DeleteTaskV1Props,\n  DeleteTransformationProps,\n  DisableTaskProps,\n  DisableTaskV1Props,\n  EnableTaskProps,\n  EnableTaskV1Props,\n  GetAuthenticationProps,\n  GetDestinationProps,\n  GetEventProps,\n  GetRunProps,\n  GetSourceProps,\n  GetTaskProps,\n  GetTaskV1Props,\n  GetTransformationProps,\n  ListAuthenticationsProps,\n  ListDestinationsProps,\n  ListEventsProps,\n  ListRunsProps,\n  ListSourcesProps,\n  ListTasksProps,\n  ListTasksV1Props,\n  ListTransformationsProps,\n  PushProps,\n  PushTaskProps,\n  RunSourceProps,\n  RunTaskProps,\n  RunTaskV1Props,\n  TriggerDockerSourceDiscoverProps,\n  TryTransformationBeforeUpdateProps,\n  UpdateAuthenticationProps,\n  UpdateDestinationProps,\n  UpdateSourceProps,\n  UpdateTaskProps,\n  UpdateTaskV1Props,\n  UpdateTransformationProps,\n  ValidateSourceBeforeUpdateProps,\n} from '../model/clientMethodProps';\n\nimport type { OnDemandTrigger } from '../model/onDemandTrigger';\nimport type { PushTaskRecords } from '../model/pushTaskRecords';\nimport type { ScheduleTrigger } from '../model/scheduleTrigger';\nimport type { SubscriptionTrigger } from '../model/subscriptionTrigger';\nimport type { TaskCreateTrigger } from '../model/taskCreateTrigger';\nimport type { Trigger } from '../model/trigger';\n\nexport const apiClientVersion = '1.30.0';\n\nexport const REGIONS = ['eu', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region: Region };\n\nfunction getDefaultHosts(region: Region): Host[] {\n  const url = 'data.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\n/**\n * Guard: Return strongly typed specific OnDemandTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific OnDemandTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isOnDemandTrigger(trigger: TaskCreateTrigger | Trigger): trigger is OnDemandTrigger {\n  return trigger.type === 'onDemand';\n}\n\n/**\n * Guard: Return strongly typed specific ScheduleTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific ScheduleTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isScheduleTrigger(trigger: TaskCreateTrigger | Trigger): trigger is ScheduleTrigger {\n  return trigger.type === 'schedule';\n}\n\n/**\n * Guard: Return strongly typed specific SubscriptionTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific SubscriptionTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isSubscriptionTrigger(trigger: TaskCreateTrigger | Trigger): trigger is SubscriptionTrigger {\n  return trigger.type === 'subscription';\n}\n\nexport function createIngestionClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Ingestion',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `push` requests by leveraging the Transformation pipeline setup in the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/).\n     *\n     * @summary Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     * @param chunkedPush - The `chunkedPush` object.\n     * @param chunkedPush.indexName - The `indexName` to replace `objects` in.\n     * @param chunkedPush.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param chunkedPush.action - The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.\n     * @param chunkedPush.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param chunkedPush.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param chunkedPush.referenceIndexName - This is required when targeting an index that does not have a push connector setup (e.g. a tmp index), but you wish to attach another index's transformation to it (e.g. the source index name).\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getEvent` method and merged with the transporter requestOptions.\n     */\n    async chunkedPush(\n      {\n        indexName,\n        objects,\n        action = 'addObject',\n        waitForTasks,\n        batchSize = 1000,\n        referenceIndexName,\n      }: ChunkedPushOptions,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<WatchResponse>> {\n      let records: Array<PushTaskRecords> = [];\n      const responses: Array<WatchResponse> = [];\n\n      const objectEntries = objects.entries();\n      for (const [i, obj] of objectEntries) {\n        records.push(obj as PushTaskRecords);\n        if (records.length === batchSize || i === objects.length - 1) {\n          responses.push(\n            await this.push({ indexName, pushTaskPayload: { action, records }, referenceIndexName }, requestOptions),\n          );\n          records = [];\n        }\n      }\n\n      let retryCount = 0;\n\n      if (waitForTasks) {\n        for (const resp of responses) {\n          if (!resp.eventID) {\n            throw new Error('received unexpected response from the push endpoint, eventID must not be undefined');\n          }\n\n          await createIterablePromise({\n            func: async () => {\n              if (resp.eventID === undefined || !resp.eventID) {\n                throw new Error('received unexpected response from the push endpoint, eventID must not be undefined');\n              }\n\n              return this.getEvent({ runID: resp.runID, eventID: resp.eventID }).catch((error: ApiError) => {\n                if (error.status === 404) {\n                  return undefined;\n                }\n\n                throw error;\n              });\n            },\n            validate: (response) => response !== undefined,\n            aggregator: () => (retryCount += 1),\n            error: {\n              validate: () => retryCount >= 50,\n              message: () => `The maximum number of retries exceeded. (${retryCount}/${50})`,\n            },\n            timeout: (): number => Math.min(retryCount * 500, 5000),\n          });\n        }\n      }\n\n      return responses;\n    },\n\n    /**\n     * Creates a new authentication resource.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param authenticationCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createAuthentication(\n      authenticationCreate: AuthenticationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<AuthenticationCreateResponse> {\n      if (!authenticationCreate) {\n        throw new Error('Parameter `authenticationCreate` is required when calling `createAuthentication`.');\n      }\n\n      if (!authenticationCreate.type) {\n        throw new Error('Parameter `authenticationCreate.type` is required when calling `createAuthentication`.');\n      }\n      if (!authenticationCreate.name) {\n        throw new Error('Parameter `authenticationCreate.name` is required when calling `createAuthentication`.');\n      }\n      if (!authenticationCreate.input) {\n        throw new Error('Parameter `authenticationCreate.input` is required when calling `createAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new destination.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param destinationCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createDestination(\n      destinationCreate: DestinationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<DestinationCreateResponse> {\n      if (!destinationCreate) {\n        throw new Error('Parameter `destinationCreate` is required when calling `createDestination`.');\n      }\n\n      if (!destinationCreate.type) {\n        throw new Error('Parameter `destinationCreate.type` is required when calling `createDestination`.');\n      }\n      if (!destinationCreate.name) {\n        throw new Error('Parameter `destinationCreate.name` is required when calling `createDestination`.');\n      }\n      if (!destinationCreate.input) {\n        throw new Error('Parameter `destinationCreate.input` is required when calling `createDestination`.');\n      }\n\n      const requestPath = '/1/destinations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new source.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createSource(sourceCreate: SourceCreate, requestOptions?: RequestOptions): Promise<SourceCreateResponse> {\n      if (!sourceCreate) {\n        throw new Error('Parameter `sourceCreate` is required when calling `createSource`.');\n      }\n\n      if (!sourceCreate.type) {\n        throw new Error('Parameter `sourceCreate.type` is required when calling `createSource`.');\n      }\n      if (!sourceCreate.name) {\n        throw new Error('Parameter `sourceCreate.name` is required when calling `createSource`.');\n      }\n\n      const requestPath = '/1/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new task.\n     * @param taskCreate - Request body for creating a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTask(taskCreate: TaskCreate, requestOptions?: RequestOptions): Promise<TaskCreateResponse> {\n      if (!taskCreate) {\n        throw new Error('Parameter `taskCreate` is required when calling `createTask`.');\n      }\n\n      if (!taskCreate.sourceID) {\n        throw new Error('Parameter `taskCreate.sourceID` is required when calling `createTask`.');\n      }\n      if (!taskCreate.destinationID) {\n        throw new Error('Parameter `taskCreate.destinationID` is required when calling `createTask`.');\n      }\n      if (!taskCreate.action) {\n        throw new Error('Parameter `taskCreate.action` is required when calling `createTask`.');\n      }\n\n      const requestPath = '/2/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new task using the v1 endpoint, please use `createTask` instead.\n     *\n     * @deprecated\n     * @param taskCreate - Request body for creating a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTaskV1(taskCreate: TaskCreateV1, requestOptions?: RequestOptions): Promise<TaskCreateResponse> {\n      if (!taskCreate) {\n        throw new Error('Parameter `taskCreate` is required when calling `createTaskV1`.');\n      }\n\n      if (!taskCreate.sourceID) {\n        throw new Error('Parameter `taskCreate.sourceID` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.destinationID) {\n        throw new Error('Parameter `taskCreate.destinationID` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.trigger) {\n        throw new Error('Parameter `taskCreate.trigger` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.action) {\n        throw new Error('Parameter `taskCreate.action` is required when calling `createTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new transformation.\n     * @param transformationCreate - Request body for creating a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTransformation(\n      transformationCreate: TransformationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationCreateResponse> {\n      if (!transformationCreate) {\n        throw new Error('Parameter `transformationCreate` is required when calling `createTransformation`.');\n      }\n\n      if (!transformationCreate.name) {\n        throw new Error('Parameter `transformationCreate.name` is required when calling `createTransformation`.');\n      }\n\n      const requestPath = '/1/transformations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes an authentication resource. You can\\'t delete authentication resources that are used by a source or a destination.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteAuthentication - The deleteAuthentication object.\n     * @param deleteAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteAuthentication(\n      { authenticationID }: DeleteAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `deleteAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a destination by its ID. You can\\'t delete destinations that are referenced in tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteDestination - The deleteDestination object.\n     * @param deleteDestination.destinationID - Unique identifier of a destination.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteDestination(\n      { destinationID }: DeleteDestinationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `deleteDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a source by its ID. You can\\'t delete sources that are referenced in tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteSource - The deleteSource object.\n     * @param deleteSource.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSource({ sourceID }: DeleteSourceProps, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `deleteSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a task by its ID.\n     * @param deleteTask - The deleteTask object.\n     * @param deleteTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTask({ taskID }: DeleteTaskProps, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `deleteTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a task by its ID using the v1 endpoint, please use `deleteTask` instead.\n     *\n     * @deprecated\n     * @param deleteTaskV1 - The deleteTaskV1 object.\n     * @param deleteTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTaskV1({ taskID }: DeleteTaskV1Props, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `deleteTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a transformation by its ID.\n     * @param deleteTransformation - The deleteTransformation object.\n     * @param deleteTransformation.transformationID - Unique identifier of a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTransformation(\n      { transformationID }: DeleteTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `deleteTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Disables a task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param disableTask - The disableTask object.\n     * @param disableTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    disableTask({ taskID }: DisableTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `disableTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/disable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Disables a task using the v1 endpoint, please use `disableTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param disableTaskV1 - The disableTaskV1 object.\n     * @param disableTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    disableTaskV1({ taskID }: DisableTaskV1Props, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `disableTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/disable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Enables a task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param enableTask - The enableTask object.\n     * @param enableTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    enableTask({ taskID }: EnableTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `enableTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/enable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Enables a task using the v1 endpoint, please use `enableTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param enableTaskV1 - The enableTaskV1 object.\n     * @param enableTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    enableTaskV1({ taskID }: EnableTaskV1Props, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `enableTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/enable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves an authentication resource by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getAuthentication - The getAuthentication object.\n     * @param getAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAuthentication(\n      { authenticationID }: GetAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Authentication> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `getAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a destination by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getDestination - The getDestination object.\n     * @param getDestination.destinationID - Unique identifier of a destination.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDestination({ destinationID }: GetDestinationProps, requestOptions?: RequestOptions): Promise<Destination> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `getDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a single task run event by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getEvent - The getEvent object.\n     * @param getEvent.runID - Unique identifier of a task run.\n     * @param getEvent.eventID - Unique identifier of an event.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getEvent({ runID, eventID }: GetEventProps, requestOptions?: RequestOptions): Promise<Event> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `getEvent`.');\n      }\n\n      if (!eventID) {\n        throw new Error('Parameter `eventID` is required when calling `getEvent`.');\n      }\n\n      const requestPath = '/1/runs/{runID}/events/{eventID}'\n        .replace('{runID}', encodeURIComponent(runID))\n        .replace('{eventID}', encodeURIComponent(eventID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a single task run by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getRun - The getRun object.\n     * @param getRun.runID - Unique identifier of a task run.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRun({ runID }: GetRunProps, requestOptions?: RequestOptions): Promise<Run> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `getRun`.');\n      }\n\n      const requestPath = '/1/runs/{runID}'.replace('{runID}', encodeURIComponent(runID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a source by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getSource - The getSource object.\n     * @param getSource.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSource({ sourceID }: GetSourceProps, requestOptions?: RequestOptions): Promise<Source> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `getSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a task by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getTask - The getTask object.\n     * @param getTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTask({ taskID }: GetTaskProps, requestOptions?: RequestOptions): Promise<Task> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a task by its ID using the v1 endpoint, please use `getTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param getTaskV1 - The getTaskV1 object.\n     * @param getTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTaskV1({ taskID }: GetTaskV1Props, requestOptions?: RequestOptions): Promise<TaskV1> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a transformation by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getTransformation - The getTransformation object.\n     * @param getTransformation.transformationID - Unique identifier of a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTransformation(\n      { transformationID }: GetTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Transformation> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `getTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of all authentication resources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listAuthentications - The listAuthentications object.\n     * @param listAuthentications.itemsPerPage - Number of items per page.\n     * @param listAuthentications.page - Page number of the paginated API response.\n     * @param listAuthentications.type - Type of authentication resource to retrieve.\n     * @param listAuthentications.platform - Ecommerce platform for which to retrieve authentications.\n     * @param listAuthentications.sort - Property by which to sort the list of authentications.\n     * @param listAuthentications.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listAuthentications(\n      { itemsPerPage, page, type, platform, sort, order }: ListAuthenticationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListAuthenticationsResponse> {\n      const requestPath = '/1/authentications';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (platform !== undefined) {\n        queryParameters['platform'] = platform.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of destinations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listDestinations - The listDestinations object.\n     * @param listDestinations.itemsPerPage - Number of items per page.\n     * @param listDestinations.page - Page number of the paginated API response.\n     * @param listDestinations.type - Destination type.\n     * @param listDestinations.authenticationID - Authentication ID used by destinations.\n     * @param listDestinations.transformationID - Get the list of destinations used by a transformation.\n     * @param listDestinations.sort - Property by which to sort the destinations.\n     * @param listDestinations.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listDestinations(\n      { itemsPerPage, page, type, authenticationID, transformationID, sort, order }: ListDestinationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListDestinationsResponse> {\n      const requestPath = '/1/destinations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (authenticationID !== undefined) {\n        queryParameters['authenticationID'] = authenticationID.toString();\n      }\n\n      if (transformationID !== undefined) {\n        queryParameters['transformationID'] = transformationID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of events for a task run, identified by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listEvents - The listEvents object.\n     * @param listEvents.runID - Unique identifier of a task run.\n     * @param listEvents.itemsPerPage - Number of items per page.\n     * @param listEvents.page - Page number of the paginated API response.\n     * @param listEvents.status - Event status for filtering the list of task runs.\n     * @param listEvents.type - Event type for filtering the list of task runs.\n     * @param listEvents.sort - Property by which to sort the list of task run events.\n     * @param listEvents.order - Sort order of the response, ascending or descending.\n     * @param listEvents.startDate - Date and time in RFC 3339 format for the earliest events to retrieve. By default, the current time minus three hours is used.\n     * @param listEvents.endDate - Date and time in RFC 3339 format for the latest events to retrieve. By default, the current time is used.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listEvents(\n      { runID, itemsPerPage, page, status, type, sort, order, startDate, endDate }: ListEventsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<ListEventsResponse> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `listEvents`.');\n      }\n\n      const requestPath = '/1/runs/{runID}/events'.replace('{runID}', encodeURIComponent(runID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (status !== undefined) {\n        queryParameters['status'] = status.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a list of task runs.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listRuns - The listRuns object.\n     * @param listRuns.itemsPerPage - Number of items per page.\n     * @param listRuns.page - Page number of the paginated API response.\n     * @param listRuns.status - Run status for filtering the list of task runs.\n     * @param listRuns.type - Run type for filtering the list of task runs.\n     * @param listRuns.taskID - Task ID for filtering the list of task runs.\n     * @param listRuns.sort - Property by which to sort the list of task runs.\n     * @param listRuns.order - Sort order of the response, ascending or descending.\n     * @param listRuns.startDate - Date in RFC 3339 format for the earliest run to retrieve. By default, the current day minus seven days is used.\n     * @param listRuns.endDate - Date in RFC 3339 format for the latest run to retrieve. By default, the current day is used.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listRuns(\n      { itemsPerPage, page, status, type, taskID, sort, order, startDate, endDate }: ListRunsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<RunListResponse> {\n      const requestPath = '/1/runs';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (status !== undefined) {\n        queryParameters['status'] = status.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (taskID !== undefined) {\n        queryParameters['taskID'] = taskID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of sources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listSources - The listSources object.\n     * @param listSources.itemsPerPage - Number of items per page.\n     * @param listSources.page - Page number of the paginated API response.\n     * @param listSources.type - Source type. Some sources require authentication.\n     * @param listSources.authenticationID - Authentication IDs of the sources to retrieve. \\'none\\' returns sources that doesn\\'t have an authentication.\n     * @param listSources.sort - Property by which to sort the list of sources.\n     * @param listSources.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listSources(\n      { itemsPerPage, page, type, authenticationID, sort, order }: ListSourcesProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListSourcesResponse> {\n      const requestPath = '/1/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (authenticationID !== undefined) {\n        queryParameters['authenticationID'] = authenticationID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listTasks - The listTasks object.\n     * @param listTasks.itemsPerPage - Number of items per page.\n     * @param listTasks.page - Page number of the paginated API response.\n     * @param listTasks.action - Actions for filtering the list of tasks.\n     * @param listTasks.enabled - Whether to filter the list of tasks by the `enabled` status.\n     * @param listTasks.sourceID - Source IDs for filtering the list of tasks.\n     * @param listTasks.sourceType - Filters the tasks with the specified source type.\n     * @param listTasks.destinationID - Destination IDs for filtering the list of tasks.\n     * @param listTasks.triggerType - Type of task trigger for filtering the list of tasks.\n     * @param listTasks.withEmailNotifications - If specified, the response only includes tasks with notifications.email.enabled set to this value.\n     * @param listTasks.sort - Property by which to sort the list of tasks.\n     * @param listTasks.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTasks(\n      {\n        itemsPerPage,\n        page,\n        action,\n        enabled,\n        sourceID,\n        sourceType,\n        destinationID,\n        triggerType,\n        withEmailNotifications,\n        sort,\n        order,\n      }: ListTasksProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTasksResponse> {\n      const requestPath = '/2/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (action !== undefined) {\n        queryParameters['action'] = action.toString();\n      }\n\n      if (enabled !== undefined) {\n        queryParameters['enabled'] = enabled.toString();\n      }\n\n      if (sourceID !== undefined) {\n        queryParameters['sourceID'] = sourceID.toString();\n      }\n\n      if (sourceType !== undefined) {\n        queryParameters['sourceType'] = sourceType.toString();\n      }\n\n      if (destinationID !== undefined) {\n        queryParameters['destinationID'] = destinationID.toString();\n      }\n\n      if (triggerType !== undefined) {\n        queryParameters['triggerType'] = triggerType.toString();\n      }\n\n      if (withEmailNotifications !== undefined) {\n        queryParameters['withEmailNotifications'] = withEmailNotifications.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of tasks using the v1 endpoint, please use `getTasks` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param listTasksV1 - The listTasksV1 object.\n     * @param listTasksV1.itemsPerPage - Number of items per page.\n     * @param listTasksV1.page - Page number of the paginated API response.\n     * @param listTasksV1.action - Actions for filtering the list of tasks.\n     * @param listTasksV1.enabled - Whether to filter the list of tasks by the `enabled` status.\n     * @param listTasksV1.sourceID - Source IDs for filtering the list of tasks.\n     * @param listTasksV1.destinationID - Destination IDs for filtering the list of tasks.\n     * @param listTasksV1.triggerType - Type of task trigger for filtering the list of tasks.\n     * @param listTasksV1.sort - Property by which to sort the list of tasks.\n     * @param listTasksV1.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTasksV1(\n      { itemsPerPage, page, action, enabled, sourceID, destinationID, triggerType, sort, order }: ListTasksV1Props = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTasksResponseV1> {\n      const requestPath = '/1/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (action !== undefined) {\n        queryParameters['action'] = action.toString();\n      }\n\n      if (enabled !== undefined) {\n        queryParameters['enabled'] = enabled.toString();\n      }\n\n      if (sourceID !== undefined) {\n        queryParameters['sourceID'] = sourceID.toString();\n      }\n\n      if (destinationID !== undefined) {\n        queryParameters['destinationID'] = destinationID.toString();\n      }\n\n      if (triggerType !== undefined) {\n        queryParameters['triggerType'] = triggerType.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of transformations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listTransformations - The listTransformations object.\n     * @param listTransformations.itemsPerPage - Number of items per page.\n     * @param listTransformations.page - Page number of the paginated API response.\n     * @param listTransformations.sort - Property by which to sort the list of transformations.\n     * @param listTransformations.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTransformations(\n      { itemsPerPage, page, sort, order }: ListTransformationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTransformationsResponse> {\n      const requestPath = '/1/transformations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Pushes records through the Pipeline, directly to an index. You can make the call synchronous by providing the `watch` parameter, for asynchronous calls, you can use the observability endpoints and/or debugger dashboard to see the status of your task. If you want to leverage the [pre-indexing data transformation](https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/how-to/transform-your-data/), this is the recommended way of ingesting your records. This method is similar to `pushTask`, but requires an `indexName` instead of a `taskID`. If zero or many tasks are found, an error will be returned.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param push - The push object.\n     * @param push.indexName - Name of the index on which to perform the operation.\n     * @param push.pushTaskPayload - The pushTaskPayload object.\n     * @param push.watch - When provided, the push operation will be synchronous and the API will wait for the ingestion to be finished before responding.\n     * @param push.referenceIndexName - This is required when targeting an index that does not have a push connector setup (e.g. a tmp index), but you wish to attach another index\\'s transformation to it (e.g. the source index name).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    push(\n      { indexName, pushTaskPayload, watch, referenceIndexName }: PushProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `push`.');\n      }\n\n      if (!pushTaskPayload) {\n        throw new Error('Parameter `pushTaskPayload` is required when calling `push`.');\n      }\n\n      if (!pushTaskPayload.action) {\n        throw new Error('Parameter `pushTaskPayload.action` is required when calling `push`.');\n      }\n      if (!pushTaskPayload.records) {\n        throw new Error('Parameter `pushTaskPayload.records` is required when calling `push`.');\n      }\n\n      const requestPath = '/1/push/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (watch !== undefined) {\n        queryParameters['watch'] = watch.toString();\n      }\n\n      if (referenceIndexName !== undefined) {\n        queryParameters['referenceIndexName'] = referenceIndexName.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: pushTaskPayload,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Pushes records through the Pipeline, directly to an index. You can make the call synchronous by providing the `watch` parameter, for asynchronous calls, you can use the observability endpoints and/or debugger dashboard to see the status of your task. If you want to leverage the [pre-indexing data transformation](https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/how-to/transform-your-data/), this is the recommended way of ingesting your records. This method is similar to `push`, but requires a `taskID` instead of a `indexName`, which is useful when many `destinations` target the same `indexName`.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param pushTask - The pushTask object.\n     * @param pushTask.taskID - Unique identifier of a task.\n     * @param pushTask.pushTaskPayload - The pushTaskPayload object.\n     * @param pushTask.watch - When provided, the push operation will be synchronous and the API will wait for the ingestion to be finished before responding.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    pushTask(\n      { taskID, pushTaskPayload, watch }: PushTaskProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `pushTask`.');\n      }\n\n      if (!pushTaskPayload) {\n        throw new Error('Parameter `pushTaskPayload` is required when calling `pushTask`.');\n      }\n\n      if (!pushTaskPayload.action) {\n        throw new Error('Parameter `pushTaskPayload.action` is required when calling `pushTask`.');\n      }\n      if (!pushTaskPayload.records) {\n        throw new Error('Parameter `pushTaskPayload.records` is required when calling `pushTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/push'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (watch !== undefined) {\n        queryParameters['watch'] = watch.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: pushTaskPayload,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs all tasks linked to a source, only available for Shopify sources. It will create 1 run per task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param runSource - The runSource object.\n     * @param runSource.sourceID - Unique identifier of a source.\n     * @param runSource.runSourcePayload -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runSource(\n      { sourceID, runSourcePayload }: RunSourceProps,\n      requestOptions?: RequestOptions,\n    ): Promise<RunSourceResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `runSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/run'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: runSourcePayload ? runSourcePayload : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs a task. You can check the status of task runs with the observability endpoints.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param runTask - The runTask object.\n     * @param runTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runTask({ taskID }: RunTaskProps, requestOptions?: RequestOptions): Promise<RunResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `runTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/run'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs a task using the v1 endpoint, please use `runTask` instead. You can check the status of task runs with the observability endpoints.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param runTaskV1 - The runTaskV1 object.\n     * @param runTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runTaskV1({ taskID }: RunTaskV1Props, requestOptions?: RequestOptions): Promise<RunResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `runTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/run'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for authentication resources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param authenticationSearch - The authenticationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchAuthentications(\n      authenticationSearch: AuthenticationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Authentication>> {\n      if (!authenticationSearch) {\n        throw new Error('Parameter `authenticationSearch` is required when calling `searchAuthentications`.');\n      }\n\n      if (!authenticationSearch.authenticationIDs) {\n        throw new Error(\n          'Parameter `authenticationSearch.authenticationIDs` is required when calling `searchAuthentications`.',\n        );\n      }\n\n      const requestPath = '/1/authentications/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for destinations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param destinationSearch - The destinationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchDestinations(\n      destinationSearch: DestinationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Destination>> {\n      if (!destinationSearch) {\n        throw new Error('Parameter `destinationSearch` is required when calling `searchDestinations`.');\n      }\n\n      if (!destinationSearch.destinationIDs) {\n        throw new Error('Parameter `destinationSearch.destinationIDs` is required when calling `searchDestinations`.');\n      }\n\n      const requestPath = '/1/destinations/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for sources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceSearch - The sourceSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSources(sourceSearch: SourceSearch, requestOptions?: RequestOptions): Promise<Array<Source>> {\n      if (!sourceSearch) {\n        throw new Error('Parameter `sourceSearch` is required when calling `searchSources`.');\n      }\n\n      if (!sourceSearch.sourceIDs) {\n        throw new Error('Parameter `sourceSearch.sourceIDs` is required when calling `searchSources`.');\n      }\n\n      const requestPath = '/1/sources/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param taskSearch - The taskSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTasks(taskSearch: TaskSearch, requestOptions?: RequestOptions): Promise<Array<Task>> {\n      if (!taskSearch) {\n        throw new Error('Parameter `taskSearch` is required when calling `searchTasks`.');\n      }\n\n      if (!taskSearch.taskIDs) {\n        throw new Error('Parameter `taskSearch.taskIDs` is required when calling `searchTasks`.');\n      }\n\n      const requestPath = '/2/tasks/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for tasks using the v1 endpoint, please use `searchTasks` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param taskSearch - The taskSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTasksV1(taskSearch: TaskSearch, requestOptions?: RequestOptions): Promise<Array<TaskV1>> {\n      if (!taskSearch) {\n        throw new Error('Parameter `taskSearch` is required when calling `searchTasksV1`.');\n      }\n\n      if (!taskSearch.taskIDs) {\n        throw new Error('Parameter `taskSearch.taskIDs` is required when calling `searchTasksV1`.');\n      }\n\n      const requestPath = '/1/tasks/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for transformations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param transformationSearch - The transformationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTransformations(\n      transformationSearch: TransformationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Transformation>> {\n      if (!transformationSearch) {\n        throw new Error('Parameter `transformationSearch` is required when calling `searchTransformations`.');\n      }\n\n      if (!transformationSearch.transformationIDs) {\n        throw new Error(\n          'Parameter `transformationSearch.transformationIDs` is required when calling `searchTransformations`.',\n        );\n      }\n\n      const requestPath = '/1/transformations/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Triggers a stream-listing request for a source. Triggering stream-listing requests only works with sources with `type: docker` and `imageType: airbyte`.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param triggerDockerSourceDiscover - The triggerDockerSourceDiscover object.\n     * @param triggerDockerSourceDiscover.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    triggerDockerSourceDiscover(\n      { sourceID }: TriggerDockerSourceDiscoverProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/discover'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Try a transformation before creating it.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param transformationTry - The transformationTry object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    tryTransformation(\n      transformationTry: TransformationTry,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationTryResponse> {\n      if (!transformationTry) {\n        throw new Error('Parameter `transformationTry` is required when calling `tryTransformation`.');\n      }\n\n      if (!transformationTry.sampleRecord) {\n        throw new Error('Parameter `transformationTry.sampleRecord` is required when calling `tryTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/try';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationTry,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Try a transformation before updating it.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param tryTransformationBeforeUpdate - The tryTransformationBeforeUpdate object.\n     * @param tryTransformationBeforeUpdate.transformationID - Unique identifier of a transformation.\n     * @param tryTransformationBeforeUpdate.transformationTry - The transformationTry object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    tryTransformationBeforeUpdate(\n      { transformationID, transformationTry }: TryTransformationBeforeUpdateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationTryResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.');\n      }\n\n      if (!transformationTry) {\n        throw new Error('Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.');\n      }\n\n      if (!transformationTry.sampleRecord) {\n        throw new Error(\n          'Parameter `transformationTry.sampleRecord` is required when calling `tryTransformationBeforeUpdate`.',\n        );\n      }\n\n      const requestPath = '/1/transformations/{transformationID}/try'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationTry,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates an authentication resource.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateAuthentication - The updateAuthentication object.\n     * @param updateAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param updateAuthentication.authenticationUpdate - The authenticationUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateAuthentication(\n      { authenticationID, authenticationUpdate }: UpdateAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<AuthenticationUpdateResponse> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `updateAuthentication`.');\n      }\n\n      if (!authenticationUpdate) {\n        throw new Error('Parameter `authenticationUpdate` is required when calling `updateAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates the destination by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateDestination - The updateDestination object.\n     * @param updateDestination.destinationID - Unique identifier of a destination.\n     * @param updateDestination.destinationUpdate - The destinationUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateDestination(\n      { destinationID, destinationUpdate }: UpdateDestinationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DestinationUpdateResponse> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `updateDestination`.');\n      }\n\n      if (!destinationUpdate) {\n        throw new Error('Parameter `destinationUpdate` is required when calling `updateDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a source by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateSource - The updateSource object.\n     * @param updateSource.sourceID - Unique identifier of a source.\n     * @param updateSource.sourceUpdate - The sourceUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateSource(\n      { sourceID, sourceUpdate }: UpdateSourceProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SourceUpdateResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `updateSource`.');\n      }\n\n      if (!sourceUpdate) {\n        throw new Error('Parameter `sourceUpdate` is required when calling `updateSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a task by its ID.\n     * @param updateTask - The updateTask object.\n     * @param updateTask.taskID - Unique identifier of a task.\n     * @param updateTask.taskUpdate - The taskUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTask({ taskID, taskUpdate }: UpdateTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `updateTask`.');\n      }\n\n      if (!taskUpdate) {\n        throw new Error('Parameter `taskUpdate` is required when calling `updateTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a task by its ID using the v1 endpoint, please use `updateTask` instead.\n     *\n     * @deprecated\n     * @param updateTaskV1 - The updateTaskV1 object.\n     * @param updateTaskV1.taskID - Unique identifier of a task.\n     * @param updateTaskV1.taskUpdate - The taskUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTaskV1(\n      { taskID, taskUpdate }: UpdateTaskV1Props,\n      requestOptions?: RequestOptions,\n    ): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `updateTaskV1`.');\n      }\n\n      if (!taskUpdate) {\n        throw new Error('Parameter `taskUpdate` is required when calling `updateTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a transformation by its ID.\n     * @param updateTransformation - The updateTransformation object.\n     * @param updateTransformation.transformationID - Unique identifier of a transformation.\n     * @param updateTransformation.transformationCreate - The transformationCreate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTransformation(\n      { transformationID, transformationCreate }: UpdateTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationUpdateResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `updateTransformation`.');\n      }\n\n      if (!transformationCreate) {\n        throw new Error('Parameter `transformationCreate` is required when calling `updateTransformation`.');\n      }\n\n      if (!transformationCreate.name) {\n        throw new Error('Parameter `transformationCreate.name` is required when calling `updateTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Validates a source payload to ensure it can be created and that the data source can be reached by Algolia.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    validateSource(\n      sourceCreate: SourceCreate,\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<WatchResponse> {\n      const requestPath = '/1/sources/validate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceCreate ? sourceCreate : {},\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Validates an update of a source payload to ensure it can be created and that the data source can be reached by Algolia.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param validateSourceBeforeUpdate - The validateSourceBeforeUpdate object.\n     * @param validateSourceBeforeUpdate.sourceID - Unique identifier of a source.\n     * @param validateSourceBeforeUpdate.sourceUpdate - The sourceUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    validateSourceBeforeUpdate(\n      { sourceID, sourceUpdate }: ValidateSourceBeforeUpdateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.');\n      }\n\n      if (!sourceUpdate) {\n        throw new Error('Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/validate'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceUpdate,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";AAIA,SAAS,mBAAmB,iBAAiB,wBAAwB;AACrE,SAAS,4BAA4B;;;ACMrC,SAAS,YAAY,uBAAuB,mBAAmB,uBAAuB;AAiH/E,IAAM,mBAAmB;AAEzB,IAAM,UAAU,CAAC,MAAM,IAAI;AAIlC,SAAS,gBAAgB,QAAwB;AAC/C,QAAM,MAAM,4BAA4B,QAAQ,YAAY,MAAM;AAElE,SAAO,CAAC,EAAE,KAAK,QAAQ,aAAa,UAAU,QAAQ,CAAC;AACzD;AAQO,SAAS,kBAAkB,SAAkE;AAClG,SAAO,QAAQ,SAAS;AAC1B;AAQO,SAAS,kBAAkB,SAAkE;AAClG,SAAO,QAAQ,SAAS;AAC1B;AAQO,SAAS,sBAAsB,SAAsE;AAC1G,SAAO,QAAQ,SAAS;AAC1B;AAEO,SAAS,sBAAsB;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL,GAAwC;AACtC,QAAM,OAAO,WAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,cAAc,kBAAkB;AAAA,IACpC,OAAO,gBAAgB,YAAY;AAAA,IACnC,GAAG;AAAA,IACH,cAAc,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,MAAM,YACJ;AAAA,MACE;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,IACF,GACA,gBAC+B;AAC/B,UAAI,UAAkC,CAAC;AACvC,YAAM,YAAkC,CAAC;AAEzC,YAAM,gBAAgB,QAAQ,QAAQ;AACtC,iBAAW,CAAC,GAAG,GAAG,KAAK,eAAe;AACpC,gBAAQ,KAAK,GAAsB;AACnC,YAAI,QAAQ,WAAW,aAAa,MAAM,QAAQ,SAAS,GAAG;AAC5D,oBAAU;AAAA,YACR,MAAM,KAAK,KAAK,EAAE,WAAW,iBAAiB,EAAE,QAAQ,QAAQ,GAAG,mBAAmB,GAAG,cAAc;AAAA,UACzG;AACA,oBAAU,CAAC;AAAA,QACb;AAAA,MACF;AAEA,UAAI,aAAa;AAEjB,UAAI,cAAc;AAChB,mBAAW,QAAQ,WAAW;AAC5B,cAAI,CAAC,KAAK,SAAS;AACjB,kBAAM,IAAI,MAAM,oFAAoF;AAAA,UACtG;AAEA,gBAAM,sBAAsB;AAAA,YAC1B,MAAM,YAAY;AAChB,kBAAI,KAAK,YAAY,UAAa,CAAC,KAAK,SAAS;AAC/C,sBAAM,IAAI,MAAM,oFAAoF;AAAA,cACtG;AAEA,qBAAO,KAAK,SAAS,EAAE,OAAO,KAAK,OAAO,SAAS,KAAK,QAAQ,CAAC,EAAE,MAAM,CAAC,UAAoB;AAC5F,oBAAI,MAAM,WAAW,KAAK;AACxB,yBAAO;AAAA,gBACT;AAEA,sBAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,YACA,UAAU,CAAC,aAAa,aAAa;AAAA,YACrC,YAAY,MAAO,cAAc;AAAA,YACjC,OAAO;AAAA,cACL,UAAU,MAAM,cAAc;AAAA,cAC9B,SAAS,MAAM,4CAA4C,UAAU,IAAI,EAAE;AAAA,YAC7E;AAAA,YACA,SAAS,MAAc,KAAK,IAAI,aAAa,KAAK,GAAI;AAAA,UACxD,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,qBACE,sBACA,gBACuC;AACvC,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAEA,UAAI,CAAC,qBAAqB,MAAM;AAC9B,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AACA,UAAI,CAAC,qBAAqB,MAAM;AAC9B,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AACA,UAAI,CAAC,qBAAqB,OAAO;AAC/B,cAAM,IAAI,MAAM,yFAAyF;AAAA,MAC3G;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,kBACE,mBACA,gBACoC;AACpC,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,UAAI,CAAC,kBAAkB,MAAM;AAC3B,cAAM,IAAI,MAAM,kFAAkF;AAAA,MACpG;AACA,UAAI,CAAC,kBAAkB,MAAM;AAC3B,cAAM,IAAI,MAAM,kFAAkF;AAAA,MACpG;AACA,UAAI,CAAC,kBAAkB,OAAO;AAC5B,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,aAAa,cAA4B,gBAAgE;AACvG,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,UAAI,CAAC,aAAa,MAAM;AACtB,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AACA,UAAI,CAAC,aAAa,MAAM;AACtB,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,WAAW,YAAwB,gBAA8D;AAC/F,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,UAAI,CAAC,WAAW,UAAU;AACxB,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AACA,UAAI,CAAC,WAAW,eAAe;AAC7B,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AACA,UAAI,CAAC,WAAW,QAAQ;AACtB,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aAAa,YAA0B,gBAA8D;AACnG,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,UAAI,CAAC,WAAW,UAAU;AACxB,cAAM,IAAI,MAAM,0EAA0E;AAAA,MAC5F;AACA,UAAI,CAAC,WAAW,eAAe;AAC7B,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AACA,UAAI,CAAC,WAAW,SAAS;AACvB,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AACA,UAAI,CAAC,WAAW,QAAQ;AACtB,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,qBACE,sBACA,gBACuC;AACvC,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAEA,UAAI,CAAC,qBAAqB,MAAM;AAC9B,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,qBACE,EAAE,iBAAiB,GACnB,gBACyB;AACzB,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,kBACE,EAAE,cAAc,GAChB,gBACyB;AACzB,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AAEA,YAAM,cAAc,kCAAkC;AAAA,QACpD;AAAA,QACA,mBAAmB,aAAa;AAAA,MAClC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,aAAa,EAAE,SAAS,GAAsB,gBAA0D;AACtG,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,wBAAwB,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,WAAW,EAAE,OAAO,GAAoB,gBAA0D;AAChG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,aAAa,EAAE,OAAO,GAAsB,gBAA0D;AACpG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,qBACE,EAAE,iBAAiB,GACnB,gBACyB;AACzB,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,YAAY,EAAE,OAAO,GAAqB,gBAA8D;AACtG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,YAAM,cAAc,4BAA4B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,cAAc,EAAE,OAAO,GAAuB,gBAA8D;AAC1G,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,YAAM,cAAc,4BAA4B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,WAAW,EAAE,OAAO,GAAoB,gBAA8D;AACpG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,2BAA2B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC7F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,aAAa,EAAE,OAAO,GAAsB,gBAA8D;AACxG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,2BAA2B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC7F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,kBACE,EAAE,iBAAiB,GACnB,gBACyB;AACzB,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,eAAe,EAAE,cAAc,GAAwB,gBAAuD;AAC5G,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc,kCAAkC;AAAA,QACpD;AAAA,QACA,mBAAmB,aAAa;AAAA,MAClC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,SAAS,EAAE,OAAO,QAAQ,GAAkB,gBAAiD;AAC3F,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,mCACjB,QAAQ,WAAW,mBAAmB,KAAK,CAAC,EAC5C,QAAQ,aAAa,mBAAmB,OAAO,CAAC;AACnD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,OAAO,EAAE,MAAM,GAAgB,gBAA+C;AAC5E,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACxE;AAEA,YAAM,cAAc,kBAAkB,QAAQ,WAAW,mBAAmB,KAAK,CAAC;AAClF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,UAAU,EAAE,SAAS,GAAmB,gBAAkD;AACxF,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,YAAM,cAAc,wBAAwB,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,QAAQ,EAAE,OAAO,GAAiB,gBAAgD;AAChF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,UAAU,EAAE,OAAO,GAAmB,gBAAkD;AACtF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,kBACE,EAAE,iBAAiB,GACnB,gBACyB;AACzB,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBA,oBACE,EAAE,cAAc,MAAM,MAAM,UAAU,MAAM,MAAM,IAA8B,CAAC,GACjF,iBAA6C,QACP;AACtC,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,aAAa,QAAW;AAC1B,wBAAgB,UAAU,IAAI,SAAS,SAAS;AAAA,MAClD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmBA,iBACE,EAAE,cAAc,MAAM,MAAM,kBAAkB,kBAAkB,MAAM,MAAM,IAA2B,CAAC,GACxG,iBAA6C,QACV;AACnC,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,qBAAqB,QAAW;AAClC,wBAAgB,kBAAkB,IAAI,iBAAiB,SAAS;AAAA,MAClE;AAEA,UAAI,qBAAqB,QAAW;AAClC,wBAAgB,kBAAkB,IAAI,iBAAiB,SAAS;AAAA,MAClE;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAqBA,WACE,EAAE,OAAO,cAAc,MAAM,QAAQ,MAAM,MAAM,OAAO,WAAW,QAAQ,GAC3E,gBAC6B;AAC7B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,yBAAyB,QAAQ,WAAW,mBAAmB,KAAK,CAAC;AACzF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAqBA,SACE,EAAE,cAAc,MAAM,QAAQ,MAAM,QAAQ,MAAM,OAAO,WAAW,QAAQ,IAAmB,CAAC,GAChG,iBAA6C,QACnB;AAC1B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBA,YACE,EAAE,cAAc,MAAM,MAAM,kBAAkB,MAAM,MAAM,IAAsB,CAAC,GACjF,iBAA6C,QACf;AAC9B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,qBAAqB,QAAW;AAClC,wBAAgB,kBAAkB,IAAI,iBAAiB,SAAS;AAAA,MAClE;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuBA,UACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAoB,CAAC,GACrB,iBAA6C,QACjB;AAC5B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,aAAa,QAAW;AAC1B,wBAAgB,UAAU,IAAI,SAAS,SAAS;AAAA,MAClD;AAEA,UAAI,eAAe,QAAW;AAC5B,wBAAgB,YAAY,IAAI,WAAW,SAAS;AAAA,MACtD;AAEA,UAAI,kBAAkB,QAAW;AAC/B,wBAAgB,eAAe,IAAI,cAAc,SAAS;AAAA,MAC5D;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,UAAI,2BAA2B,QAAW;AACxC,wBAAgB,wBAAwB,IAAI,uBAAuB,SAAS;AAAA,MAC9E;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuBA,YACE,EAAE,cAAc,MAAM,QAAQ,SAAS,UAAU,eAAe,aAAa,MAAM,MAAM,IAAsB,CAAC,GAChH,iBAA6C,QACf;AAC9B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,aAAa,QAAW;AAC1B,wBAAgB,UAAU,IAAI,SAAS,SAAS;AAAA,MAClD;AAEA,UAAI,kBAAkB,QAAW;AAC/B,wBAAgB,eAAe,IAAI,cAAc,SAAS;AAAA,MAC5D;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,oBACE,EAAE,cAAc,MAAM,MAAM,MAAM,IAA8B,CAAC,GACjE,iBAA6C,QACP;AACtC,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,iBAAiB,QAAW;AAC9B,wBAAgB,cAAc,IAAI,aAAa,SAAS;AAAA,MAC1D;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,KACE,EAAE,WAAW,iBAAiB,OAAO,mBAAmB,GACxD,gBACwB;AACxB,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,UAAI,CAAC,gBAAgB,QAAQ;AAC3B,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AACA,UAAI,CAAC,gBAAgB,SAAS;AAC5B,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc,sBAAsB,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,uBAAuB,QAAW;AACpC,wBAAgB,oBAAoB,IAAI,mBAAmB,SAAS;AAAA,MACtE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,uBAAiB;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG,iDAAgB;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,SACE,EAAE,QAAQ,iBAAiB,MAAM,GACjC,gBACwB;AACxB,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACpF;AAEA,UAAI,CAAC,gBAAgB,QAAQ;AAC3B,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AACA,UAAI,CAAC,gBAAgB,SAAS;AAC5B,cAAM,IAAI,MAAM,0EAA0E;AAAA,MAC5F;AAEA,YAAM,cAAc,yBAAyB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC3F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,uBAAiB;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG,iDAAgB;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,UACE,EAAE,UAAU,iBAAiB,GAC7B,gBAC4B;AAC5B,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,YAAM,cAAc,4BAA4B,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AAClG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,mBAAmB,mBAAmB,CAAC;AAAA,MAC/C;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,QAAQ,EAAE,OAAO,GAAiB,gBAAuD;AACvF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,wBAAwB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC1F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAeA,UAAU,EAAE,OAAO,GAAmB,gBAAuD;AAC3F,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,wBAAwB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AAC1F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,sBACE,sBACA,gBACgC;AAChC,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,oFAAoF;AAAA,MACtG;AAEA,UAAI,CAAC,qBAAqB,mBAAmB;AAC3C,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,mBACE,mBACA,gBAC6B;AAC7B,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,UAAI,CAAC,kBAAkB,gBAAgB;AACrC,cAAM,IAAI,MAAM,6FAA6F;AAAA,MAC/G;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,cAAc,cAA4B,gBAAyD;AACjG,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,UAAI,CAAC,aAAa,WAAW;AAC3B,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,YAAY,YAAwB,gBAAuD;AACzF,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,WAAW,SAAS;AACvB,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,cAAc,YAAwB,gBAAyD;AAC7F,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACpF;AAEA,UAAI,CAAC,WAAW,SAAS;AACvB,cAAM,IAAI,MAAM,0EAA0E;AAAA,MAC5F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,sBACE,sBACA,gBACgC;AAChC,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,oFAAoF;AAAA,MACtG;AAEA,UAAI,CAAC,qBAAqB,mBAAmB;AAC3C,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,4BACE,EAAE,SAAS,GACX,gBACwB;AACxB,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,YAAM,cAAc,iCAAiC,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACvG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,uBAAiB;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG,iDAAgB;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,kBACE,mBACA,gBACoC;AACpC,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,UAAI,CAAC,kBAAkB,cAAc;AACnC,cAAM,IAAI,MAAM,0FAA0F;AAAA,MAC5G;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,8BACE,EAAE,kBAAkB,kBAAkB,GACtC,gBACoC;AACpC,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AAEA,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,yFAAyF;AAAA,MAC3G;AAEA,UAAI,CAAC,kBAAkB,cAAc;AACnC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,4CAA4C;AAAA,QAC9D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,qBACE,EAAE,kBAAkB,qBAAqB,GACzC,gBACuC;AACvC,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,kBACE,EAAE,eAAe,kBAAkB,GACnC,gBACoC;AACpC,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AAEA,UAAI,CAAC,mBAAmB;AACtB,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,YAAM,cAAc,kCAAkC;AAAA,QACpD;AAAA,QACA,mBAAmB,aAAa;AAAA,MAClC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,aACE,EAAE,UAAU,aAAa,GACzB,gBAC+B;AAC/B,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,wBAAwB,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AAC9F,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,WAAW,EAAE,QAAQ,WAAW,GAAoB,gBAA8D;AAChH,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aACE,EAAE,QAAQ,WAAW,GACrB,gBAC6B;AAC7B,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc,oBAAoB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACtF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,qBACE,EAAE,kBAAkB,qBAAqB,GACzC,gBACuC;AACvC,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAEA,UAAI,CAAC,qBAAqB,MAAM;AAC9B,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AAEA,YAAM,cAAc,wCAAwC;AAAA,QAC1D;AAAA,QACA,mBAAmB,gBAAgB;AAAA,MACrC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,eACE,cACA,iBAA6C,QACrB;AACxB,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,eAAe,eAAe,CAAC;AAAA,MACvC;AAEA,uBAAiB;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG,iDAAgB;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,2BACE,EAAE,UAAU,aAAa,GACzB,gBACwB;AACxB,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACnG;AAEA,YAAM,cAAc,iCAAiC,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACvG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,uBAAiB;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,GAAG,iDAAgB;AAAA,QACrB;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;ADvlFO,SAAS,gBACd,OACA,QACA,QACA,SACiB;AACjB,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,MAAI,CAAC,UAAW,WAAW,OAAO,WAAW,YAAY,CAAC,QAAQ,SAAS,MAAM,IAAK;AACpF,UAAM,IAAI,MAAM,4DAA4D,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,EAClG;AAEA,SAAO;AAAA,IACL,GAAG,sBAAsB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,WAAW,qBAAqB;AAAA,MAChC,eAAe,CAAC,EAAE,SAAS,QAAQ,CAAC;AAAA,MACpC,gBAAgB,gBAAgB;AAAA,MAChC,eAAe,gBAAgB;AAAA,MAC/B,YAAY,kBAAkB;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;", "names": []}