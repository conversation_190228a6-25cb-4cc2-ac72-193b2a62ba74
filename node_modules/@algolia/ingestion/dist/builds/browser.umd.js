(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["@algolia/ingestion"] = {}));
})(this, (function (exports) { 'use strict';

	function G(){function a(o){return new Promise(d=>{let m=new XMLHttpRequest;m.open(o.method,o.url,true),Object.keys(o.headers).forEach(i=>m.setRequestHeader(i,o.headers[i]));let f=(i,e)=>setTimeout(()=>{m.abort(),d({status:0,content:e,isTimedOut:true});},i),y=f(o.connectTimeout,"Connection timeout"),w;m.onreadystatechange=()=>{m.readyState>m.OPENED&&w===void 0&&(clearTimeout(y),w=f(o.responseTimeout,"Socket timeout"));},m.onerror=()=>{m.status===0&&(clearTimeout(y),clearTimeout(w),d({content:m.responseText||"Network request failed",status:m.status,isTimedOut:false}));},m.onload=()=>{clearTimeout(y),clearTimeout(w),d({content:m.responseText,status:m.status,isTimedOut:false});},m.send(o.data);})}return {send:a}}function W(a){let o,d=`algolia-client-js-${a.key}`;function m(){return o===void 0&&(o=a.localStorage||window.localStorage),o}function f(){return JSON.parse(m().getItem(d)||"{}")}function y(i){m().setItem(d,JSON.stringify(i));}function w(){let i=a.timeToLive?a.timeToLive*1e3:null,e=f(),r=Object.fromEntries(Object.entries(e).filter(([,n])=>n.timestamp!==void 0));if(y(r),!i)return;let s=Object.fromEntries(Object.entries(r).filter(([,n])=>{let u=new Date().getTime();return !(n.timestamp+i<u)}));y(s);}return {get(i,e,r={miss:()=>Promise.resolve()}){return Promise.resolve().then(()=>(w(),f()[JSON.stringify(i)])).then(s=>Promise.all([s?s.value:e(),s!==void 0])).then(([s,n])=>Promise.all([s,n||r.miss(s)])).then(([s])=>s)},set(i,e){return Promise.resolve().then(()=>{let r=f();return r[JSON.stringify(i)]={timestamp:new Date().getTime(),value:e},m().setItem(d,JSON.stringify(r)),e})},delete(i){return Promise.resolve().then(()=>{let e=f();delete e[JSON.stringify(i)],m().setItem(d,JSON.stringify(e));})},clear(){return Promise.resolve().then(()=>{m().removeItem(d);})}}}function Y(){return {get(a,o,d={miss:()=>Promise.resolve()}){return o().then(f=>Promise.all([f,d.miss(f)])).then(([f])=>f)},set(a,o){return Promise.resolve(o)},delete(a){return Promise.resolve()},clear(){return Promise.resolve()}}}function D(a){let o=[...a.caches],d=o.shift();return d===void 0?Y():{get(m,f,y={miss:()=>Promise.resolve()}){return d.get(m,f,y).catch(()=>D({caches:o}).get(m,f,y))},set(m,f){return d.set(m,f).catch(()=>D({caches:o}).set(m,f))},delete(m){return d.delete(m).catch(()=>D({caches:o}).delete(m))},clear(){return d.clear().catch(()=>D({caches:o}).clear())}}}function x(a={serializable:true}){let o={};return {get(d,m,f={miss:()=>Promise.resolve()}){let y=JSON.stringify(d);if(y in o)return Promise.resolve(a.serializable?JSON.parse(o[y]):o[y]);let w=m();return w.then(i=>f.miss(i)).then(()=>w)},set(d,m){return o[JSON.stringify(d)]=a.serializable?JSON.stringify(m):m,Promise.resolve(m)},delete(d){return delete o[JSON.stringify(d)],Promise.resolve()},clear(){return o={},Promise.resolve()}}}function Z(a){let o={value:`Algolia for JavaScript (${a})`,add(d){let m=`; ${d.segment}${d.version!==void 0?` (${d.version})`:""}`;return o.value.indexOf(m)===-1&&(o.value=`${o.value}${m}`),o}};return o}function $(a,o,d="WithinHeaders"){let m={"x-algolia-api-key":o,"x-algolia-application-id":a};return {headers(){return d==="WithinHeaders"?m:{}},queryParameters(){return d==="WithinQueryParameters"?m:{}}}}function j({func:a,validate:o,aggregator:d,error:m,timeout:f=()=>0}){let y=w=>new Promise((i,e)=>{a(w).then(async r=>(d&&await d(r),await o(r)?i(r):m&&await m.validate(r)?e(new Error(await m.message(r))):setTimeout(()=>{y(r).then(i).catch(e);},await f()))).catch(r=>{e(r);});});return y()}function B({algoliaAgents:a,client:o,version:d}){let m=Z(d).add({segment:o,version:d});return a.forEach(f=>m.add(f)),m}function J(){return {debug(a,o){return Promise.resolve()},info(a,o){return Promise.resolve()},error(a,o){return Promise.resolve()}}}var N=2*60*1e3;function _(a,o="up"){let d=Date.now();function m(){return o==="up"||Date.now()-d>N}function f(){return o==="timed out"&&Date.now()-d<=N}return {...a,status:o,lastUpdate:d,isUp:m,isTimedOut:f}}var z=class extends Error{name="AlgoliaError";constructor(a,o){super(a),o&&(this.name=o);}};var M=class extends z{stackTrace;constructor(a,o,d){super(a,d),this.stackTrace=o;}},ee=class extends M{constructor(a){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",a,"RetryError");}},U=class extends M{status;constructor(a,o,d,m="ApiError"){super(a,d,m),this.status=o;}},re=class extends z{response;constructor(a,o){super(a,"DeserializationError"),this.response=o;}},te=class extends U{error;constructor(a,o,d,m){super(a,o,m,"DetailedApiError"),this.error=d;}};function se(a,o,d){let m=oe(d),f=`${a.protocol}://${a.url}${a.port?`:${a.port}`:""}/${o.charAt(0)==="/"?o.substring(1):o}`;return m.length&&(f+=`?${m}`),f}function oe(a){return Object.keys(a).filter(o=>a[o]!==void 0).sort().map(o=>`${o}=${encodeURIComponent(Object.prototype.toString.call(a[o])==="[object Array]"?a[o].join(","):a[o]).replace(/\+/g,"%20")}`).join("&")}function ae(a,o){if(a.method==="GET"||a.data===void 0&&o.data===void 0)return;let d=Array.isArray(a.data)?a.data:{...a.data,...o.data};return JSON.stringify(d)}function ne(a,o,d){let m={Accept:"application/json",...a,...o,...d},f={};return Object.keys(m).forEach(y=>{let w=m[y];f[y.toLowerCase()]=w;}),f}function ie(a){try{return JSON.parse(a.content)}catch(o){throw new re(o.message,a)}}function ue({content:a,status:o},d){try{let m=JSON.parse(a);return "error"in m?new te(m.message,o,m.error,d):new U(m.message,o,d)}catch{}return new U(a,o,d)}function me({isTimedOut:a,status:o}){return !a&&~~o===0}function ce({isTimedOut:a,status:o}){return a||me({isTimedOut:a,status:o})||~~(o/100)!==2&&~~(o/100)!==4}function de({status:a}){return ~~(a/100)===2}function pe(a){return a.map(o=>F(o))}function F(a){let o=a.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return {...a,request:{...a.request,headers:{...a.request.headers,...o}}}}function X({hosts:a,hostsCache:o,baseHeaders:d,logger:m,baseQueryParameters:f,algoliaAgent:y,timeouts:w,requester:i,requestsCache:e,responsesCache:r}){async function s(t){let c=await Promise.all(t.map(q=>o.get(q,()=>Promise.resolve(_(q))))),h=c.filter(q=>q.isUp()),l=c.filter(q=>q.isTimedOut()),g=[...h,...l];return {hosts:g.length>0?g:t,getTimeout(q,p){return (l.length===0&&q===0?1:l.length+3+q)*p}}}async function n(t,c,h=true){let l=[],g=ae(t,c),P=ne(d,t.headers,c.headers),q=t.method==="GET"?{...t.data,...c.data}:{},p={...f,...t.queryParameters,...q};if(y.value&&(p["x-algolia-agent"]=y.value),c&&c.queryParameters)for(let R of Object.keys(c.queryParameters))!c.queryParameters[R]||Object.prototype.toString.call(c.queryParameters[R])==="[object Object]"?p[R]=c.queryParameters[R]:p[R]=c.queryParameters[R].toString();let E=0,T=async(R,C)=>{let S=R.pop();if(S===void 0)throw new ee(pe(l));let v={...w,...c.timeouts},Q={data:g,headers:P,method:t.method,url:se(S,t.path,p),connectTimeout:C(E,v.connect),responseTimeout:C(E,h?v.read:v.write)},L=A=>{let V={request:Q,response:A,host:S,triesLeft:R.length};return l.push(V),V},k=await i.send(Q);if(ce(k)){let A=L(k);return k.isTimedOut&&E++,m.info("Retryable failure",F(A)),await o.set(S,_(S,k.isTimedOut?"timed out":"down")),T(R,C)}if(de(k))return ie(k);throw L(k),ue(k,l)},O=a.filter(R=>R.accept==="readWrite"||(h?R.accept==="read":R.accept==="write")),b=await s(O);return T([...b.hosts].reverse(),b.getTimeout)}function u(t,c={}){let h=t.useReadTransporter||t.method==="GET";if(!h)return n(t,c,h);let l=()=>n(t,c);if((c.cacheable||t.cacheable)!==true)return l();let P={request:t,requestOptions:c,transporter:{queryParameters:f,headers:d}};return r.get(P,()=>e.get(P,()=>e.set(P,l()).then(q=>Promise.all([e.delete(P),q]),q=>Promise.all([e.delete(P),Promise.reject(q)])).then(([q,p])=>p)),{miss:q=>r.set(P,q)})}return {hostsCache:o,requester:i,timeouts:w,logger:m,algoliaAgent:y,baseHeaders:d,baseQueryParameters:f,hosts:a,request:u,requestsCache:e,responsesCache:r}}var I="1.30.0",H=["eu","us"];function he(a){return [{url:"data.{region}.algolia.com".replace("{region}",a),accept:"readWrite",protocol:"https"}]}function fe(a){return a.type==="onDemand"}function le(a){return a.type==="schedule"}function Pe(a){return a.type==="subscription"}function K({appId:a,apiKey:o,authMode:d,algoliaAgents:m,region:f,...y}){let w=$(a,o,d),i=X({hosts:he(f),...y,algoliaAgent:B({algoliaAgents:m,client:"Ingestion",version:I}),baseHeaders:{"content-type":"text/plain",...w.headers(),...y.baseHeaders},baseQueryParameters:{...w.queryParameters(),...y.baseQueryParameters}});return {transporter:i,appId:a,apiKey:o,clearCache(){return Promise.all([i.requestsCache.clear(),i.responsesCache.clear()]).then(()=>{})},get _ua(){return i.algoliaAgent.value},addAlgoliaAgent(e,r){i.algoliaAgent.add({segment:e,version:r});},setClientApiKey({apiKey:e}){!d||d==="WithinHeaders"?i.baseHeaders["x-algolia-api-key"]=e:i.baseQueryParameters["x-algolia-api-key"]=e;},async chunkedPush({indexName:e,objects:r,action:s="addObject",waitForTasks:n,batchSize:u=1e3,referenceIndexName:t},c){let h=[],l=[],g=r.entries();for(let[q,p]of g)h.push(p),(h.length===u||q===r.length-1)&&(l.push(await this.push({indexName:e,pushTaskPayload:{action:s,records:h},referenceIndexName:t},c)),h=[]);let P=0;if(n)for(let q of l){if(!q.eventID)throw new Error("received unexpected response from the push endpoint, eventID must not be undefined");await j({func:async()=>{if(q.eventID===void 0||!q.eventID)throw new Error("received unexpected response from the push endpoint, eventID must not be undefined");return this.getEvent({runID:q.runID,eventID:q.eventID}).catch(p=>{if(p.status!==404)throw p})},validate:p=>p!==void 0,aggregator:()=>P+=1,error:{validate:()=>P>=50,message:()=>`The maximum number of retries exceeded. (${P}/50)`},timeout:()=>Math.min(P*500,5e3)});}return l},createAuthentication(e,r){if(!e)throw new Error("Parameter `authenticationCreate` is required when calling `createAuthentication`.");if(!e.type)throw new Error("Parameter `authenticationCreate.type` is required when calling `createAuthentication`.");if(!e.name)throw new Error("Parameter `authenticationCreate.name` is required when calling `createAuthentication`.");if(!e.input)throw new Error("Parameter `authenticationCreate.input` is required when calling `createAuthentication`.");let t={method:"POST",path:"/1/authentications",queryParameters:{},headers:{},data:e};return i.request(t,r)},createDestination(e,r){if(!e)throw new Error("Parameter `destinationCreate` is required when calling `createDestination`.");if(!e.type)throw new Error("Parameter `destinationCreate.type` is required when calling `createDestination`.");if(!e.name)throw new Error("Parameter `destinationCreate.name` is required when calling `createDestination`.");if(!e.input)throw new Error("Parameter `destinationCreate.input` is required when calling `createDestination`.");let t={method:"POST",path:"/1/destinations",queryParameters:{},headers:{},data:e};return i.request(t,r)},createSource(e,r){if(!e)throw new Error("Parameter `sourceCreate` is required when calling `createSource`.");if(!e.type)throw new Error("Parameter `sourceCreate.type` is required when calling `createSource`.");if(!e.name)throw new Error("Parameter `sourceCreate.name` is required when calling `createSource`.");let t={method:"POST",path:"/1/sources",queryParameters:{},headers:{},data:e};return i.request(t,r)},createTask(e,r){if(!e)throw new Error("Parameter `taskCreate` is required when calling `createTask`.");if(!e.sourceID)throw new Error("Parameter `taskCreate.sourceID` is required when calling `createTask`.");if(!e.destinationID)throw new Error("Parameter `taskCreate.destinationID` is required when calling `createTask`.");if(!e.action)throw new Error("Parameter `taskCreate.action` is required when calling `createTask`.");let t={method:"POST",path:"/2/tasks",queryParameters:{},headers:{},data:e};return i.request(t,r)},createTaskV1(e,r){if(!e)throw new Error("Parameter `taskCreate` is required when calling `createTaskV1`.");if(!e.sourceID)throw new Error("Parameter `taskCreate.sourceID` is required when calling `createTaskV1`.");if(!e.destinationID)throw new Error("Parameter `taskCreate.destinationID` is required when calling `createTaskV1`.");if(!e.trigger)throw new Error("Parameter `taskCreate.trigger` is required when calling `createTaskV1`.");if(!e.action)throw new Error("Parameter `taskCreate.action` is required when calling `createTaskV1`.");let t={method:"POST",path:"/1/tasks",queryParameters:{},headers:{},data:e};return i.request(t,r)},createTransformation(e,r){if(!e)throw new Error("Parameter `transformationCreate` is required when calling `createTransformation`.");if(!e.name)throw new Error("Parameter `transformationCreate.name` is required when calling `createTransformation`.");let t={method:"POST",path:"/1/transformations",queryParameters:{},headers:{},data:e};return i.request(t,r)},customDelete({path:e,parameters:r},s){if(!e)throw new Error("Parameter `path` is required when calling `customDelete`.");let c={method:"DELETE",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return i.request(c,s)},customGet({path:e,parameters:r},s){if(!e)throw new Error("Parameter `path` is required when calling `customGet`.");let c={method:"GET",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{}};return i.request(c,s)},customPost({path:e,parameters:r,body:s},n){if(!e)throw new Error("Parameter `path` is required when calling `customPost`.");let h={method:"POST",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:s||{}};return i.request(h,n)},customPut({path:e,parameters:r,body:s},n){if(!e)throw new Error("Parameter `path` is required when calling `customPut`.");let h={method:"PUT",path:"/{path}".replace("{path}",e),queryParameters:r||{},headers:{},data:s||{}};return i.request(h,n)},deleteAuthentication({authenticationID:e},r){if(!e)throw new Error("Parameter `authenticationID` is required when calling `deleteAuthentication`.");let t={method:"DELETE",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},deleteDestination({destinationID:e},r){if(!e)throw new Error("Parameter `destinationID` is required when calling `deleteDestination`.");let t={method:"DELETE",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},deleteSource({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `deleteSource`.");let t={method:"DELETE",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},deleteTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `deleteTask`.");let t={method:"DELETE",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},deleteTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `deleteTaskV1`.");let t={method:"DELETE",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},deleteTransformation({transformationID:e},r){if(!e)throw new Error("Parameter `transformationID` is required when calling `deleteTransformation`.");let t={method:"DELETE",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},disableTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `disableTask`.");let t={method:"PUT",path:"/2/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},disableTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `disableTaskV1`.");let t={method:"PUT",path:"/1/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},enableTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `enableTask`.");let t={method:"PUT",path:"/2/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},enableTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `enableTaskV1`.");let t={method:"PUT",path:"/1/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getAuthentication({authenticationID:e},r){if(!e)throw new Error("Parameter `authenticationID` is required when calling `getAuthentication`.");let t={method:"GET",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getDestination({destinationID:e},r){if(!e)throw new Error("Parameter `destinationID` is required when calling `getDestination`.");let t={method:"GET",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getEvent({runID:e,eventID:r},s){if(!e)throw new Error("Parameter `runID` is required when calling `getEvent`.");if(!r)throw new Error("Parameter `eventID` is required when calling `getEvent`.");let c={method:"GET",path:"/1/runs/{runID}/events/{eventID}".replace("{runID}",encodeURIComponent(e)).replace("{eventID}",encodeURIComponent(r)),queryParameters:{},headers:{}};return i.request(c,s)},getRun({runID:e},r){if(!e)throw new Error("Parameter `runID` is required when calling `getRun`.");let t={method:"GET",path:"/1/runs/{runID}".replace("{runID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getSource({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `getSource`.");let t={method:"GET",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `getTask`.");let t={method:"GET",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `getTaskV1`.");let t={method:"GET",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},getTransformation({transformationID:e},r){if(!e)throw new Error("Parameter `transformationID` is required when calling `getTransformation`.");let t={method:"GET",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},listAuthentications({itemsPerPage:e,page:r,type:s,platform:n,sort:u,order:t}={},c=void 0){let h="/1/authentications",l={},g={};e!==void 0&&(g.itemsPerPage=e.toString()),r!==void 0&&(g.page=r.toString()),s!==void 0&&(g.type=s.toString()),n!==void 0&&(g.platform=n.toString()),u!==void 0&&(g.sort=u.toString()),t!==void 0&&(g.order=t.toString());let P={method:"GET",path:h,queryParameters:g,headers:l};return i.request(P,c)},listDestinations({itemsPerPage:e,page:r,type:s,authenticationID:n,transformationID:u,sort:t,order:c}={},h=void 0){let l="/1/destinations",g={},P={};e!==void 0&&(P.itemsPerPage=e.toString()),r!==void 0&&(P.page=r.toString()),s!==void 0&&(P.type=s.toString()),n!==void 0&&(P.authenticationID=n.toString()),u!==void 0&&(P.transformationID=u.toString()),t!==void 0&&(P.sort=t.toString()),c!==void 0&&(P.order=c.toString());let q={method:"GET",path:l,queryParameters:P,headers:g};return i.request(q,h)},listEvents({runID:e,itemsPerPage:r,page:s,status:n,type:u,sort:t,order:c,startDate:h,endDate:l},g){if(!e)throw new Error("Parameter `runID` is required when calling `listEvents`.");let P="/1/runs/{runID}/events".replace("{runID}",encodeURIComponent(e)),q={},p={};r!==void 0&&(p.itemsPerPage=r.toString()),s!==void 0&&(p.page=s.toString()),n!==void 0&&(p.status=n.toString()),u!==void 0&&(p.type=u.toString()),t!==void 0&&(p.sort=t.toString()),c!==void 0&&(p.order=c.toString()),h!==void 0&&(p.startDate=h.toString()),l!==void 0&&(p.endDate=l.toString());let E={method:"GET",path:P,queryParameters:p,headers:q};return i.request(E,g)},listRuns({itemsPerPage:e,page:r,status:s,type:n,taskID:u,sort:t,order:c,startDate:h,endDate:l}={},g=void 0){let P="/1/runs",q={},p={};e!==void 0&&(p.itemsPerPage=e.toString()),r!==void 0&&(p.page=r.toString()),s!==void 0&&(p.status=s.toString()),n!==void 0&&(p.type=n.toString()),u!==void 0&&(p.taskID=u.toString()),t!==void 0&&(p.sort=t.toString()),c!==void 0&&(p.order=c.toString()),h!==void 0&&(p.startDate=h.toString()),l!==void 0&&(p.endDate=l.toString());let E={method:"GET",path:P,queryParameters:p,headers:q};return i.request(E,g)},listSources({itemsPerPage:e,page:r,type:s,authenticationID:n,sort:u,order:t}={},c=void 0){let h="/1/sources",l={},g={};e!==void 0&&(g.itemsPerPage=e.toString()),r!==void 0&&(g.page=r.toString()),s!==void 0&&(g.type=s.toString()),n!==void 0&&(g.authenticationID=n.toString()),u!==void 0&&(g.sort=u.toString()),t!==void 0&&(g.order=t.toString());let P={method:"GET",path:h,queryParameters:g,headers:l};return i.request(P,c)},listTasks({itemsPerPage:e,page:r,action:s,enabled:n,sourceID:u,sourceType:t,destinationID:c,triggerType:h,withEmailNotifications:l,sort:g,order:P}={},q=void 0){let p="/2/tasks",E={},T={};e!==void 0&&(T.itemsPerPage=e.toString()),r!==void 0&&(T.page=r.toString()),s!==void 0&&(T.action=s.toString()),n!==void 0&&(T.enabled=n.toString()),u!==void 0&&(T.sourceID=u.toString()),t!==void 0&&(T.sourceType=t.toString()),c!==void 0&&(T.destinationID=c.toString()),h!==void 0&&(T.triggerType=h.toString()),l!==void 0&&(T.withEmailNotifications=l.toString()),g!==void 0&&(T.sort=g.toString()),P!==void 0&&(T.order=P.toString());let O={method:"GET",path:p,queryParameters:T,headers:E};return i.request(O,q)},listTasksV1({itemsPerPage:e,page:r,action:s,enabled:n,sourceID:u,destinationID:t,triggerType:c,sort:h,order:l}={},g=void 0){let P="/1/tasks",q={},p={};e!==void 0&&(p.itemsPerPage=e.toString()),r!==void 0&&(p.page=r.toString()),s!==void 0&&(p.action=s.toString()),n!==void 0&&(p.enabled=n.toString()),u!==void 0&&(p.sourceID=u.toString()),t!==void 0&&(p.destinationID=t.toString()),c!==void 0&&(p.triggerType=c.toString()),h!==void 0&&(p.sort=h.toString()),l!==void 0&&(p.order=l.toString());let E={method:"GET",path:P,queryParameters:p,headers:q};return i.request(E,g)},listTransformations({itemsPerPage:e,page:r,sort:s,order:n}={},u=void 0){let t="/1/transformations",c={},h={};e!==void 0&&(h.itemsPerPage=e.toString()),r!==void 0&&(h.page=r.toString()),s!==void 0&&(h.sort=s.toString()),n!==void 0&&(h.order=n.toString());let l={method:"GET",path:t,queryParameters:h,headers:c};return i.request(l,u)},push({indexName:e,pushTaskPayload:r,watch:s,referenceIndexName:n},u){if(!e)throw new Error("Parameter `indexName` is required when calling `push`.");if(!r)throw new Error("Parameter `pushTaskPayload` is required when calling `push`.");if(!r.action)throw new Error("Parameter `pushTaskPayload.action` is required when calling `push`.");if(!r.records)throw new Error("Parameter `pushTaskPayload.records` is required when calling `push`.");let t="/1/push/{indexName}".replace("{indexName}",encodeURIComponent(e)),c={},h={};s!==void 0&&(h.watch=s.toString()),n!==void 0&&(h.referenceIndexName=n.toString());let l={method:"POST",path:t,queryParameters:h,headers:c,data:r};return u={timeouts:{connect:18e4,read:18e4,write:18e4,...u?.timeouts}},i.request(l,u)},pushTask({taskID:e,pushTaskPayload:r,watch:s},n){if(!e)throw new Error("Parameter `taskID` is required when calling `pushTask`.");if(!r)throw new Error("Parameter `pushTaskPayload` is required when calling `pushTask`.");if(!r.action)throw new Error("Parameter `pushTaskPayload.action` is required when calling `pushTask`.");if(!r.records)throw new Error("Parameter `pushTaskPayload.records` is required when calling `pushTask`.");let u="/2/tasks/{taskID}/push".replace("{taskID}",encodeURIComponent(e)),t={},c={};s!==void 0&&(c.watch=s.toString());let h={method:"POST",path:u,queryParameters:c,headers:t,data:r};return n={timeouts:{connect:18e4,read:18e4,write:18e4,...n?.timeouts}},i.request(h,n)},runSource({sourceID:e,runSourcePayload:r},s){if(!e)throw new Error("Parameter `sourceID` is required when calling `runSource`.");let c={method:"POST",path:"/1/sources/{sourceID}/run".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r||{}};return i.request(c,s)},runTask({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `runTask`.");let t={method:"POST",path:"/2/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},runTaskV1({taskID:e},r){if(!e)throw new Error("Parameter `taskID` is required when calling `runTaskV1`.");let t={method:"POST",path:"/1/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return i.request(t,r)},searchAuthentications(e,r){if(!e)throw new Error("Parameter `authenticationSearch` is required when calling `searchAuthentications`.");if(!e.authenticationIDs)throw new Error("Parameter `authenticationSearch.authenticationIDs` is required when calling `searchAuthentications`.");let t={method:"POST",path:"/1/authentications/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},searchDestinations(e,r){if(!e)throw new Error("Parameter `destinationSearch` is required when calling `searchDestinations`.");if(!e.destinationIDs)throw new Error("Parameter `destinationSearch.destinationIDs` is required when calling `searchDestinations`.");let t={method:"POST",path:"/1/destinations/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},searchSources(e,r){if(!e)throw new Error("Parameter `sourceSearch` is required when calling `searchSources`.");if(!e.sourceIDs)throw new Error("Parameter `sourceSearch.sourceIDs` is required when calling `searchSources`.");let t={method:"POST",path:"/1/sources/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},searchTasks(e,r){if(!e)throw new Error("Parameter `taskSearch` is required when calling `searchTasks`.");if(!e.taskIDs)throw new Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasks`.");let t={method:"POST",path:"/2/tasks/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},searchTasksV1(e,r){if(!e)throw new Error("Parameter `taskSearch` is required when calling `searchTasksV1`.");if(!e.taskIDs)throw new Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasksV1`.");let t={method:"POST",path:"/1/tasks/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},searchTransformations(e,r){if(!e)throw new Error("Parameter `transformationSearch` is required when calling `searchTransformations`.");if(!e.transformationIDs)throw new Error("Parameter `transformationSearch.transformationIDs` is required when calling `searchTransformations`.");let t={method:"POST",path:"/1/transformations/search",queryParameters:{},headers:{},data:e};return i.request(t,r)},triggerDockerSourceDiscover({sourceID:e},r){if(!e)throw new Error("Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.");let t={method:"POST",path:"/1/sources/{sourceID}/discover".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{}};return r={timeouts:{connect:18e4,read:18e4,write:18e4,...r?.timeouts}},i.request(t,r)},tryTransformation(e,r){if(!e)throw new Error("Parameter `transformationTry` is required when calling `tryTransformation`.");if(!e.sampleRecord)throw new Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformation`.");let t={method:"POST",path:"/1/transformations/try",queryParameters:{},headers:{},data:e};return i.request(t,r)},tryTransformationBeforeUpdate({transformationID:e,transformationTry:r},s){if(!e)throw new Error("Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.");if(!r)throw new Error("Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.");if(!r.sampleRecord)throw new Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformationBeforeUpdate`.");let c={method:"POST",path:"/1/transformations/{transformationID}/try".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateAuthentication({authenticationID:e,authenticationUpdate:r},s){if(!e)throw new Error("Parameter `authenticationID` is required when calling `updateAuthentication`.");if(!r)throw new Error("Parameter `authenticationUpdate` is required when calling `updateAuthentication`.");let c={method:"PATCH",path:"/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateDestination({destinationID:e,destinationUpdate:r},s){if(!e)throw new Error("Parameter `destinationID` is required when calling `updateDestination`.");if(!r)throw new Error("Parameter `destinationUpdate` is required when calling `updateDestination`.");let c={method:"PATCH",path:"/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateSource({sourceID:e,sourceUpdate:r},s){if(!e)throw new Error("Parameter `sourceID` is required when calling `updateSource`.");if(!r)throw new Error("Parameter `sourceUpdate` is required when calling `updateSource`.");let c={method:"PATCH",path:"/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateTask({taskID:e,taskUpdate:r},s){if(!e)throw new Error("Parameter `taskID` is required when calling `updateTask`.");if(!r)throw new Error("Parameter `taskUpdate` is required when calling `updateTask`.");let c={method:"PATCH",path:"/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateTaskV1({taskID:e,taskUpdate:r},s){if(!e)throw new Error("Parameter `taskID` is required when calling `updateTaskV1`.");if(!r)throw new Error("Parameter `taskUpdate` is required when calling `updateTaskV1`.");let c={method:"PATCH",path:"/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},updateTransformation({transformationID:e,transformationCreate:r},s){if(!e)throw new Error("Parameter `transformationID` is required when calling `updateTransformation`.");if(!r)throw new Error("Parameter `transformationCreate` is required when calling `updateTransformation`.");if(!r.name)throw new Error("Parameter `transformationCreate.name` is required when calling `updateTransformation`.");let c={method:"PUT",path:"/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return i.request(c,s)},validateSource(e,r=void 0){let t={method:"POST",path:"/1/sources/validate",queryParameters:{},headers:{},data:e||{}};return r={timeouts:{connect:18e4,read:18e4,write:18e4,...r?.timeouts}},i.request(t,r)},validateSourceBeforeUpdate({sourceID:e,sourceUpdate:r},s){if(!e)throw new Error("Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.");if(!r)throw new Error("Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.");let c={method:"POST",path:"/1/sources/{sourceID}/validate".replace("{sourceID}",encodeURIComponent(e)),queryParameters:{},headers:{},data:r};return s={timeouts:{connect:18e4,read:18e4,write:18e4,...s?.timeouts}},i.request(c,s)}}}function gs(a,o,d,m){if(!a||typeof a!="string")throw new Error("`appId` is missing.");if(!o||typeof o!="string")throw new Error("`apiKey` is missing.");if(!d||d&&(typeof d!="string"||!H.includes(d)))throw new Error(`\`region\` is required and must be one of the following: ${H.join(", ")}`);return K({appId:a,apiKey:o,region:d,timeouts:{connect:25e3,read:25e3,write:25e3},logger:J(),requester:G(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:x(),requestsCache:x({serializable:false}),hostsCache:D({caches:[W({key:`${I}-${a}`}),x()]}),...m})}

	exports.apiClientVersion = I;
	exports.ingestionClient = gs;
	exports.isOnDemandTrigger = fe;
	exports.isScheduleTrigger = le;
	exports.isSubscriptionTrigger = Pe;

}));
