{"version": 3, "sources": ["../../../requester-browser-xhr/src/createXhrRequester.ts", "../../../client-common/src/cache/createBrowserLocalStorageCache.ts", "../../../client-common/src/cache/createNullCache.ts", "../../../client-common/src/cache/createFallbackableCache.ts", "../../../client-common/src/cache/createMemoryCache.ts", "../../../client-common/src/constants.ts", "../../../client-common/src/createAlgoliaAgent.ts", "../../../client-common/src/createAuth.ts", "../../../client-common/src/createIterablePromise.ts", "../../../client-common/src/getAlgoliaAgent.ts", "../../../client-common/src/logger/createNullLogger.ts", "../../../client-common/src/transporter/createStatefulHost.ts", "../../../client-common/src/transporter/errors.ts", "../../../client-common/src/transporter/helpers.ts", "../../../client-common/src/transporter/responses.ts", "../../../client-common/src/transporter/stackTrace.ts", "../../../client-common/src/transporter/createTransporter.ts", "../../../client-common/src/types/logger.ts", "../../src/ingestionClient.ts", "../../builds/browser.ts"], "sourcesContent": ["import type { EndRequest, Requester, Response } from '@algolia/client-common';\n\ntype Timeout = ReturnType<typeof setTimeout>;\n\nexport function createXhrRequester(): Requester {\n  function send(request: EndRequest): Promise<Response> {\n    return new Promise((resolve) => {\n      const baseRequester = new XMLHttpRequest();\n      baseRequester.open(request.method, request.url, true);\n\n      Object.keys(request.headers).forEach((key) => baseRequester.setRequestHeader(key, request.headers[key]));\n\n      const createTimeout = (timeout: number, content: string): Timeout => {\n        return setTimeout(() => {\n          baseRequester.abort();\n\n          resolve({\n            status: 0,\n            content,\n            isTimedOut: true,\n          });\n        }, timeout);\n      };\n\n      const connectTimeout = createTimeout(request.connectTimeout, 'Connection timeout');\n\n      let responseTimeout: Timeout | undefined;\n\n      baseRequester.onreadystatechange = (): void => {\n        if (baseRequester.readyState > baseRequester.OPENED && responseTimeout === undefined) {\n          clearTimeout(connectTimeout);\n\n          responseTimeout = createTimeout(request.responseTimeout, 'Socket timeout');\n        }\n      };\n\n      baseRequester.onerror = (): void => {\n        // istanbul ignore next\n        if (baseRequester.status === 0) {\n          clearTimeout(connectTimeout);\n          clearTimeout(responseTimeout!);\n\n          resolve({\n            content: baseRequester.responseText || 'Network request failed',\n            status: baseRequester.status,\n            isTimedOut: false,\n          });\n        }\n      };\n\n      baseRequester.onload = (): void => {\n        clearTimeout(connectTimeout);\n        clearTimeout(responseTimeout!);\n\n        resolve({\n          content: baseRequester.responseText,\n          status: baseRequester.status,\n          isTimedOut: false,\n        });\n      };\n\n      baseRequester.send(request.data);\n    });\n  }\n\n  return { send };\n}\n", "import type { BrowserLocalStorageCacheItem, BrowserLocalStorageOptions, Cache, CacheEvents } from '../types';\n\nexport function createBrowserLocalStorageCache(options: BrowserLocalStorageOptions): Cache {\n  let storage: Storage;\n  // We've changed the namespace to avoid conflicts with v4, as this version is a huge breaking change\n  const namespaceKey = `algolia-client-js-${options.key}`;\n\n  function getStorage(): Storage {\n    if (storage === undefined) {\n      storage = options.localStorage || window.localStorage;\n    }\n\n    return storage;\n  }\n\n  function getNamespace<TValue>(): Record<string, TValue> {\n    return JSON.parse(getStorage().getItem(namespaceKey) || '{}');\n  }\n\n  function setNamespace(namespace: Record<string, any>): void {\n    getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n  }\n\n  function removeOutdatedCacheItems(): void {\n    const timeToLive = options.timeToLive ? options.timeToLive * 1000 : null;\n    const namespace = getNamespace<BrowserLocalStorageCacheItem>();\n\n    const filteredNamespaceWithoutOldFormattedCacheItems = Object.fromEntries(\n      Object.entries(namespace).filter(([, cacheItem]) => {\n        return cacheItem.timestamp !== undefined;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutOldFormattedCacheItems);\n\n    if (!timeToLive) {\n      return;\n    }\n\n    const filteredNamespaceWithoutExpiredItems = Object.fromEntries(\n      Object.entries(filteredNamespaceWithoutOldFormattedCacheItems).filter(([, cacheItem]) => {\n        const currentTimestamp = new Date().getTime();\n        const isExpired = cacheItem.timestamp + timeToLive < currentTimestamp;\n\n        return !isExpired;\n      }),\n    );\n\n    setNamespace(filteredNamespaceWithoutExpiredItems);\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: () => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return Promise.resolve()\n        .then(() => {\n          removeOutdatedCacheItems();\n\n          return getNamespace<Promise<BrowserLocalStorageCacheItem>>()[JSON.stringify(key)];\n        })\n        .then((value) => {\n          return Promise.all([value ? value.value : defaultValue(), value !== undefined]);\n        })\n        .then(([value, exists]) => {\n          return Promise.all([value, exists || events.miss(value)]);\n        })\n        .then(([value]) => value);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        namespace[JSON.stringify(key)] = {\n          timestamp: new Date().getTime(),\n          value,\n        };\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n\n        return value;\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n\n        delete namespace[JSON.stringify(key)];\n\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n      });\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve().then(() => {\n        getStorage().removeItem(namespaceKey);\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents } from '../types';\n\nexport function createNullCache(): Cache {\n  return {\n    get<TValue>(\n      _key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const value = defaultValue();\n\n      return value.then((result) => Promise.all([result, events.miss(result)])).then(([result]) => result);\n    },\n\n    set<TValue>(_key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return Promise.resolve(value);\n    },\n\n    delete(_key: Record<string, any> | string): Promise<void> {\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Cache, CacheEvents, FallbackableCacheOptions } from '../types';\nimport { createNullCache } from './createNullCache';\n\nexport function createFallbackableCache(options: FallbackableCacheOptions): Cache {\n  const caches = [...options.caches];\n  const current = caches.shift();\n\n  if (current === undefined) {\n    return createNullCache();\n  }\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      return current.get(key, defaultValue, events).catch(() => {\n        return createFallbackableCache({ caches }).get(key, defaultValue, events);\n      });\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      return current.set(key, value).catch(() => {\n        return createFallbackableCache({ caches }).set(key, value);\n      });\n    },\n\n    delete(key: Record<string, any> | string): Promise<void> {\n      return current.delete(key).catch(() => {\n        return createFallbackableCache({ caches }).delete(key);\n      });\n    },\n\n    clear(): Promise<void> {\n      return current.clear().catch(() => {\n        return createFallbackableCache({ caches }).clear();\n      });\n    },\n  };\n}\n", "import type { C<PERSON>, CacheEvents, MemoryCacheOptions } from '../types';\n\nexport function createMemoryCache(options: MemoryCacheOptions = { serializable: true }): Cache {\n  let cache: Record<string, any> = {};\n\n  return {\n    get<TValue>(\n      key: Record<string, any> | string,\n      defaultValue: () => Promise<TValue>,\n      events: CacheEvents<TValue> = {\n        miss: (): Promise<void> => Promise.resolve(),\n      },\n    ): Promise<TValue> {\n      const keyAsString = JSON.stringify(key);\n\n      if (keyAsString in cache) {\n        return Promise.resolve(options.serializable ? JSON.parse(cache[keyAsString]) : cache[keyAsString]);\n      }\n\n      const promise = defaultValue();\n\n      return promise.then((value: TValue) => events.miss(value)).then(() => promise);\n    },\n\n    set<TValue>(key: Record<string, any> | string, value: TValue): Promise<TValue> {\n      cache[JSON.stringify(key)] = options.serializable ? JSON.stringify(value) : value;\n\n      return Promise.resolve(value);\n    },\n\n    delete(key: Record<string, unknown> | string): Promise<void> {\n      delete cache[JSON.stringify(key)];\n\n      return Promise.resolve();\n    },\n\n    clear(): Promise<void> {\n      cache = {};\n\n      return Promise.resolve();\n    },\n  };\n}\n", "export const DEFAULT_CONNECT_TIMEOUT_BROWSER = 1000;\nexport const DEFAULT_READ_TIMEOUT_BROWSER = 2000;\nexport const DEFAULT_WRITE_TIMEOUT_BROWSER = 30000;\n\nexport const DEFAULT_CONNECT_TIMEOUT_NODE = 2000;\nexport const DEFAULT_READ_TIMEOUT_NODE = 5000;\nexport const DEFAULT_WRITE_TIMEOUT_NODE = 30000;\n", "import type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport function createAlgoliaAgent(version: string): AlgoliaAgent {\n  const algoliaAgent = {\n    value: `Algolia for JavaScript (${version})`,\n    add(options: AlgoliaAgentOptions): AlgoliaAgent {\n      const addedAlgoliaAgent = `; ${options.segment}${options.version !== undefined ? ` (${options.version})` : ''}`;\n\n      if (algoliaAgent.value.indexOf(addedAlgoliaAgent) === -1) {\n        algoliaAgent.value = `${algoliaAgent.value}${addedAlgoliaAgent}`;\n      }\n\n      return algoliaAgent;\n    },\n  };\n\n  return algoliaAgent;\n}\n", "import type { AuthMode, Headers, QueryParameters } from './types';\n\nexport function createAuth(\n  appId: string,\n  apiKey: string,\n  authMode: AuthMode = 'WithinHeaders',\n): {\n  readonly headers: () => Headers;\n  readonly queryParameters: () => QueryParameters;\n} {\n  const credentials = {\n    'x-algolia-api-key': apiKey,\n    'x-algolia-application-id': appId,\n  };\n\n  return {\n    headers(): Headers {\n      return authMode === 'WithinHeaders' ? credentials : {};\n    },\n\n    queryParameters(): QueryParameters {\n      return authMode === 'WithinQueryParameters' ? credentials : {};\n    },\n  };\n}\n", "import type { CreateIterablePromise } from './types/createIterablePromise';\n\n/**\n * Helper: Returns the promise of a given `func` to iterate on, based on a given `validate` condition.\n *\n * @param createIterator - The createIterator options.\n * @param createIterator.func - The function to run, which returns a promise.\n * @param createIterator.validate - The validator function. It receives the resolved return of `func`.\n * @param createIterator.aggregator - The function that runs right after the `func` method has been executed, allows you to do anything with the response before `validate`.\n * @param createIterator.error - The `validate` condition to throw an error, and its message.\n * @param createIterator.timeout - The function to decide how long to wait between iterations.\n */\nexport function createIterablePromise<TResponse>({\n  func,\n  validate,\n  aggregator,\n  error,\n  timeout = (): number => 0,\n}: CreateIterablePromise<TResponse>): Promise<TResponse> {\n  const retry = (previousResponse?: TResponse | undefined): Promise<TResponse> => {\n    return new Promise<TResponse>((resolve, reject) => {\n      func(previousResponse)\n        .then(async (response) => {\n          if (aggregator) {\n            await aggregator(response);\n          }\n\n          if (await validate(response)) {\n            return resolve(response);\n          }\n\n          if (error && (await error.validate(response))) {\n            return reject(new Error(await error.message(response)));\n          }\n\n          return setTimeout(\n            () => {\n              retry(response).then(resolve).catch(reject);\n            },\n            await timeout(),\n          );\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  };\n\n  return retry();\n}\n", "import { createAlgoliaAgent } from './createAlgoliaAgent';\nimport type { AlgoliaAgent, AlgoliaAgentOptions } from './types';\n\nexport type GetAlgoliaAgent = {\n  algoliaAgents: AlgoliaAgentOptions[];\n  client: string;\n  version: string;\n};\n\nexport function getAlgoliaAgent({ algoliaAgents, client, version }: GetAlgoliaAgent): AlgoliaAgent {\n  const defaultAlgoliaAgent = createAlgoliaAgent(version).add({\n    segment: client,\n    version,\n  });\n\n  algoliaAgents.forEach((algoliaAgent) => defaultAlgoliaAgent.add(algoliaAgent));\n\n  return defaultAlgoliaAgent;\n}\n", "import type { Logger } from '../types/logger';\n\nexport function createNullLogger(): Logger {\n  return {\n    debug(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    info(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n    error(_message: string, _args?: any | undefined): Promise<void> {\n      return Promise.resolve();\n    },\n  };\n}\n", "import type { Host, StatefulHost } from '../types';\n\n// By default, API Clients at Algolia have expiration delay of 5 mins.\n// In the JavaScript client, we have 2 mins.\nconst EXPIRATION_DELAY = 2 * 60 * 1000;\n\nexport function createStatefulHost(host: Host, status: StatefulHost['status'] = 'up'): StatefulHost {\n  const lastUpdate = Date.now();\n\n  function isUp(): boolean {\n    return status === 'up' || Date.now() - lastUpdate > EXPIRATION_DELAY;\n  }\n\n  function isTimedOut(): boolean {\n    return status === 'timed out' && Date.now() - lastUpdate <= EXPIRATION_DELAY;\n  }\n\n  return { ...host, status, lastUpdate, isUp, isTimedOut };\n}\n", "import type { Response, StackFrame } from '../types';\n\nexport class AlgoliaError extends Error {\n  override name: string = 'AlgoliaError';\n\n  constructor(message: string, name: string) {\n    super(message);\n\n    if (name) {\n      this.name = name;\n    }\n  }\n}\n\nexport class IndexNotFoundError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} does not exist`, 'IndexNotFoundError');\n  }\n}\n\nexport class IndicesInSameAppError extends AlgoliaError {\n  constructor() {\n    super('Indices are in the same application. Use operationIndex instead.', 'IndicesInSameAppError');\n  }\n}\n\nexport class IndexAlreadyExistsError extends AlgoliaError {\n  constructor(indexName: string) {\n    super(`${indexName} index already exists.`, 'IndexAlreadyExistsError');\n  }\n}\n\nexport class ErrorWithStackTrace extends AlgoliaError {\n  stackTrace: StackFrame[];\n\n  constructor(message: string, stackTrace: StackFrame[], name: string) {\n    super(message, name);\n    // the array and object should be frozen to reflect the stackTrace at the time of the error\n    this.stackTrace = stackTrace;\n  }\n}\n\nexport class RetryError extends ErrorWithStackTrace {\n  constructor(stackTrace: StackFrame[]) {\n    super(\n      'Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.',\n      stackTrace,\n      'RetryError',\n    );\n  }\n}\n\nexport class ApiError extends ErrorWithStackTrace {\n  status: number;\n\n  constructor(message: string, status: number, stackTrace: StackFrame[], name = 'ApiError') {\n    super(message, stackTrace, name);\n    this.status = status;\n  }\n}\n\nexport class DeserializationError extends AlgoliaError {\n  response: Response;\n\n  constructor(message: string, response: Response) {\n    super(message, 'DeserializationError');\n    this.response = response;\n  }\n}\n\nexport type DetailedErrorWithMessage = {\n  message: string;\n  label: string;\n};\n\nexport type DetailedErrorWithTypeID = {\n  id: string;\n  type: string;\n  name?: string | undefined;\n};\n\nexport type DetailedError = {\n  code: string;\n  details?: DetailedErrorWithMessage[] | DetailedErrorWithTypeID[] | undefined;\n};\n\n// DetailedApiError is only used by the ingestion client to return more informative error, other clients will use ApiClient.\nexport class DetailedApiError extends ApiError {\n  error: DetailedError;\n\n  constructor(message: string, status: number, error: DetailedError, stackTrace: StackFrame[]) {\n    super(message, status, stackTrace, 'DetailedApiError');\n    this.error = error;\n  }\n}\n", "import type { Headers, Host, QueryParameters, Request, RequestOptions, Response, StackFrame } from '../types';\nimport { ApiError, DeserializationError, DetailedApiError } from './errors';\n\nexport function shuffle<TData>(array: TData[]): TData[] {\n  const shuffledArray = array;\n\n  for (let c = array.length - 1; c > 0; c--) {\n    const b = Math.floor(Math.random() * (c + 1));\n    const a = array[c];\n\n    shuffledArray[c] = array[b];\n    shuffledArray[b] = a;\n  }\n\n  return shuffledArray;\n}\n\nexport function serializeUrl(host: Host, path: string, queryParameters: QueryParameters): string {\n  const queryParametersAsString = serializeQueryParameters(queryParameters);\n  let url = `${host.protocol}://${host.url}${host.port ? `:${host.port}` : ''}/${\n    path.charAt(0) === '/' ? path.substring(1) : path\n  }`;\n\n  if (queryParametersAsString.length) {\n    url += `?${queryParametersAsString}`;\n  }\n\n  return url;\n}\n\nexport function serializeQueryParameters(parameters: QueryParameters): string {\n  return Object.keys(parameters)\n    .filter((key) => parameters[key] !== undefined)\n    .sort()\n    .map(\n      (key) =>\n        `${key}=${encodeURIComponent(\n          Object.prototype.toString.call(parameters[key]) === '[object Array]'\n            ? parameters[key].join(',')\n            : parameters[key],\n        ).replace(/\\+/g, '%20')}`,\n    )\n    .join('&');\n}\n\nexport function serializeData(request: Request, requestOptions: RequestOptions): string | undefined {\n  if (request.method === 'GET' || (request.data === undefined && requestOptions.data === undefined)) {\n    return undefined;\n  }\n\n  const data = Array.isArray(request.data) ? request.data : { ...request.data, ...requestOptions.data };\n\n  return JSON.stringify(data);\n}\n\nexport function serializeHeaders(\n  baseHeaders: Headers,\n  requestHeaders: Headers,\n  requestOptionsHeaders?: Headers | undefined,\n): Headers {\n  const headers: Headers = {\n    Accept: 'application/json',\n    ...baseHeaders,\n    ...requestHeaders,\n    ...requestOptionsHeaders,\n  };\n  const serializedHeaders: Headers = {};\n\n  Object.keys(headers).forEach((header) => {\n    const value = headers[header];\n    serializedHeaders[header.toLowerCase()] = value;\n  });\n\n  return serializedHeaders;\n}\n\nexport function deserializeSuccess<TObject>(response: Response): TObject {\n  try {\n    return JSON.parse(response.content);\n  } catch (e) {\n    throw new DeserializationError((e as Error).message, response);\n  }\n}\n\nexport function deserializeFailure({ content, status }: Response, stackFrame: StackFrame[]): Error {\n  try {\n    const parsed = JSON.parse(content);\n    if ('error' in parsed) {\n      return new DetailedApiError(parsed.message, status, parsed.error, stackFrame);\n    }\n    return new ApiError(parsed.message, status, stackFrame);\n  } catch {\n    // ..\n  }\n  return new ApiError(content, status, stackFrame);\n}\n", "import type { Response } from '../types';\n\nexport function isNetworkError({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return !isTimedOut && ~~status === 0;\n}\n\nexport function isRetryable({ isTimedOut, status }: Omit<Response, 'content'>): boolean {\n  return isTimedOut || isNetworkError({ isTimedOut, status }) || (~~(status / 100) !== 2 && ~~(status / 100) !== 4);\n}\n\nexport function isSuccess({ status }: Pick<Response, 'status'>): boolean {\n  return ~~(status / 100) === 2;\n}\n", "import type { Head<PERSON>, StackFrame } from '../types';\n\nexport function stackTraceWithoutCredentials(stackTrace: StackFrame[]): StackFrame[] {\n  return stackTrace.map((stackFrame) => stackFrameWithoutCredentials(stackFrame));\n}\n\nexport function stackFrameWithoutCredentials(stackFrame: StackFrame): StackFrame {\n  const modifiedHeaders: Headers = stackFrame.request.headers['x-algolia-api-key']\n    ? { 'x-algolia-api-key': '*****' }\n    : {};\n\n  return {\n    ...stackFrame,\n    request: {\n      ...stackFrame.request,\n      headers: {\n        ...stackFrame.request.headers,\n        ...modifiedHeaders,\n      },\n    },\n  };\n}\n", "import type {\n  EndRequest,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n  Response,\n  StackFrame,\n  Transporter,\n  TransporterOptions,\n} from '../types';\nimport { createStatefulHost } from './createStatefulHost';\nimport { RetryError } from './errors';\nimport { deserializeFailure, deserializeSuccess, serializeData, serializeHeaders, serializeUrl } from './helpers';\nimport { isRetryable, isSuccess } from './responses';\nimport { stackFrameWithoutCredentials, stackTraceWithoutCredentials } from './stackTrace';\n\ntype RetryableOptions = {\n  hosts: Host[];\n  getTimeout: (retryCount: number, timeout: number) => number;\n};\n\nexport function createTransporter({\n  hosts,\n  hostsCache,\n  baseHeaders,\n  logger,\n  baseQueryParameters,\n  algoliaAgent,\n  timeouts,\n  requester,\n  requestsCache,\n  responsesCache,\n}: TransporterOptions): Transporter {\n  async function createRetryableOptions(compatibleHosts: Host[]): Promise<RetryableOptions> {\n    const statefulHosts = await Promise.all(\n      compatibleHosts.map((compatibleHost) => {\n        return hostsCache.get(compatibleHost, () => {\n          return Promise.resolve(createStatefulHost(compatibleHost));\n        });\n      }),\n    );\n    const hostsUp = statefulHosts.filter((host) => host.isUp());\n    const hostsTimedOut = statefulHosts.filter((host) => host.isTimedOut());\n\n    // Note, we put the hosts that previously timed out on the end of the list.\n    const hostsAvailable = [...hostsUp, ...hostsTimedOut];\n    const compatibleHostsAvailable = hostsAvailable.length > 0 ? hostsAvailable : compatibleHosts;\n\n    return {\n      hosts: compatibleHostsAvailable,\n      getTimeout(timeoutsCount: number, baseTimeout: number): number {\n        /**\n         * Imagine that you have 4 hosts, if timeouts will increase\n         * on the following way: 1 (timed out) > 4 (timed out) > 5 (200).\n         *\n         * Note that, the very next request, we start from the previous timeout.\n         *\n         *  5 (timed out) > 6 (timed out) > 7 ...\n         *\n         * This strategy may need to be reviewed, but is the strategy on the our\n         * current v3 version.\n         */\n        const timeoutMultiplier =\n          hostsTimedOut.length === 0 && timeoutsCount === 0 ? 1 : hostsTimedOut.length + 3 + timeoutsCount;\n\n        return timeoutMultiplier * baseTimeout;\n      },\n    };\n  }\n\n  async function retryableRequest<TResponse>(\n    request: Request,\n    requestOptions: RequestOptions,\n    isRead = true,\n  ): Promise<TResponse> {\n    const stackTrace: StackFrame[] = [];\n\n    /**\n     * First we prepare the payload that do not depend from hosts.\n     */\n    const data = serializeData(request, requestOptions);\n    const headers = serializeHeaders(baseHeaders, request.headers, requestOptions.headers);\n\n    // On `GET`, the data is proxied to query parameters.\n    const dataQueryParameters: QueryParameters =\n      request.method === 'GET'\n        ? {\n            ...request.data,\n            ...requestOptions.data,\n          }\n        : {};\n\n    const queryParameters: QueryParameters = {\n      ...baseQueryParameters,\n      ...request.queryParameters,\n      ...dataQueryParameters,\n    };\n\n    if (algoliaAgent.value) {\n      queryParameters['x-algolia-agent'] = algoliaAgent.value;\n    }\n\n    if (requestOptions && requestOptions.queryParameters) {\n      for (const key of Object.keys(requestOptions.queryParameters)) {\n        // We want to keep `undefined` and `null` values,\n        // but also avoid stringifying `object`s, as they are\n        // handled in the `serializeUrl` step right after.\n        if (\n          !requestOptions.queryParameters[key] ||\n          Object.prototype.toString.call(requestOptions.queryParameters[key]) === '[object Object]'\n        ) {\n          queryParameters[key] = requestOptions.queryParameters[key];\n        } else {\n          queryParameters[key] = requestOptions.queryParameters[key].toString();\n        }\n      }\n    }\n\n    let timeoutsCount = 0;\n\n    const retry = async (\n      retryableHosts: Host[],\n      getTimeout: (timeoutsCount: number, timeout: number) => number,\n    ): Promise<TResponse> => {\n      /**\n       * We iterate on each host, until there is no host left.\n       */\n      const host = retryableHosts.pop();\n      if (host === undefined) {\n        throw new RetryError(stackTraceWithoutCredentials(stackTrace));\n      }\n\n      const timeout = { ...timeouts, ...requestOptions.timeouts };\n\n      const payload: EndRequest = {\n        data,\n        headers,\n        method: request.method,\n        url: serializeUrl(host, request.path, queryParameters),\n        connectTimeout: getTimeout(timeoutsCount, timeout.connect),\n        responseTimeout: getTimeout(timeoutsCount, isRead ? timeout.read : timeout.write),\n      };\n\n      /**\n       * The stackFrame is pushed to the stackTrace so we\n       * can have information about onRetry and onFailure\n       * decisions.\n       */\n      const pushToStackTrace = (response: Response): StackFrame => {\n        const stackFrame: StackFrame = {\n          request: payload,\n          response,\n          host,\n          triesLeft: retryableHosts.length,\n        };\n\n        stackTrace.push(stackFrame);\n\n        return stackFrame;\n      };\n\n      const response = await requester.send(payload);\n\n      if (isRetryable(response)) {\n        const stackFrame = pushToStackTrace(response);\n\n        // If response is a timeout, we increase the number of timeouts so we can increase the timeout later.\n        if (response.isTimedOut) {\n          timeoutsCount++;\n        }\n        /**\n         * Failures are individually sent to the logger, allowing\n         * the end user to debug / store stack frames even\n         * when a retry error does not happen.\n         */\n        logger.info('Retryable failure', stackFrameWithoutCredentials(stackFrame));\n\n        /**\n         * We also store the state of the host in failure cases. If the host, is\n         * down it will remain down for the next 2 minutes. In a timeout situation,\n         * this host will be added end of the list of hosts on the next request.\n         */\n        await hostsCache.set(host, createStatefulHost(host, response.isTimedOut ? 'timed out' : 'down'));\n\n        return retry(retryableHosts, getTimeout);\n      }\n\n      if (isSuccess(response)) {\n        return deserializeSuccess(response);\n      }\n\n      pushToStackTrace(response);\n      throw deserializeFailure(response, stackTrace);\n    };\n\n    /**\n     * Finally, for each retryable host perform request until we got a non\n     * retryable response. Some notes here:\n     *\n     * 1. The reverse here is applied so we can apply a `pop` later on => more performant.\n     * 2. We also get from the retryable options a timeout multiplier that is tailored\n     * for the current context.\n     */\n    const compatibleHosts = hosts.filter(\n      (host) => host.accept === 'readWrite' || (isRead ? host.accept === 'read' : host.accept === 'write'),\n    );\n    const options = await createRetryableOptions(compatibleHosts);\n\n    return retry([...options.hosts].reverse(), options.getTimeout);\n  }\n\n  function createRequest<TResponse>(request: Request, requestOptions: RequestOptions = {}): Promise<TResponse> {\n    /**\n     * A read request is either a `GET` request, or a request that we make\n     * via the `read` transporter (e.g. `search`).\n     */\n    const isRead = request.useReadTransporter || request.method === 'GET';\n    if (!isRead) {\n      /**\n       * On write requests, no cache mechanisms are applied, and we\n       * proxy the request immediately to the requester.\n       */\n      return retryableRequest<TResponse>(request, requestOptions, isRead);\n    }\n\n    const createRetryableRequest = (): Promise<TResponse> => {\n      /**\n       * Then, we prepare a function factory that contains the construction of\n       * the retryable request. At this point, we may *not* perform the actual\n       * request. But we want to have the function factory ready.\n       */\n      return retryableRequest<TResponse>(request, requestOptions);\n    };\n\n    /**\n     * Once we have the function factory ready, we need to determine of the\n     * request is \"cacheable\" - should be cached. Note that, once again,\n     * the user can force this option.\n     */\n    const cacheable = requestOptions.cacheable || request.cacheable;\n\n    /**\n     * If is not \"cacheable\", we immediately trigger the retryable request, no\n     * need to check cache implementations.\n     */\n    if (cacheable !== true) {\n      return createRetryableRequest();\n    }\n\n    /**\n     * If the request is \"cacheable\", we need to first compute the key to ask\n     * the cache implementations if this request is on progress or if the\n     * response already exists on the cache.\n     */\n    const key = {\n      request,\n      requestOptions,\n      transporter: {\n        queryParameters: baseQueryParameters,\n        headers: baseHeaders,\n      },\n    };\n\n    /**\n     * With the computed key, we first ask the responses cache\n     * implementation if this request was been resolved before.\n     */\n    return responsesCache.get(\n      key,\n      () => {\n        /**\n         * If the request has never resolved before, we actually ask if there\n         * is a current request with the same key on progress.\n         */\n        return requestsCache.get(key, () =>\n          /**\n           * Finally, if there is no request in progress with the same key,\n           * this `createRetryableRequest()` will actually trigger the\n           * retryable request.\n           */\n          requestsCache\n            .set(key, createRetryableRequest())\n            .then(\n              (response) => Promise.all([requestsCache.delete(key), response]),\n              (err) => Promise.all([requestsCache.delete(key), Promise.reject(err)]),\n            )\n            .then(([_, response]) => response),\n        );\n      },\n      {\n        /**\n         * Of course, once we get this response back from the server, we\n         * tell response cache to actually store the received response\n         * to be used later.\n         */\n        miss: (response) => responsesCache.set(key, response),\n      },\n    );\n  }\n\n  return {\n    hostsCache,\n    requester,\n    timeouts,\n    logger,\n    algoliaAgent,\n    baseHeaders,\n    baseQueryParameters,\n    hosts,\n    request: createRequest,\n    requestsCache,\n    responsesCache,\n  };\n}\n", "export const LogLevelEnum: Readonly<Record<string, LogLevelType>> = {\n  Debug: 1,\n  Info: 2,\n  Error: 3,\n};\n\nexport type LogLevelType = 1 | 2 | 3;\n\nexport type Logger = {\n  /**\n   * Logs debug messages.\n   */\n  debug: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs info messages.\n   */\n  info: (message: string, args?: any | undefined) => Promise<void>;\n\n  /**\n   * Logs error messages.\n   */\n  error: (message: string, args?: any | undefined) => Promise<void>;\n};\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  ApiError,\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createIterablePromise, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { Authentication } from '../model/authentication';\nimport type { AuthenticationCreate } from '../model/authenticationCreate';\nimport type { AuthenticationCreateResponse } from '../model/authenticationCreateResponse';\nimport type { AuthenticationSearch } from '../model/authenticationSearch';\n\nimport type { AuthenticationUpdateResponse } from '../model/authenticationUpdateResponse';\nimport type { DeleteResponse } from '../model/deleteResponse';\nimport type { Destination } from '../model/destination';\nimport type { DestinationCreate } from '../model/destinationCreate';\nimport type { DestinationCreateResponse } from '../model/destinationCreateResponse';\nimport type { DestinationSearch } from '../model/destinationSearch';\n\nimport type { DestinationUpdateResponse } from '../model/destinationUpdateResponse';\n\nimport type { Event } from '../model/event';\n\nimport type { ListAuthenticationsResponse } from '../model/listAuthenticationsResponse';\nimport type { ListDestinationsResponse } from '../model/listDestinationsResponse';\nimport type { ListEventsResponse } from '../model/listEventsResponse';\nimport type { ListSourcesResponse } from '../model/listSourcesResponse';\nimport type { ListTasksResponse } from '../model/listTasksResponse';\nimport type { ListTasksResponseV1 } from '../model/listTasksResponseV1';\nimport type { ListTransformationsResponse } from '../model/listTransformationsResponse';\n\nimport type { Run } from '../model/run';\nimport type { RunListResponse } from '../model/runListResponse';\nimport type { RunResponse } from '../model/runResponse';\n\nimport type { RunSourceResponse } from '../model/runSourceResponse';\n\nimport type { Source } from '../model/source';\nimport type { SourceCreate } from '../model/sourceCreate';\nimport type { SourceCreateResponse } from '../model/sourceCreateResponse';\nimport type { SourceSearch } from '../model/sourceSearch';\n\nimport type { SourceUpdateResponse } from '../model/sourceUpdateResponse';\nimport type { Task } from '../model/task';\nimport type { TaskCreate } from '../model/taskCreate';\nimport type { TaskCreateResponse } from '../model/taskCreateResponse';\nimport type { TaskCreateV1 } from '../model/taskCreateV1';\nimport type { TaskSearch } from '../model/taskSearch';\n\nimport type { TaskUpdateResponse } from '../model/taskUpdateResponse';\n\nimport type { TaskV1 } from '../model/taskV1';\nimport type { Transformation } from '../model/transformation';\nimport type { TransformationCreate } from '../model/transformationCreate';\nimport type { TransformationCreateResponse } from '../model/transformationCreateResponse';\nimport type { TransformationSearch } from '../model/transformationSearch';\n\nimport type { TransformationTry } from '../model/transformationTry';\nimport type { TransformationTryResponse } from '../model/transformationTryResponse';\nimport type { TransformationUpdateResponse } from '../model/transformationUpdateResponse';\n\nimport type { WatchResponse } from '../model/watchResponse';\n\nimport type {\n  ChunkedPushOptions,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteAuthenticationProps,\n  DeleteDestinationProps,\n  DeleteSourceProps,\n  DeleteTaskProps,\n  DeleteTaskV1Props,\n  DeleteTransformationProps,\n  DisableTaskProps,\n  DisableTaskV1Props,\n  EnableTaskProps,\n  EnableTaskV1Props,\n  GetAuthenticationProps,\n  GetDestinationProps,\n  GetEventProps,\n  GetRunProps,\n  GetSourceProps,\n  GetTaskProps,\n  GetTaskV1Props,\n  GetTransformationProps,\n  ListAuthenticationsProps,\n  ListDestinationsProps,\n  ListEventsProps,\n  ListRunsProps,\n  ListSourcesProps,\n  ListTasksProps,\n  ListTasksV1Props,\n  ListTransformationsProps,\n  PushProps,\n  PushTaskProps,\n  RunSourceProps,\n  RunTaskProps,\n  RunTaskV1Props,\n  TriggerDockerSourceDiscoverProps,\n  TryTransformationBeforeUpdateProps,\n  UpdateAuthenticationProps,\n  UpdateDestinationProps,\n  UpdateSourceProps,\n  UpdateTaskProps,\n  UpdateTaskV1Props,\n  UpdateTransformationProps,\n  ValidateSourceBeforeUpdateProps,\n} from '../model/clientMethodProps';\n\nimport type { OnDemandTrigger } from '../model/onDemandTrigger';\nimport type { PushTaskRecords } from '../model/pushTaskRecords';\nimport type { ScheduleTrigger } from '../model/scheduleTrigger';\nimport type { SubscriptionTrigger } from '../model/subscriptionTrigger';\nimport type { TaskCreateTrigger } from '../model/taskCreateTrigger';\nimport type { Trigger } from '../model/trigger';\n\nexport const apiClientVersion = '1.30.0';\n\nexport const REGIONS = ['eu', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region: Region };\n\nfunction getDefaultHosts(region: Region): Host[] {\n  const url = 'data.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\n/**\n * Guard: Return strongly typed specific OnDemandTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific OnDemandTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isOnDemandTrigger(trigger: TaskCreateTrigger | Trigger): trigger is OnDemandTrigger {\n  return trigger.type === 'onDemand';\n}\n\n/**\n * Guard: Return strongly typed specific ScheduleTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific ScheduleTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isScheduleTrigger(trigger: TaskCreateTrigger | Trigger): trigger is ScheduleTrigger {\n  return trigger.type === 'schedule';\n}\n\n/**\n * Guard: Return strongly typed specific SubscriptionTrigger for a given Trigger.\n *\n * @summary Guard method that returns a strongly typed specific SubscriptionTrigger for a given Trigger.\n * @param trigger - The given Task Trigger.\n */\nexport function isSubscriptionTrigger(trigger: TaskCreateTrigger | Trigger): trigger is SubscriptionTrigger {\n  return trigger.type === 'subscription';\n}\n\nexport function createIngestionClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Ingestion',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `push` requests by leveraging the Transformation pipeline setup in the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/).\n     *\n     * @summary Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     * @param chunkedPush - The `chunkedPush` object.\n     * @param chunkedPush.indexName - The `indexName` to replace `objects` in.\n     * @param chunkedPush.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param chunkedPush.action - The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.\n     * @param chunkedPush.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param chunkedPush.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param chunkedPush.referenceIndexName - This is required when targeting an index that does not have a push connector setup (e.g. a tmp index), but you wish to attach another index's transformation to it (e.g. the source index name).\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getEvent` method and merged with the transporter requestOptions.\n     */\n    async chunkedPush(\n      {\n        indexName,\n        objects,\n        action = 'addObject',\n        waitForTasks,\n        batchSize = 1000,\n        referenceIndexName,\n      }: ChunkedPushOptions,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<WatchResponse>> {\n      let records: Array<PushTaskRecords> = [];\n      const responses: Array<WatchResponse> = [];\n\n      const objectEntries = objects.entries();\n      for (const [i, obj] of objectEntries) {\n        records.push(obj as PushTaskRecords);\n        if (records.length === batchSize || i === objects.length - 1) {\n          responses.push(\n            await this.push({ indexName, pushTaskPayload: { action, records }, referenceIndexName }, requestOptions),\n          );\n          records = [];\n        }\n      }\n\n      let retryCount = 0;\n\n      if (waitForTasks) {\n        for (const resp of responses) {\n          if (!resp.eventID) {\n            throw new Error('received unexpected response from the push endpoint, eventID must not be undefined');\n          }\n\n          await createIterablePromise({\n            func: async () => {\n              if (resp.eventID === undefined || !resp.eventID) {\n                throw new Error('received unexpected response from the push endpoint, eventID must not be undefined');\n              }\n\n              return this.getEvent({ runID: resp.runID, eventID: resp.eventID }).catch((error: ApiError) => {\n                if (error.status === 404) {\n                  return undefined;\n                }\n\n                throw error;\n              });\n            },\n            validate: (response) => response !== undefined,\n            aggregator: () => (retryCount += 1),\n            error: {\n              validate: () => retryCount >= 50,\n              message: () => `The maximum number of retries exceeded. (${retryCount}/${50})`,\n            },\n            timeout: (): number => Math.min(retryCount * 500, 5000),\n          });\n        }\n      }\n\n      return responses;\n    },\n\n    /**\n     * Creates a new authentication resource.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param authenticationCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createAuthentication(\n      authenticationCreate: AuthenticationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<AuthenticationCreateResponse> {\n      if (!authenticationCreate) {\n        throw new Error('Parameter `authenticationCreate` is required when calling `createAuthentication`.');\n      }\n\n      if (!authenticationCreate.type) {\n        throw new Error('Parameter `authenticationCreate.type` is required when calling `createAuthentication`.');\n      }\n      if (!authenticationCreate.name) {\n        throw new Error('Parameter `authenticationCreate.name` is required when calling `createAuthentication`.');\n      }\n      if (!authenticationCreate.input) {\n        throw new Error('Parameter `authenticationCreate.input` is required when calling `createAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new destination.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param destinationCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createDestination(\n      destinationCreate: DestinationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<DestinationCreateResponse> {\n      if (!destinationCreate) {\n        throw new Error('Parameter `destinationCreate` is required when calling `createDestination`.');\n      }\n\n      if (!destinationCreate.type) {\n        throw new Error('Parameter `destinationCreate.type` is required when calling `createDestination`.');\n      }\n      if (!destinationCreate.name) {\n        throw new Error('Parameter `destinationCreate.name` is required when calling `createDestination`.');\n      }\n      if (!destinationCreate.input) {\n        throw new Error('Parameter `destinationCreate.input` is required when calling `createDestination`.');\n      }\n\n      const requestPath = '/1/destinations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new source.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createSource(sourceCreate: SourceCreate, requestOptions?: RequestOptions): Promise<SourceCreateResponse> {\n      if (!sourceCreate) {\n        throw new Error('Parameter `sourceCreate` is required when calling `createSource`.');\n      }\n\n      if (!sourceCreate.type) {\n        throw new Error('Parameter `sourceCreate.type` is required when calling `createSource`.');\n      }\n      if (!sourceCreate.name) {\n        throw new Error('Parameter `sourceCreate.name` is required when calling `createSource`.');\n      }\n\n      const requestPath = '/1/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new task.\n     * @param taskCreate - Request body for creating a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTask(taskCreate: TaskCreate, requestOptions?: RequestOptions): Promise<TaskCreateResponse> {\n      if (!taskCreate) {\n        throw new Error('Parameter `taskCreate` is required when calling `createTask`.');\n      }\n\n      if (!taskCreate.sourceID) {\n        throw new Error('Parameter `taskCreate.sourceID` is required when calling `createTask`.');\n      }\n      if (!taskCreate.destinationID) {\n        throw new Error('Parameter `taskCreate.destinationID` is required when calling `createTask`.');\n      }\n      if (!taskCreate.action) {\n        throw new Error('Parameter `taskCreate.action` is required when calling `createTask`.');\n      }\n\n      const requestPath = '/2/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new task using the v1 endpoint, please use `createTask` instead.\n     *\n     * @deprecated\n     * @param taskCreate - Request body for creating a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTaskV1(taskCreate: TaskCreateV1, requestOptions?: RequestOptions): Promise<TaskCreateResponse> {\n      if (!taskCreate) {\n        throw new Error('Parameter `taskCreate` is required when calling `createTaskV1`.');\n      }\n\n      if (!taskCreate.sourceID) {\n        throw new Error('Parameter `taskCreate.sourceID` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.destinationID) {\n        throw new Error('Parameter `taskCreate.destinationID` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.trigger) {\n        throw new Error('Parameter `taskCreate.trigger` is required when calling `createTaskV1`.');\n      }\n      if (!taskCreate.action) {\n        throw new Error('Parameter `taskCreate.action` is required when calling `createTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Creates a new transformation.\n     * @param transformationCreate - Request body for creating a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    createTransformation(\n      transformationCreate: TransformationCreate,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationCreateResponse> {\n      if (!transformationCreate) {\n        throw new Error('Parameter `transformationCreate` is required when calling `createTransformation`.');\n      }\n\n      if (!transformationCreate.name) {\n        throw new Error('Parameter `transformationCreate.name` is required when calling `createTransformation`.');\n      }\n\n      const requestPath = '/1/transformations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes an authentication resource. You can\\'t delete authentication resources that are used by a source or a destination.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteAuthentication - The deleteAuthentication object.\n     * @param deleteAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteAuthentication(\n      { authenticationID }: DeleteAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `deleteAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a destination by its ID. You can\\'t delete destinations that are referenced in tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteDestination - The deleteDestination object.\n     * @param deleteDestination.destinationID - Unique identifier of a destination.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteDestination(\n      { destinationID }: DeleteDestinationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `deleteDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a source by its ID. You can\\'t delete sources that are referenced in tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param deleteSource - The deleteSource object.\n     * @param deleteSource.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSource({ sourceID }: DeleteSourceProps, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `deleteSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a task by its ID.\n     * @param deleteTask - The deleteTask object.\n     * @param deleteTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTask({ taskID }: DeleteTaskProps, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `deleteTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a task by its ID using the v1 endpoint, please use `deleteTask` instead.\n     *\n     * @deprecated\n     * @param deleteTaskV1 - The deleteTaskV1 object.\n     * @param deleteTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTaskV1({ taskID }: DeleteTaskV1Props, requestOptions?: RequestOptions): Promise<DeleteResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `deleteTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a transformation by its ID.\n     * @param deleteTransformation - The deleteTransformation object.\n     * @param deleteTransformation.transformationID - Unique identifier of a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteTransformation(\n      { transformationID }: DeleteTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeleteResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `deleteTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Disables a task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param disableTask - The disableTask object.\n     * @param disableTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    disableTask({ taskID }: DisableTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `disableTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/disable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Disables a task using the v1 endpoint, please use `disableTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param disableTaskV1 - The disableTaskV1 object.\n     * @param disableTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    disableTaskV1({ taskID }: DisableTaskV1Props, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `disableTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/disable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Enables a task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param enableTask - The enableTask object.\n     * @param enableTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    enableTask({ taskID }: EnableTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `enableTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/enable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Enables a task using the v1 endpoint, please use `enableTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param enableTaskV1 - The enableTaskV1 object.\n     * @param enableTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    enableTaskV1({ taskID }: EnableTaskV1Props, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `enableTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/enable'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves an authentication resource by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getAuthentication - The getAuthentication object.\n     * @param getAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAuthentication(\n      { authenticationID }: GetAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Authentication> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `getAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a destination by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getDestination - The getDestination object.\n     * @param getDestination.destinationID - Unique identifier of a destination.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDestination({ destinationID }: GetDestinationProps, requestOptions?: RequestOptions): Promise<Destination> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `getDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a single task run event by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getEvent - The getEvent object.\n     * @param getEvent.runID - Unique identifier of a task run.\n     * @param getEvent.eventID - Unique identifier of an event.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getEvent({ runID, eventID }: GetEventProps, requestOptions?: RequestOptions): Promise<Event> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `getEvent`.');\n      }\n\n      if (!eventID) {\n        throw new Error('Parameter `eventID` is required when calling `getEvent`.');\n      }\n\n      const requestPath = '/1/runs/{runID}/events/{eventID}'\n        .replace('{runID}', encodeURIComponent(runID))\n        .replace('{eventID}', encodeURIComponent(eventID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a single task run by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getRun - The getRun object.\n     * @param getRun.runID - Unique identifier of a task run.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRun({ runID }: GetRunProps, requestOptions?: RequestOptions): Promise<Run> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `getRun`.');\n      }\n\n      const requestPath = '/1/runs/{runID}'.replace('{runID}', encodeURIComponent(runID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a source by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getSource - The getSource object.\n     * @param getSource.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSource({ sourceID }: GetSourceProps, requestOptions?: RequestOptions): Promise<Source> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `getSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a task by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getTask - The getTask object.\n     * @param getTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTask({ taskID }: GetTaskProps, requestOptions?: RequestOptions): Promise<Task> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a task by its ID using the v1 endpoint, please use `getTask` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param getTaskV1 - The getTaskV1 object.\n     * @param getTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTaskV1({ taskID }: GetTaskV1Props, requestOptions?: RequestOptions): Promise<TaskV1> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a transformation by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param getTransformation - The getTransformation object.\n     * @param getTransformation.transformationID - Unique identifier of a transformation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTransformation(\n      { transformationID }: GetTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Transformation> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `getTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of all authentication resources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listAuthentications - The listAuthentications object.\n     * @param listAuthentications.itemsPerPage - Number of items per page.\n     * @param listAuthentications.page - Page number of the paginated API response.\n     * @param listAuthentications.type - Type of authentication resource to retrieve.\n     * @param listAuthentications.platform - Ecommerce platform for which to retrieve authentications.\n     * @param listAuthentications.sort - Property by which to sort the list of authentications.\n     * @param listAuthentications.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listAuthentications(\n      { itemsPerPage, page, type, platform, sort, order }: ListAuthenticationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListAuthenticationsResponse> {\n      const requestPath = '/1/authentications';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (platform !== undefined) {\n        queryParameters['platform'] = platform.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of destinations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listDestinations - The listDestinations object.\n     * @param listDestinations.itemsPerPage - Number of items per page.\n     * @param listDestinations.page - Page number of the paginated API response.\n     * @param listDestinations.type - Destination type.\n     * @param listDestinations.authenticationID - Authentication ID used by destinations.\n     * @param listDestinations.transformationID - Get the list of destinations used by a transformation.\n     * @param listDestinations.sort - Property by which to sort the destinations.\n     * @param listDestinations.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listDestinations(\n      { itemsPerPage, page, type, authenticationID, transformationID, sort, order }: ListDestinationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListDestinationsResponse> {\n      const requestPath = '/1/destinations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (authenticationID !== undefined) {\n        queryParameters['authenticationID'] = authenticationID.toString();\n      }\n\n      if (transformationID !== undefined) {\n        queryParameters['transformationID'] = transformationID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of events for a task run, identified by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listEvents - The listEvents object.\n     * @param listEvents.runID - Unique identifier of a task run.\n     * @param listEvents.itemsPerPage - Number of items per page.\n     * @param listEvents.page - Page number of the paginated API response.\n     * @param listEvents.status - Event status for filtering the list of task runs.\n     * @param listEvents.type - Event type for filtering the list of task runs.\n     * @param listEvents.sort - Property by which to sort the list of task run events.\n     * @param listEvents.order - Sort order of the response, ascending or descending.\n     * @param listEvents.startDate - Date and time in RFC 3339 format for the earliest events to retrieve. By default, the current time minus three hours is used.\n     * @param listEvents.endDate - Date and time in RFC 3339 format for the latest events to retrieve. By default, the current time is used.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listEvents(\n      { runID, itemsPerPage, page, status, type, sort, order, startDate, endDate }: ListEventsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<ListEventsResponse> {\n      if (!runID) {\n        throw new Error('Parameter `runID` is required when calling `listEvents`.');\n      }\n\n      const requestPath = '/1/runs/{runID}/events'.replace('{runID}', encodeURIComponent(runID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (status !== undefined) {\n        queryParameters['status'] = status.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieve a list of task runs.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listRuns - The listRuns object.\n     * @param listRuns.itemsPerPage - Number of items per page.\n     * @param listRuns.page - Page number of the paginated API response.\n     * @param listRuns.status - Run status for filtering the list of task runs.\n     * @param listRuns.type - Run type for filtering the list of task runs.\n     * @param listRuns.taskID - Task ID for filtering the list of task runs.\n     * @param listRuns.sort - Property by which to sort the list of task runs.\n     * @param listRuns.order - Sort order of the response, ascending or descending.\n     * @param listRuns.startDate - Date in RFC 3339 format for the earliest run to retrieve. By default, the current day minus seven days is used.\n     * @param listRuns.endDate - Date in RFC 3339 format for the latest run to retrieve. By default, the current day is used.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listRuns(\n      { itemsPerPage, page, status, type, taskID, sort, order, startDate, endDate }: ListRunsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<RunListResponse> {\n      const requestPath = '/1/runs';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (status !== undefined) {\n        queryParameters['status'] = status.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (taskID !== undefined) {\n        queryParameters['taskID'] = taskID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of sources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listSources - The listSources object.\n     * @param listSources.itemsPerPage - Number of items per page.\n     * @param listSources.page - Page number of the paginated API response.\n     * @param listSources.type - Source type. Some sources require authentication.\n     * @param listSources.authenticationID - Authentication IDs of the sources to retrieve. \\'none\\' returns sources that doesn\\'t have an authentication.\n     * @param listSources.sort - Property by which to sort the list of sources.\n     * @param listSources.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listSources(\n      { itemsPerPage, page, type, authenticationID, sort, order }: ListSourcesProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListSourcesResponse> {\n      const requestPath = '/1/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      if (authenticationID !== undefined) {\n        queryParameters['authenticationID'] = authenticationID.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listTasks - The listTasks object.\n     * @param listTasks.itemsPerPage - Number of items per page.\n     * @param listTasks.page - Page number of the paginated API response.\n     * @param listTasks.action - Actions for filtering the list of tasks.\n     * @param listTasks.enabled - Whether to filter the list of tasks by the `enabled` status.\n     * @param listTasks.sourceID - Source IDs for filtering the list of tasks.\n     * @param listTasks.sourceType - Filters the tasks with the specified source type.\n     * @param listTasks.destinationID - Destination IDs for filtering the list of tasks.\n     * @param listTasks.triggerType - Type of task trigger for filtering the list of tasks.\n     * @param listTasks.withEmailNotifications - If specified, the response only includes tasks with notifications.email.enabled set to this value.\n     * @param listTasks.sort - Property by which to sort the list of tasks.\n     * @param listTasks.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTasks(\n      {\n        itemsPerPage,\n        page,\n        action,\n        enabled,\n        sourceID,\n        sourceType,\n        destinationID,\n        triggerType,\n        withEmailNotifications,\n        sort,\n        order,\n      }: ListTasksProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTasksResponse> {\n      const requestPath = '/2/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (action !== undefined) {\n        queryParameters['action'] = action.toString();\n      }\n\n      if (enabled !== undefined) {\n        queryParameters['enabled'] = enabled.toString();\n      }\n\n      if (sourceID !== undefined) {\n        queryParameters['sourceID'] = sourceID.toString();\n      }\n\n      if (sourceType !== undefined) {\n        queryParameters['sourceType'] = sourceType.toString();\n      }\n\n      if (destinationID !== undefined) {\n        queryParameters['destinationID'] = destinationID.toString();\n      }\n\n      if (triggerType !== undefined) {\n        queryParameters['triggerType'] = triggerType.toString();\n      }\n\n      if (withEmailNotifications !== undefined) {\n        queryParameters['withEmailNotifications'] = withEmailNotifications.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of tasks using the v1 endpoint, please use `getTasks` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param listTasksV1 - The listTasksV1 object.\n     * @param listTasksV1.itemsPerPage - Number of items per page.\n     * @param listTasksV1.page - Page number of the paginated API response.\n     * @param listTasksV1.action - Actions for filtering the list of tasks.\n     * @param listTasksV1.enabled - Whether to filter the list of tasks by the `enabled` status.\n     * @param listTasksV1.sourceID - Source IDs for filtering the list of tasks.\n     * @param listTasksV1.destinationID - Destination IDs for filtering the list of tasks.\n     * @param listTasksV1.triggerType - Type of task trigger for filtering the list of tasks.\n     * @param listTasksV1.sort - Property by which to sort the list of tasks.\n     * @param listTasksV1.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTasksV1(\n      { itemsPerPage, page, action, enabled, sourceID, destinationID, triggerType, sort, order }: ListTasksV1Props = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTasksResponseV1> {\n      const requestPath = '/1/tasks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (action !== undefined) {\n        queryParameters['action'] = action.toString();\n      }\n\n      if (enabled !== undefined) {\n        queryParameters['enabled'] = enabled.toString();\n      }\n\n      if (sourceID !== undefined) {\n        queryParameters['sourceID'] = sourceID.toString();\n      }\n\n      if (destinationID !== undefined) {\n        queryParameters['destinationID'] = destinationID.toString();\n      }\n\n      if (triggerType !== undefined) {\n        queryParameters['triggerType'] = triggerType.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a list of transformations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param listTransformations - The listTransformations object.\n     * @param listTransformations.itemsPerPage - Number of items per page.\n     * @param listTransformations.page - Page number of the paginated API response.\n     * @param listTransformations.sort - Property by which to sort the list of transformations.\n     * @param listTransformations.order - Sort order of the response, ascending or descending.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listTransformations(\n      { itemsPerPage, page, sort, order }: ListTransformationsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListTransformationsResponse> {\n      const requestPath = '/1/transformations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (itemsPerPage !== undefined) {\n        queryParameters['itemsPerPage'] = itemsPerPage.toString();\n      }\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (sort !== undefined) {\n        queryParameters['sort'] = sort.toString();\n      }\n\n      if (order !== undefined) {\n        queryParameters['order'] = order.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Pushes records through the Pipeline, directly to an index. You can make the call synchronous by providing the `watch` parameter, for asynchronous calls, you can use the observability endpoints and/or debugger dashboard to see the status of your task. If you want to leverage the [pre-indexing data transformation](https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/how-to/transform-your-data/), this is the recommended way of ingesting your records. This method is similar to `pushTask`, but requires an `indexName` instead of a `taskID`. If zero or many tasks are found, an error will be returned.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param push - The push object.\n     * @param push.indexName - Name of the index on which to perform the operation.\n     * @param push.pushTaskPayload - The pushTaskPayload object.\n     * @param push.watch - When provided, the push operation will be synchronous and the API will wait for the ingestion to be finished before responding.\n     * @param push.referenceIndexName - This is required when targeting an index that does not have a push connector setup (e.g. a tmp index), but you wish to attach another index\\'s transformation to it (e.g. the source index name).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    push(\n      { indexName, pushTaskPayload, watch, referenceIndexName }: PushProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `push`.');\n      }\n\n      if (!pushTaskPayload) {\n        throw new Error('Parameter `pushTaskPayload` is required when calling `push`.');\n      }\n\n      if (!pushTaskPayload.action) {\n        throw new Error('Parameter `pushTaskPayload.action` is required when calling `push`.');\n      }\n      if (!pushTaskPayload.records) {\n        throw new Error('Parameter `pushTaskPayload.records` is required when calling `push`.');\n      }\n\n      const requestPath = '/1/push/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (watch !== undefined) {\n        queryParameters['watch'] = watch.toString();\n      }\n\n      if (referenceIndexName !== undefined) {\n        queryParameters['referenceIndexName'] = referenceIndexName.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: pushTaskPayload,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Pushes records through the Pipeline, directly to an index. You can make the call synchronous by providing the `watch` parameter, for asynchronous calls, you can use the observability endpoints and/or debugger dashboard to see the status of your task. If you want to leverage the [pre-indexing data transformation](https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/how-to/transform-your-data/), this is the recommended way of ingesting your records. This method is similar to `push`, but requires a `taskID` instead of a `indexName`, which is useful when many `destinations` target the same `indexName`.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param pushTask - The pushTask object.\n     * @param pushTask.taskID - Unique identifier of a task.\n     * @param pushTask.pushTaskPayload - The pushTaskPayload object.\n     * @param pushTask.watch - When provided, the push operation will be synchronous and the API will wait for the ingestion to be finished before responding.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    pushTask(\n      { taskID, pushTaskPayload, watch }: PushTaskProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `pushTask`.');\n      }\n\n      if (!pushTaskPayload) {\n        throw new Error('Parameter `pushTaskPayload` is required when calling `pushTask`.');\n      }\n\n      if (!pushTaskPayload.action) {\n        throw new Error('Parameter `pushTaskPayload.action` is required when calling `pushTask`.');\n      }\n      if (!pushTaskPayload.records) {\n        throw new Error('Parameter `pushTaskPayload.records` is required when calling `pushTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/push'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (watch !== undefined) {\n        queryParameters['watch'] = watch.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: pushTaskPayload,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs all tasks linked to a source, only available for Shopify sources. It will create 1 run per task.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param runSource - The runSource object.\n     * @param runSource.sourceID - Unique identifier of a source.\n     * @param runSource.runSourcePayload -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runSource(\n      { sourceID, runSourcePayload }: RunSourceProps,\n      requestOptions?: RequestOptions,\n    ): Promise<RunSourceResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `runSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/run'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: runSourcePayload ? runSourcePayload : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs a task. You can check the status of task runs with the observability endpoints.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param runTask - The runTask object.\n     * @param runTask.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runTask({ taskID }: RunTaskProps, requestOptions?: RequestOptions): Promise<RunResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `runTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}/run'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Runs a task using the v1 endpoint, please use `runTask` instead. You can check the status of task runs with the observability endpoints.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param runTaskV1 - The runTaskV1 object.\n     * @param runTaskV1.taskID - Unique identifier of a task.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    runTaskV1({ taskID }: RunTaskV1Props, requestOptions?: RequestOptions): Promise<RunResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `runTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}/run'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for authentication resources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param authenticationSearch - The authenticationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchAuthentications(\n      authenticationSearch: AuthenticationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Authentication>> {\n      if (!authenticationSearch) {\n        throw new Error('Parameter `authenticationSearch` is required when calling `searchAuthentications`.');\n      }\n\n      if (!authenticationSearch.authenticationIDs) {\n        throw new Error(\n          'Parameter `authenticationSearch.authenticationIDs` is required when calling `searchAuthentications`.',\n        );\n      }\n\n      const requestPath = '/1/authentications/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for destinations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param destinationSearch - The destinationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchDestinations(\n      destinationSearch: DestinationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Destination>> {\n      if (!destinationSearch) {\n        throw new Error('Parameter `destinationSearch` is required when calling `searchDestinations`.');\n      }\n\n      if (!destinationSearch.destinationIDs) {\n        throw new Error('Parameter `destinationSearch.destinationIDs` is required when calling `searchDestinations`.');\n      }\n\n      const requestPath = '/1/destinations/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for sources.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceSearch - The sourceSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSources(sourceSearch: SourceSearch, requestOptions?: RequestOptions): Promise<Array<Source>> {\n      if (!sourceSearch) {\n        throw new Error('Parameter `sourceSearch` is required when calling `searchSources`.');\n      }\n\n      if (!sourceSearch.sourceIDs) {\n        throw new Error('Parameter `sourceSearch.sourceIDs` is required when calling `searchSources`.');\n      }\n\n      const requestPath = '/1/sources/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for tasks.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param taskSearch - The taskSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTasks(taskSearch: TaskSearch, requestOptions?: RequestOptions): Promise<Array<Task>> {\n      if (!taskSearch) {\n        throw new Error('Parameter `taskSearch` is required when calling `searchTasks`.');\n      }\n\n      if (!taskSearch.taskIDs) {\n        throw new Error('Parameter `taskSearch.taskIDs` is required when calling `searchTasks`.');\n      }\n\n      const requestPath = '/2/tasks/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for tasks using the v1 endpoint, please use `searchTasks` instead.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     *\n     * @deprecated\n     * @param taskSearch - The taskSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTasksV1(taskSearch: TaskSearch, requestOptions?: RequestOptions): Promise<Array<TaskV1>> {\n      if (!taskSearch) {\n        throw new Error('Parameter `taskSearch` is required when calling `searchTasksV1`.');\n      }\n\n      if (!taskSearch.taskIDs) {\n        throw new Error('Parameter `taskSearch.taskIDs` is required when calling `searchTasksV1`.');\n      }\n\n      const requestPath = '/1/tasks/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for transformations.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param transformationSearch - The transformationSearch object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchTransformations(\n      transformationSearch: TransformationSearch,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<Transformation>> {\n      if (!transformationSearch) {\n        throw new Error('Parameter `transformationSearch` is required when calling `searchTransformations`.');\n      }\n\n      if (!transformationSearch.transformationIDs) {\n        throw new Error(\n          'Parameter `transformationSearch.transformationIDs` is required when calling `searchTransformations`.',\n        );\n      }\n\n      const requestPath = '/1/transformations/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationSearch,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Triggers a stream-listing request for a source. Triggering stream-listing requests only works with sources with `type: docker` and `imageType: airbyte`.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param triggerDockerSourceDiscover - The triggerDockerSourceDiscover object.\n     * @param triggerDockerSourceDiscover.sourceID - Unique identifier of a source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    triggerDockerSourceDiscover(\n      { sourceID }: TriggerDockerSourceDiscoverProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/discover'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Try a transformation before creating it.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param transformationTry - The transformationTry object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    tryTransformation(\n      transformationTry: TransformationTry,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationTryResponse> {\n      if (!transformationTry) {\n        throw new Error('Parameter `transformationTry` is required when calling `tryTransformation`.');\n      }\n\n      if (!transformationTry.sampleRecord) {\n        throw new Error('Parameter `transformationTry.sampleRecord` is required when calling `tryTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/try';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationTry,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Try a transformation before updating it.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param tryTransformationBeforeUpdate - The tryTransformationBeforeUpdate object.\n     * @param tryTransformationBeforeUpdate.transformationID - Unique identifier of a transformation.\n     * @param tryTransformationBeforeUpdate.transformationTry - The transformationTry object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    tryTransformationBeforeUpdate(\n      { transformationID, transformationTry }: TryTransformationBeforeUpdateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationTryResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.');\n      }\n\n      if (!transformationTry) {\n        throw new Error('Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.');\n      }\n\n      if (!transformationTry.sampleRecord) {\n        throw new Error(\n          'Parameter `transformationTry.sampleRecord` is required when calling `tryTransformationBeforeUpdate`.',\n        );\n      }\n\n      const requestPath = '/1/transformations/{transformationID}/try'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationTry,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates an authentication resource.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateAuthentication - The updateAuthentication object.\n     * @param updateAuthentication.authenticationID - Unique identifier of an authentication resource.\n     * @param updateAuthentication.authenticationUpdate - The authenticationUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateAuthentication(\n      { authenticationID, authenticationUpdate }: UpdateAuthenticationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<AuthenticationUpdateResponse> {\n      if (!authenticationID) {\n        throw new Error('Parameter `authenticationID` is required when calling `updateAuthentication`.');\n      }\n\n      if (!authenticationUpdate) {\n        throw new Error('Parameter `authenticationUpdate` is required when calling `updateAuthentication`.');\n      }\n\n      const requestPath = '/1/authentications/{authenticationID}'.replace(\n        '{authenticationID}',\n        encodeURIComponent(authenticationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: authenticationUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates the destination by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateDestination - The updateDestination object.\n     * @param updateDestination.destinationID - Unique identifier of a destination.\n     * @param updateDestination.destinationUpdate - The destinationUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateDestination(\n      { destinationID, destinationUpdate }: UpdateDestinationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DestinationUpdateResponse> {\n      if (!destinationID) {\n        throw new Error('Parameter `destinationID` is required when calling `updateDestination`.');\n      }\n\n      if (!destinationUpdate) {\n        throw new Error('Parameter `destinationUpdate` is required when calling `updateDestination`.');\n      }\n\n      const requestPath = '/1/destinations/{destinationID}'.replace(\n        '{destinationID}',\n        encodeURIComponent(destinationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: destinationUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a source by its ID.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param updateSource - The updateSource object.\n     * @param updateSource.sourceID - Unique identifier of a source.\n     * @param updateSource.sourceUpdate - The sourceUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateSource(\n      { sourceID, sourceUpdate }: UpdateSourceProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SourceUpdateResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `updateSource`.');\n      }\n\n      if (!sourceUpdate) {\n        throw new Error('Parameter `sourceUpdate` is required when calling `updateSource`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a task by its ID.\n     * @param updateTask - The updateTask object.\n     * @param updateTask.taskID - Unique identifier of a task.\n     * @param updateTask.taskUpdate - The taskUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTask({ taskID, taskUpdate }: UpdateTaskProps, requestOptions?: RequestOptions): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `updateTask`.');\n      }\n\n      if (!taskUpdate) {\n        throw new Error('Parameter `taskUpdate` is required when calling `updateTask`.');\n      }\n\n      const requestPath = '/2/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a task by its ID using the v1 endpoint, please use `updateTask` instead.\n     *\n     * @deprecated\n     * @param updateTaskV1 - The updateTaskV1 object.\n     * @param updateTaskV1.taskID - Unique identifier of a task.\n     * @param updateTaskV1.taskUpdate - The taskUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTaskV1(\n      { taskID, taskUpdate }: UpdateTaskV1Props,\n      requestOptions?: RequestOptions,\n    ): Promise<TaskUpdateResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `updateTaskV1`.');\n      }\n\n      if (!taskUpdate) {\n        throw new Error('Parameter `taskUpdate` is required when calling `updateTaskV1`.');\n      }\n\n      const requestPath = '/1/tasks/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PATCH',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: taskUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Updates a transformation by its ID.\n     * @param updateTransformation - The updateTransformation object.\n     * @param updateTransformation.transformationID - Unique identifier of a transformation.\n     * @param updateTransformation.transformationCreate - The transformationCreate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateTransformation(\n      { transformationID, transformationCreate }: UpdateTransformationProps,\n      requestOptions?: RequestOptions,\n    ): Promise<TransformationUpdateResponse> {\n      if (!transformationID) {\n        throw new Error('Parameter `transformationID` is required when calling `updateTransformation`.');\n      }\n\n      if (!transformationCreate) {\n        throw new Error('Parameter `transformationCreate` is required when calling `updateTransformation`.');\n      }\n\n      if (!transformationCreate.name) {\n        throw new Error('Parameter `transformationCreate.name` is required when calling `updateTransformation`.');\n      }\n\n      const requestPath = '/1/transformations/{transformationID}'.replace(\n        '{transformationID}',\n        encodeURIComponent(transformationID),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: transformationCreate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Validates a source payload to ensure it can be created and that the data source can be reached by Algolia.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param sourceCreate -\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    validateSource(\n      sourceCreate: SourceCreate,\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<WatchResponse> {\n      const requestPath = '/1/sources/validate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceCreate ? sourceCreate : {},\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Validates an update of a source payload to ensure it can be created and that the data source can be reached by Algolia.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     *  - deleteIndex\n     *  - editSettings\n     * @param validateSourceBeforeUpdate - The validateSourceBeforeUpdate object.\n     * @param validateSourceBeforeUpdate.sourceID - Unique identifier of a source.\n     * @param validateSourceBeforeUpdate.sourceUpdate - The sourceUpdate object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    validateSourceBeforeUpdate(\n      { sourceID, sourceUpdate }: ValidateSourceBeforeUpdateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<WatchResponse> {\n      if (!sourceID) {\n        throw new Error('Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.');\n      }\n\n      if (!sourceUpdate) {\n        throw new Error('Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.');\n      }\n\n      const requestPath = '/1/sources/{sourceID}/validate'.replace('{sourceID}', encodeURIComponent(sourceID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: sourceUpdate,\n      };\n\n      requestOptions = {\n        timeouts: {\n          connect: 180000,\n          read: 180000,\n          write: 180000,\n          ...requestOptions?.timeouts,\n        },\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport { createXhrRequester } from '@algolia/requester-browser-xhr';\n\nimport {\n  createBrowserLocalStorageCache,\n  createFallbackableCache,\n  createMemoryCache,\n  createNullLogger,\n} from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { apiClientVersion, createIngestionClient } from '../src/ingestionClient';\n\nimport type { Region } from '../src/ingestionClient';\nimport { REGIONS } from '../src/ingestionClient';\n\nexport type { Region, RegionOptions } from '../src/ingestionClient';\n\nexport { apiClientVersion, isOnDemandTrigger, isScheduleTrigger, isSubscriptionTrigger } from '../src/ingestionClient';\n\nexport * from '../model';\n\nexport function ingestionClient(\n  appId: string,\n  apiKey: string,\n  region: Region,\n  options?: ClientOptions | undefined,\n): IngestionClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (!region || (region && (typeof region !== 'string' || !REGIONS.includes(region)))) {\n    throw new Error(`\\`region\\` is required and must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return createIngestionClient({\n    appId,\n    apiKey,\n    region,\n    timeouts: {\n      connect: 25000,\n      read: 25000,\n      write: 25000,\n    },\n    logger: createNullLogger(),\n    requester: createXhrRequester(),\n    algoliaAgents: [{ segment: 'Browser' }],\n    authMode: 'WithinQueryParameters',\n    responsesCache: createMemoryCache(),\n    requestsCache: createMemoryCache({ serializable: false }),\n    hostsCache: createFallbackableCache({\n      caches: [createBrowserLocalStorageCache({ key: `${apiClientVersion}-${appId}` }), createMemoryCache()],\n    }),\n    ...options,\n  });\n}\n\nexport type IngestionClient = ReturnType<typeof createIngestionClient>;\n"], "mappings": "AAIO,SAASA,GAAgC,CAC9C,SAASC,EAAKC,EAAwC,CACpD,OAAO,IAAI,QAASC,GAAY,CAC9B,IAAMC,EAAgB,IAAI,eAC1BA,EAAc,KAAKF,EAAQ,OAAQA,EAAQ,IAAK,EAAI,EAEpD,OAAO,KAAKA,EAAQ,OAAO,EAAE,QAASG,GAAQD,EAAc,iBAAiBC,EAAKH,EAAQ,QAAQG,CAAG,CAAC,CAAC,EAEvG,IAAMC,EAAgB,CAACC,EAAiBC,IAC/B,WAAW,IAAM,CACtBJ,EAAc,MAAM,EAEpBD,EAAQ,CACN,OAAQ,EACR,QAAAK,EACA,WAAY,EACd,CAAC,CACH,EAAGD,CAAO,EAGNE,EAAiBH,EAAcJ,EAAQ,eAAgB,oBAAoB,EAE7EQ,EAEJN,EAAc,mBAAqB,IAAY,CACzCA,EAAc,WAAaA,EAAc,QAAUM,IAAoB,SACzE,aAAaD,CAAc,EAE3BC,EAAkBJ,EAAcJ,EAAQ,gBAAiB,gBAAgB,EAE7E,EAEAE,EAAc,QAAU,IAAY,CAE9BA,EAAc,SAAW,IAC3B,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,cAAgB,yBACvC,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,EAEL,EAEAA,EAAc,OAAS,IAAY,CACjC,aAAaK,CAAc,EAC3B,aAAaC,CAAgB,EAE7BP,EAAQ,CACN,QAASC,EAAc,aACvB,OAAQA,EAAc,OACtB,WAAY,EACd,CAAC,CACH,EAEAA,EAAc,KAAKF,EAAQ,IAAI,CACjC,CAAC,CACH,CAEA,MAAO,CAAE,KAAAD,CAAK,CAChB,CChEO,SAASU,EAA+BC,EAA4C,CACzF,IAAIC,EAEEC,EAAe,qBAAqBF,EAAQ,GAAG,GAErD,SAASG,GAAsB,CAC7B,OAAIF,IAAY,SACdA,EAAUD,EAAQ,cAAgB,OAAO,cAGpCC,CACT,CAEA,SAASG,GAA+C,CACtD,OAAO,KAAK,MAAMD,EAAW,EAAE,QAAQD,CAAY,GAAK,IAAI,CAC9D,CAEA,SAASG,EAAaC,EAAsC,CAC1DH,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAEA,SAASC,GAAiC,CACxC,IAAMC,EAAaR,EAAQ,WAAaA,EAAQ,WAAa,IAAO,KAC9DM,EAAYF,EAA2C,EAEvDK,EAAiD,OAAO,YAC5D,OAAO,QAAQH,CAAS,EAAE,OAAO,CAAC,CAAC,CAAEI,CAAS,IACrCA,EAAU,YAAc,MAChC,CACH,EAIA,GAFAL,EAAaI,CAA8C,EAEvD,CAACD,EACH,OAGF,IAAMG,EAAuC,OAAO,YAClD,OAAO,QAAQF,CAA8C,EAAE,OAAO,CAAC,CAAC,CAAEC,CAAS,IAAM,CACvF,IAAME,EAAmB,IAAI,KAAK,EAAE,QAAQ,EAG5C,MAAO,EAFWF,EAAU,UAAYF,EAAaI,EAGvD,CAAC,CACH,EAEAP,EAAaM,CAAoC,CACnD,CAEA,MAAO,CACL,IACEE,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAM,QAAQ,QAAQ,CAC9B,EACiB,CACjB,OAAO,QAAQ,QAAQ,EACpB,KAAK,KACJR,EAAyB,EAElBH,EAAoD,EAAE,KAAK,UAAUS,CAAG,CAAC,EACjF,EACA,KAAMG,GACE,QAAQ,IAAI,CAACA,EAAQA,EAAM,MAAQF,EAAa,EAAGE,IAAU,MAAS,CAAC,CAC/E,EACA,KAAK,CAAC,CAACA,EAAOC,CAAM,IACZ,QAAQ,IAAI,CAACD,EAAOC,GAAUF,EAAO,KAAKC,CAAK,CAAC,CAAC,CACzD,EACA,KAAK,CAAC,CAACA,CAAK,IAAMA,CAAK,CAC5B,EAEA,IAAYH,EAAmCG,EAAgC,CAC7E,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMV,EAAYF,EAAa,EAE/B,OAAAE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAAI,CAC/B,UAAW,IAAI,KAAK,EAAE,QAAQ,EAC9B,MAAAG,CACF,EAEAb,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,EAErDU,CACT,CAAC,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClC,IAAMP,EAAYF,EAAa,EAE/B,OAAOE,EAAU,KAAK,UAAUO,CAAG,CAAC,EAEpCV,EAAW,EAAE,QAAQD,EAAc,KAAK,UAAUI,CAAS,CAAC,CAC9D,CAAC,CACH,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,EAAE,KAAK,IAAM,CAClCH,EAAW,EAAE,WAAWD,CAAY,CACtC,CAAC,CACH,CACF,CACF,CCvGO,SAASgB,GAAyB,CACvC,MAAO,CACL,IACEC,EACAL,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CAGjB,OAFcD,EAAa,EAEd,KAAMM,GAAW,QAAQ,IAAI,CAACA,EAAQL,EAAO,KAAKK,CAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,CAAM,IAAMA,CAAM,CACrG,EAEA,IAAYD,EAAoCH,EAAgC,CAC9E,OAAO,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOG,EAAmD,CACxD,OAAO,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCzBO,SAASE,EAAwBrB,EAA0C,CAChF,IAAMsB,EAAS,CAAC,GAAGtB,EAAQ,MAAM,EAC3BuB,EAAUD,EAAO,MAAM,EAE7B,OAAIC,IAAY,OACPL,EAAgB,EAGlB,CACL,IACEL,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,OAAOQ,EAAQ,IAAIV,EAAKC,EAAcC,CAAM,EAAE,MAAM,IAC3CM,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKC,EAAcC,CAAM,CACzE,CACH,EAEA,IAAYF,EAAmCG,EAAgC,CAC7E,OAAOO,EAAQ,IAAIV,EAAKG,CAAK,EAAE,MAAM,IAC5BK,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,IAAIT,EAAKG,CAAK,CAC1D,CACH,EAEA,OAAOH,EAAkD,CACvD,OAAOU,EAAQ,OAAOV,CAAG,EAAE,MAAM,IACxBQ,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,OAAOT,CAAG,CACtD,CACH,EAEA,OAAuB,CACrB,OAAOU,EAAQ,MAAM,EAAE,MAAM,IACpBF,EAAwB,CAAE,OAAAC,CAAO,CAAC,EAAE,MAAM,CAClD,CACH,CACF,CACF,CCxCO,SAASE,EAAkBxB,EAA8B,CAAE,aAAc,EAAK,EAAU,CAC7F,IAAIyB,EAA6B,CAAC,EAElC,MAAO,CACL,IACEZ,EACAC,EACAC,EAA8B,CAC5B,KAAM,IAAqB,QAAQ,QAAQ,CAC7C,EACiB,CACjB,IAAMW,EAAc,KAAK,UAAUb,CAAG,EAEtC,GAAIa,KAAeD,EACjB,OAAO,QAAQ,QAAQzB,EAAQ,aAAe,KAAK,MAAMyB,EAAMC,CAAW,CAAC,EAAID,EAAMC,CAAW,CAAC,EAGnG,IAAMC,EAAUb,EAAa,EAE7B,OAAOa,EAAQ,KAAMX,GAAkBD,EAAO,KAAKC,CAAK,CAAC,EAAE,KAAK,IAAMW,CAAO,CAC/E,EAEA,IAAYd,EAAmCG,EAAgC,CAC7E,OAAAS,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAAIb,EAAQ,aAAe,KAAK,UAAUgB,CAAK,EAAIA,EAErE,QAAQ,QAAQA,CAAK,CAC9B,EAEA,OAAOH,EAAsD,CAC3D,cAAOY,EAAM,KAAK,UAAUZ,CAAG,CAAC,EAEzB,QAAQ,QAAQ,CACzB,EAEA,OAAuB,CACrB,OAAAY,EAAQ,CAAC,EAEF,QAAQ,QAAQ,CACzB,CACF,CACF,CExCO,SAASG,EAAmBC,EAA+B,CAChE,IAAMC,EAAe,CACnB,MAAO,2BAA2BD,CAAO,IACzC,IAAIE,EAA4C,CAC9C,IAAMC,EAAoB,KAAKD,EAAQ,OAAO,GAAGA,EAAQ,UAAY,OAAY,KAAKA,EAAQ,OAAO,IAAM,EAAE,GAE7G,OAAID,EAAa,MAAM,QAAQE,CAAiB,IAAM,KACpDF,EAAa,MAAQ,GAAGA,EAAa,KAAK,GAAGE,CAAiB,IAGzDF,CACT,CACF,EAEA,OAAOA,CACT,CCfO,SAASG,EACdC,EACAC,EACAC,EAAqB,gBAIrB,CACA,IAAMC,EAAc,CAClB,oBAAqBF,EACrB,2BAA4BD,CAC9B,EAEA,MAAO,CACL,SAAmB,CACjB,OAAOE,IAAa,gBAAkBC,EAAc,CAAC,CACvD,EAEA,iBAAmC,CACjC,OAAOD,IAAa,wBAA0BC,EAAc,CAAC,CAC/D,CACF,CACF,CCZO,SAASC,EAAiC,CAC/C,KAAAC,EACA,SAAAC,EACA,WAAAC,EACA,MAAAC,EACA,QAAAC,EAAU,IAAc,CAC1B,EAAyD,CACvD,IAAMC,EAASC,GACN,IAAI,QAAmB,CAACC,EAASC,IAAW,CACjDR,EAAKM,CAAgB,EAClB,KAAK,MAAOG,IACPP,GACF,MAAMA,EAAWO,CAAQ,EAGvB,MAAMR,EAASQ,CAAQ,EAClBF,EAAQE,CAAQ,EAGrBN,GAAU,MAAMA,EAAM,SAASM,CAAQ,EAClCD,EAAO,IAAI,MAAM,MAAML,EAAM,QAAQM,CAAQ,CAAC,CAAC,EAGjD,WACL,IAAM,CACJJ,EAAMI,CAAQ,EAAE,KAAKF,CAAO,EAAE,MAAMC,CAAM,CAC5C,EACA,MAAMJ,EAAQ,CAChB,EACD,EACA,MAAOM,GAAQ,CACdF,EAAOE,CAAG,CACZ,CAAC,CACL,CAAC,EAGH,OAAOL,EAAM,CACf,CCxCO,SAASM,EAAgB,CAAE,cAAAC,EAAe,OAAAC,EAAQ,QAAAvB,CAAQ,EAAkC,CACjG,IAAMwB,EAAsBzB,EAAmBC,CAAO,EAAE,IAAI,CAC1D,QAASuB,EACT,QAAAvB,CACF,CAAC,EAED,OAAAsB,EAAc,QAASrB,GAAiBuB,EAAoB,IAAIvB,CAAY,CAAC,EAEtEuB,CACT,CChBO,SAASC,GAA2B,CACzC,MAAO,CACL,MAAMC,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,EACA,KAAKD,EAAkBC,EAAwC,CAC7D,OAAO,QAAQ,QAAQ,CACzB,EACA,MAAMD,EAAkBC,EAAwC,CAC9D,OAAO,QAAQ,QAAQ,CACzB,CACF,CACF,CCVA,IAAMC,EAAmB,EAAI,GAAK,IAE3B,SAASC,EAAmBC,EAAYC,EAAiC,KAAoB,CAClG,IAAMC,EAAa,KAAK,IAAI,EAE5B,SAASC,GAAgB,CACvB,OAAOF,IAAW,MAAQ,KAAK,IAAI,EAAIC,EAAaJ,CACtD,CAEA,SAASM,GAAsB,CAC7B,OAAOH,IAAW,aAAe,KAAK,IAAI,EAAIC,GAAcJ,CAC9D,CAEA,MAAO,CAAE,GAAGE,EAAM,OAAAC,EAAQ,WAAAC,EAAY,KAAAC,EAAM,WAAAC,CAAW,CACzD,CChBO,IAAMC,EAAN,cAA2B,KAAM,CAC7B,KAAe,eAExB,YAAYC,EAAiBC,EAAc,CACzC,MAAMD,CAAO,EAETC,IACF,KAAK,KAAOA,EAEhB,CACF,EAoBO,IAAMC,EAAN,cAAkCC,CAAa,CACpD,WAEA,YAAYC,EAAiBC,EAA0BC,EAAc,CACnE,MAAMF,EAASE,CAAI,EAEnB,KAAK,WAAaD,CACpB,CACF,EAEaE,GAAN,cAAyBL,CAAoB,CAClD,YAAYG,EAA0B,CACpC,MACE,yJACAA,EACA,YACF,CACF,CACF,EAEaG,EAAN,cAAuBN,CAAoB,CAChD,OAEA,YAAYE,EAAiBK,EAAgBJ,EAA0BC,EAAO,WAAY,CACxF,MAAMF,EAASC,EAAYC,CAAI,EAC/B,KAAK,OAASG,CAChB,CACF,EAEaC,GAAN,cAAmCP,CAAa,CACrD,SAEA,YAAYC,EAAiBO,EAAoB,CAC/C,MAAMP,EAAS,sBAAsB,EACrC,KAAK,SAAWO,CAClB,CACF,EAmBaC,GAAN,cAA+BJ,CAAS,CAC7C,MAEA,YAAYJ,EAAiBK,EAAgBI,EAAsBR,EAA0B,CAC3F,MAAMD,EAASK,EAAQJ,EAAY,kBAAkB,EACrD,KAAK,MAAQQ,CACf,CACF,EC7EO,SAASC,GAAaC,EAAYC,EAAcC,EAA0C,CAC/F,IAAMC,EAA0BC,GAAyBF,CAAe,EACpEG,EAAM,GAAGL,EAAK,QAAQ,MAAMA,EAAK,GAAG,GAAGA,EAAK,KAAO,IAAIA,EAAK,IAAI,GAAK,EAAE,IACzEC,EAAK,OAAO,CAAC,IAAM,IAAMA,EAAK,UAAU,CAAC,EAAIA,CAC/C,GAEA,OAAIE,EAAwB,SAC1BE,GAAO,IAAIF,CAAuB,IAG7BE,CACT,CAEO,SAASD,GAAyBE,EAAqC,CAC5E,OAAO,OAAO,KAAKA,CAAU,EAC1B,OAAQC,GAAQD,EAAWC,CAAG,IAAM,MAAS,EAC7C,KAAK,EACL,IACEA,GACC,GAAGA,CAAG,IAAI,mBACR,OAAO,UAAU,SAAS,KAAKD,EAAWC,CAAG,CAAC,IAAM,iBAChDD,EAAWC,CAAG,EAAE,KAAK,GAAG,EACxBD,EAAWC,CAAG,CACpB,EAAE,QAAQ,MAAO,KAAK,CAAC,EAC3B,EACC,KAAK,GAAG,CACb,CAEO,SAASC,GAAcC,EAAkBC,EAAoD,CAClG,GAAID,EAAQ,SAAW,OAAUA,EAAQ,OAAS,QAAaC,EAAe,OAAS,OACrF,OAGF,IAAMC,EAAO,MAAM,QAAQF,EAAQ,IAAI,EAAIA,EAAQ,KAAO,CAAE,GAAGA,EAAQ,KAAM,GAAGC,EAAe,IAAK,EAEpG,OAAO,KAAK,UAAUC,CAAI,CAC5B,CAEO,SAASC,GACdC,EACAC,EACAC,EACS,CACT,IAAMC,EAAmB,CACvB,OAAQ,mBACR,GAAGH,EACH,GAAGC,EACH,GAAGC,CACL,EACME,EAA6B,CAAC,EAEpC,cAAO,KAAKD,CAAO,EAAE,QAASE,GAAW,CACvC,IAAMC,EAAQH,EAAQE,CAAM,EAC5BD,EAAkBC,EAAO,YAAY,CAAC,EAAIC,CAC5C,CAAC,EAEMF,CACT,CAEO,SAASG,GAA4BC,EAA6B,CACvE,GAAI,CACF,OAAO,KAAK,MAAMA,EAAS,OAAO,CACpC,OAASC,EAAG,CACV,MAAM,IAAIC,GAAsBD,EAAY,QAASD,CAAQ,CAC/D,CACF,CAEO,SAASG,GAAmB,CAAE,QAAAC,EAAS,OAAAC,CAAO,EAAaC,EAAiC,CACjG,GAAI,CACF,IAAMC,EAAS,KAAK,MAAMH,CAAO,EACjC,MAAI,UAAWG,EACN,IAAIC,GAAiBD,EAAO,QAASF,EAAQE,EAAO,MAAOD,CAAU,EAEvE,IAAIG,EAASF,EAAO,QAASF,EAAQC,CAAU,CACxD,MAAQ,CAER,CACA,OAAO,IAAIG,EAASL,EAASC,EAAQC,CAAU,CACjD,CC7FO,SAASI,GAAe,CAAE,WAAAC,EAAY,OAAAN,CAAO,EAAuC,CACzF,MAAO,CAACM,GAAc,CAAC,CAACN,IAAW,CACrC,CAEO,SAASO,GAAY,CAAE,WAAAD,EAAY,OAAAN,CAAO,EAAuC,CACtF,OAAOM,GAAcD,GAAe,CAAE,WAAAC,EAAY,OAAAN,CAAO,CAAC,GAAM,CAAC,EAAEA,EAAS,OAAS,GAAK,CAAC,EAAEA,EAAS,OAAS,CACjH,CAEO,SAASQ,GAAU,CAAE,OAAAR,CAAO,EAAsC,CACvE,MAAO,CAAC,EAAEA,EAAS,OAAS,CAC9B,CCVO,SAASS,GAA6BC,EAAwC,CACnF,OAAOA,EAAW,IAAKT,GAAeU,EAA6BV,CAAU,CAAC,CAChF,CAEO,SAASU,EAA6BV,EAAoC,CAC/E,IAAMW,EAA2BX,EAAW,QAAQ,QAAQ,mBAAmB,EAC3E,CAAE,oBAAqB,OAAQ,EAC/B,CAAC,EAEL,MAAO,CACL,GAAGA,EACH,QAAS,CACP,GAAGA,EAAW,QACd,QAAS,CACP,GAAGA,EAAW,QAAQ,QACtB,GAAGW,CACL,CACF,CACF,CACF,CCCO,SAASC,EAAkB,CAChC,MAAAC,EACA,WAAAC,EACA,YAAA5B,EACA,OAAA6B,EACA,oBAAAC,EACA,aAAAC,EACA,SAAAC,EACA,UAAAC,EACA,cAAAC,EACA,eAAAC,CACF,EAAoC,CAClC,eAAeC,EAAuBC,EAAoD,CACxF,IAAMC,EAAgB,MAAM,QAAQ,IAClCD,EAAgB,IAAKE,GACZX,EAAW,IAAIW,EAAgB,IAC7B,QAAQ,QAAQC,EAAmBD,CAAc,CAAC,CAC1D,CACF,CACH,EACME,EAAUH,EAAc,OAAQnD,GAASA,EAAK,KAAK,CAAC,EACpDuD,EAAgBJ,EAAc,OAAQnD,GAASA,EAAK,WAAW,CAAC,EAGhEwD,EAAiB,CAAC,GAAGF,EAAS,GAAGC,CAAa,EAGpD,MAAO,CACL,MAH+BC,EAAe,OAAS,EAAIA,EAAiBN,EAI5E,WAAWO,EAAuBC,EAA6B,CAe7D,OAFEH,EAAc,SAAW,GAAKE,IAAkB,EAAI,EAAIF,EAAc,OAAS,EAAIE,GAE1DC,CAC7B,CACF,CACF,CAEA,eAAeC,EACblD,EACAC,EACAkD,EAAS,GACW,CACpB,IAAMxB,EAA2B,CAAC,EAK5BzB,EAAOH,GAAcC,EAASC,CAAc,EAC5CM,EAAUJ,GAAiBC,EAAaJ,EAAQ,QAASC,EAAe,OAAO,EAG/EmD,EACJpD,EAAQ,SAAW,MACf,CACE,GAAGA,EAAQ,KACX,GAAGC,EAAe,IACpB,EACA,CAAC,EAEDR,EAAmC,CACvC,GAAGyC,EACH,GAAGlC,EAAQ,gBACX,GAAGoD,CACL,EAMA,GAJIjB,EAAa,QACf1C,EAAgB,iBAAiB,EAAI0C,EAAa,OAGhDlC,GAAkBA,EAAe,gBACnC,QAAWH,KAAO,OAAO,KAAKG,EAAe,eAAe,EAKxD,CAACA,EAAe,gBAAgBH,CAAG,GACnC,OAAO,UAAU,SAAS,KAAKG,EAAe,gBAAgBH,CAAG,CAAC,IAAM,kBAExEL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAEzDL,EAAgBK,CAAG,EAAIG,EAAe,gBAAgBH,CAAG,EAAE,SAAS,EAK1E,IAAIkD,EAAgB,EAEdK,EAAQ,MACZC,EACAC,IACuB,CAIvB,IAAMhE,EAAO+D,EAAe,IAAI,EAChC,GAAI/D,IAAS,OACX,MAAM,IAAIiE,GAAW9B,GAA6BC,CAAU,CAAC,EAG/D,IAAM8B,EAAU,CAAE,GAAGrB,EAAU,GAAGnC,EAAe,QAAS,EAEpDyD,EAAsB,CAC1B,KAAAxD,EACA,QAAAK,EACA,OAAQP,EAAQ,OAChB,IAAKV,GAAaC,EAAMS,EAAQ,KAAMP,CAAe,EACrD,eAAgB8D,EAAWP,EAAeS,EAAQ,OAAO,EACzD,gBAAiBF,EAAWP,EAAeG,EAASM,EAAQ,KAAOA,EAAQ,KAAK,CAClF,EAOME,EAAoB/C,GAAmC,CAC3D,IAAMM,EAAyB,CAC7B,QAASwC,EACT,SAAA9C,EACA,KAAArB,EACA,UAAW+D,EAAe,MAC5B,EAEA,OAAA3B,EAAW,KAAKT,CAAU,EAEnBA,CACT,EAEMN,EAAW,MAAMyB,EAAU,KAAKqB,CAAO,EAE7C,GAAIlC,GAAYZ,CAAQ,EAAG,CACzB,IAAMM,EAAayC,EAAiB/C,CAAQ,EAG5C,OAAIA,EAAS,YACXoC,IAOFf,EAAO,KAAK,oBAAqBL,EAA6BV,CAAU,CAAC,EAOzE,MAAMc,EAAW,IAAIzC,EAAMqD,EAAmBrD,EAAMqB,EAAS,WAAa,YAAc,MAAM,CAAC,EAExFyC,EAAMC,EAAgBC,CAAU,CACzC,CAEA,GAAI9B,GAAUb,CAAQ,EACpB,OAAOD,GAAmBC,CAAQ,EAGpC,MAAA+C,EAAiB/C,CAAQ,EACnBG,GAAmBH,EAAUe,CAAU,CAC/C,EAUMc,EAAkBV,EAAM,OAC3BxC,GAASA,EAAK,SAAW,cAAgB4D,EAAS5D,EAAK,SAAW,OAASA,EAAK,SAAW,QAC9F,EACMqE,EAAU,MAAMpB,EAAuBC,CAAe,EAE5D,OAAOY,EAAM,CAAC,GAAGO,EAAQ,KAAK,EAAE,QAAQ,EAAGA,EAAQ,UAAU,CAC/D,CAEA,SAASC,EAAyB7D,EAAkBC,EAAiC,CAAC,EAAuB,CAK3G,IAAMkD,EAASnD,EAAQ,oBAAsBA,EAAQ,SAAW,MAChE,GAAI,CAACmD,EAKH,OAAOD,EAA4BlD,EAASC,EAAgBkD,CAAM,EAGpE,IAAMW,EAAyB,IAMtBZ,EAA4BlD,EAASC,CAAc,EAc5D,IANkBA,EAAe,WAAaD,EAAQ,aAMpC,GAChB,OAAO8D,EAAuB,EAQhC,IAAMhE,EAAM,CACV,QAAAE,EACA,eAAAC,EACA,YAAa,CACX,gBAAiBiC,EACjB,QAAS9B,CACX,CACF,EAMA,OAAOmC,EAAe,IACpBzC,EACA,IAKSwC,EAAc,IAAIxC,EAAK,IAM5BwC,EACG,IAAIxC,EAAKgE,EAAuB,CAAC,EACjC,KACElD,GAAa,QAAQ,IAAI,CAAC0B,EAAc,OAAOxC,CAAG,EAAGc,CAAQ,CAAC,EAC9DmD,GAAQ,QAAQ,IAAI,CAACzB,EAAc,OAAOxC,CAAG,EAAG,QAAQ,OAAOiE,CAAG,CAAC,CAAC,CACvE,EACC,KAAK,CAAC,CAACC,EAAGpD,CAAQ,IAAMA,CAAQ,CACrC,EAEF,CAME,KAAOA,GAAa2B,EAAe,IAAIzC,EAAKc,CAAQ,CACtD,CACF,CACF,CAEA,MAAO,CACL,WAAAoB,EACA,UAAAK,EACA,SAAAD,EACA,OAAAH,EACA,aAAAE,EACA,YAAA/B,EACA,oBAAA8B,EACA,MAAAH,EACA,QAAS8B,EACT,cAAAvB,EACA,eAAAC,CACF,CACF,CE9LO,IAAM0B,EAAmB,SAEnBC,EAAU,CAAC,KAAM,IAAI,EAIlC,SAASC,GAAgBC,EAAwB,CAG/C,MAAO,CAAC,CAAE,IAFE,4BAA4B,QAAQ,WAAYA,CAAM,EAEnD,OAAQ,YAAa,SAAU,OAAQ,CAAC,CACzD,CAQO,SAASC,GAAkBC,EAAkE,CAClG,OAAOA,EAAQ,OAAS,UAC1B,CAQO,SAASC,GAAkBD,EAAkE,CAClG,OAAOA,EAAQ,OAAS,UAC1B,CAQO,SAASE,GAAsBF,EAAsE,CAC1G,OAAOA,EAAQ,OAAS,cAC1B,CAEO,SAASG,EAAsB,CACpC,MAAOC,EACP,OAAQC,EACR,SAAAC,EACA,cAAAC,EACA,OAAQC,EACR,GAAGC,CACL,EAAwC,CACtC,IAAMC,EAAOC,EAAWP,EAAaC,EAAcC,CAAQ,EACrDM,EAAcC,EAAkB,CACpC,MAAOhB,GAAgBW,CAAY,EACnC,GAAGC,EACH,aAAcK,EAAgB,CAC5B,cAAAP,EACA,OAAQ,YACR,QAASZ,CACX,CAAC,EACD,YAAa,CACX,eAAgB,aAChB,GAAGe,EAAK,QAAQ,EAChB,GAAGD,EAAQ,WACb,EACA,oBAAqB,CACnB,GAAGC,EAAK,gBAAgB,EACxB,GAAGD,EAAQ,mBACb,CACF,CAAC,EAED,MAAO,CACL,YAAAG,EAKA,MAAOR,EAKP,OAAQC,EAKR,YAA4B,CAC1B,OAAO,QAAQ,IAAI,CAACO,EAAY,cAAc,MAAM,EAAGA,EAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,IAAG,EAAY,CAClH,EAKA,IAAI,KAAc,CAChB,OAAOA,EAAY,aAAa,KAClC,EAQA,gBAAgBG,EAAiBC,EAAoC,CACnEJ,EAAY,aAAa,IAAI,CAAE,QAAAG,EAAS,QAAAC,CAAQ,CAAC,CACnD,EAQA,gBAAgB,CAAE,OAAAC,CAAO,EAA6B,CAChD,CAACX,GAAYA,IAAa,gBAC5BM,EAAY,YAAY,mBAAmB,EAAIK,EAE/CL,EAAY,oBAAoB,mBAAmB,EAAIK,CAE3D,EAeA,MAAM,YACJ,CACE,UAAAC,EACA,QAAAC,EACA,OAAAC,EAAS,YACT,aAAAC,EACA,UAAAC,EAAY,IACZ,mBAAAC,CACF,EACAC,EAC+B,CAC/B,IAAIC,EAAkC,CAAC,EACjCC,EAAkC,CAAC,EAEnCC,EAAgBR,EAAQ,QAAQ,EACtC,OAAW,CAACS,EAAGC,CAAG,IAAKF,EACrBF,EAAQ,KAAKI,CAAsB,GAC/BJ,EAAQ,SAAWH,GAAaM,IAAMT,EAAQ,OAAS,KACzDO,EAAU,KACR,MAAM,KAAK,KAAK,CAAE,UAAAR,EAAW,gBAAiB,CAAE,OAAAE,EAAQ,QAAAK,CAAQ,EAAG,mBAAAF,CAAmB,EAAGC,CAAc,CACzG,EACAC,EAAU,CAAC,GAIf,IAAIK,EAAa,EAEjB,GAAIT,EACF,QAAWU,KAAQL,EAAW,CAC5B,GAAI,CAACK,EAAK,QACR,MAAM,IAAI,MAAM,oFAAoF,EAGtG,MAAMC,EAAsB,CAC1B,KAAM,SAAY,CAChB,GAAID,EAAK,UAAY,QAAa,CAACA,EAAK,QACtC,MAAM,IAAI,MAAM,oFAAoF,EAGtG,OAAO,KAAK,SAAS,CAAE,MAAOA,EAAK,MAAO,QAASA,EAAK,OAAQ,CAAC,EAAE,MAAOE,GAAoB,CAC5F,GAAIA,EAAM,SAAW,IAIrB,MAAMA,CACR,CAAC,CACH,EACA,SAAWC,GAAaA,IAAa,OACrC,WAAY,IAAOJ,GAAc,EACjC,MAAO,CACL,SAAU,IAAMA,GAAc,GAC9B,QAAS,IAAM,4CAA4CA,CAAU,MACvE,EACA,QAAS,IAAc,KAAK,IAAIA,EAAa,IAAK,GAAI,CACxD,CAAC,CACH,CAGF,OAAOJ,CACT,EAYA,qBACES,EACAX,EACuC,CACvC,GAAI,CAACW,EACH,MAAM,IAAI,MAAM,mFAAmF,EAGrG,GAAI,CAACA,EAAqB,KACxB,MAAM,IAAI,MAAM,wFAAwF,EAE1G,GAAI,CAACA,EAAqB,KACxB,MAAM,IAAI,MAAM,wFAAwF,EAE1G,GAAI,CAACA,EAAqB,MACxB,MAAM,IAAI,MAAM,yFAAyF,EAO3G,IAAMC,EAAmB,CACvB,OAAQ,OACR,KANkB,qBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMD,CACR,EAEA,OAAOvB,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,kBACEa,EACAb,EACoC,CACpC,GAAI,CAACa,EACH,MAAM,IAAI,MAAM,6EAA6E,EAG/F,GAAI,CAACA,EAAkB,KACrB,MAAM,IAAI,MAAM,kFAAkF,EAEpG,GAAI,CAACA,EAAkB,KACrB,MAAM,IAAI,MAAM,kFAAkF,EAEpG,GAAI,CAACA,EAAkB,MACrB,MAAM,IAAI,MAAM,mFAAmF,EAOrG,IAAMD,EAAmB,CACvB,OAAQ,OACR,KANkB,kBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMC,CACR,EAEA,OAAOzB,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,aAAac,EAA4Bd,EAAgE,CACvG,GAAI,CAACc,EACH,MAAM,IAAI,MAAM,mEAAmE,EAGrF,GAAI,CAACA,EAAa,KAChB,MAAM,IAAI,MAAM,wEAAwE,EAE1F,GAAI,CAACA,EAAa,KAChB,MAAM,IAAI,MAAM,wEAAwE,EAO1F,IAAMF,EAAmB,CACvB,OAAQ,OACR,KANkB,aAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAME,CACR,EAEA,OAAO1B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAOA,WAAWe,EAAwBf,EAA8D,CAC/F,GAAI,CAACe,EACH,MAAM,IAAI,MAAM,+DAA+D,EAGjF,GAAI,CAACA,EAAW,SACd,MAAM,IAAI,MAAM,wEAAwE,EAE1F,GAAI,CAACA,EAAW,cACd,MAAM,IAAI,MAAM,6EAA6E,EAE/F,GAAI,CAACA,EAAW,OACd,MAAM,IAAI,MAAM,sEAAsE,EAOxF,IAAMH,EAAmB,CACvB,OAAQ,OACR,KANkB,WAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMG,CACR,EAEA,OAAO3B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EASA,aAAae,EAA0Bf,EAA8D,CACnG,GAAI,CAACe,EACH,MAAM,IAAI,MAAM,iEAAiE,EAGnF,GAAI,CAACA,EAAW,SACd,MAAM,IAAI,MAAM,0EAA0E,EAE5F,GAAI,CAACA,EAAW,cACd,MAAM,IAAI,MAAM,+EAA+E,EAEjG,GAAI,CAACA,EAAW,QACd,MAAM,IAAI,MAAM,yEAAyE,EAE3F,GAAI,CAACA,EAAW,OACd,MAAM,IAAI,MAAM,wEAAwE,EAO1F,IAAMH,EAAmB,CACvB,OAAQ,OACR,KANkB,WAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMG,CACR,EAEA,OAAO3B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAOA,qBACEgB,EACAhB,EACuC,CACvC,GAAI,CAACgB,EACH,MAAM,IAAI,MAAM,mFAAmF,EAGrG,GAAI,CAACA,EAAqB,KACxB,MAAM,IAAI,MAAM,wFAAwF,EAO1G,IAAMJ,EAAmB,CACvB,OAAQ,OACR,KANkB,qBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMI,CACR,EAEA,OAAO5B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EASA,aACE,CAAE,KAAAiB,EAAM,WAAAC,CAAW,EACnBlB,EACkC,CAClC,GAAI,CAACiB,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAML,EAAmB,CACvB,OAAQ,SACR,KANkB,UAAU,QAAQ,SAAUK,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAO9B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EASA,UAAU,CAAE,KAAAiB,EAAM,WAAAC,CAAW,EAAmBlB,EAAmE,CACjH,GAAI,CAACiB,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAML,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUK,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,CAQ1B,EAEA,OAAO9B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAUA,WACE,CAAE,KAAAiB,EAAM,WAAAC,EAAY,KAAAC,CAAK,EACzBnB,EACkC,CAClC,GAAI,CAACiB,EACH,MAAM,IAAI,MAAM,yDAAyD,EAO3E,IAAML,EAAmB,CACvB,OAAQ,OACR,KANkB,UAAU,QAAQ,SAAUK,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMC,GAAc,CAAC,CACvB,EAEA,OAAO/B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAUA,UACE,CAAE,KAAAiB,EAAM,WAAAC,EAAY,KAAAC,CAAK,EACzBnB,EACkC,CAClC,GAAI,CAACiB,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAML,EAAmB,CACvB,OAAQ,MACR,KANkB,UAAU,QAAQ,SAAUK,CAAI,EAOlD,gBALuCC,GAA0B,CAAC,EAMlE,QAPuB,CAAC,EAQxB,KAAMC,GAAc,CAAC,CACvB,EAEA,OAAO/B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,qBACE,CAAE,iBAAAoB,CAAiB,EACnBpB,EACyB,CACzB,GAAI,CAACoB,EACH,MAAM,IAAI,MAAM,+EAA+E,EAUjG,IAAMR,EAAmB,CACvB,OAAQ,SACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBQ,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOhC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,kBACE,CAAE,cAAAqB,CAAc,EAChBrB,EACyB,CACzB,GAAI,CAACqB,EACH,MAAM,IAAI,MAAM,yEAAyE,EAU3F,IAAMT,EAAmB,CACvB,OAAQ,SACR,KATkB,kCAAkC,QACpD,kBACA,mBAAmBS,CAAa,CAClC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOjC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,aAAa,CAAE,SAAAsB,CAAS,EAAsBtB,EAA0D,CACtG,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAMV,EAAmB,CACvB,OAAQ,SACR,KANkB,wBAAwB,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAO5F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOlC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAQA,WAAW,CAAE,OAAAuB,CAAO,EAAoBvB,EAA0D,CAChG,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAMX,EAAmB,CACvB,OAAQ,SACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAUA,aAAa,CAAE,OAAAuB,CAAO,EAAsBvB,EAA0D,CACpG,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,6DAA6D,EAO/E,IAAMX,EAAmB,CACvB,OAAQ,SACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAQA,qBACE,CAAE,iBAAAwB,CAAiB,EACnBxB,EACyB,CACzB,GAAI,CAACwB,EACH,MAAM,IAAI,MAAM,+EAA+E,EAUjG,IAAMZ,EAAmB,CACvB,OAAQ,SACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBY,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOpC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,YAAY,CAAE,OAAAuB,CAAO,EAAqBvB,EAA8D,CACtG,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,4DAA4D,EAO9E,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,4BAA4B,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAO5F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAeA,cAAc,CAAE,OAAAuB,CAAO,EAAuBvB,EAA8D,CAC1G,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,8DAA8D,EAOhF,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,4BAA4B,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAO5F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,WAAW,CAAE,OAAAuB,CAAO,EAAoBvB,EAA8D,CACpG,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,2DAA2D,EAO7E,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,2BAA2B,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAO3F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAeA,aAAa,CAAE,OAAAuB,CAAO,EAAsBvB,EAA8D,CACxG,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,6DAA6D,EAO/E,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,2BAA2B,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAO3F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,kBACE,CAAE,iBAAAoB,CAAiB,EACnBpB,EACyB,CACzB,GAAI,CAACoB,EACH,MAAM,IAAI,MAAM,4EAA4E,EAU9F,IAAMR,EAAmB,CACvB,OAAQ,MACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBQ,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOhC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,eAAe,CAAE,cAAAqB,CAAc,EAAwBrB,EAAuD,CAC5G,GAAI,CAACqB,EACH,MAAM,IAAI,MAAM,sEAAsE,EAUxF,IAAMT,EAAmB,CACvB,OAAQ,MACR,KATkB,kCAAkC,QACpD,kBACA,mBAAmBS,CAAa,CAClC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOjC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,SAAS,CAAE,MAAAyB,EAAO,QAAAC,CAAQ,EAAkB1B,EAAiD,CAC3F,GAAI,CAACyB,EACH,MAAM,IAAI,MAAM,wDAAwD,EAG1E,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,0DAA0D,EAS5E,IAAMd,EAAmB,CACvB,OAAQ,MACR,KARkB,mCACjB,QAAQ,UAAW,mBAAmBa,CAAK,CAAC,EAC5C,QAAQ,YAAa,mBAAmBC,CAAO,CAAC,EAOjD,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOtC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,OAAO,CAAE,MAAAyB,CAAM,EAAgBzB,EAA+C,CAC5E,GAAI,CAACyB,EACH,MAAM,IAAI,MAAM,sDAAsD,EAOxE,IAAMb,EAAmB,CACvB,OAAQ,MACR,KANkB,kBAAkB,QAAQ,UAAW,mBAAmBa,CAAK,CAAC,EAOhF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOrC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,UAAU,CAAE,SAAAsB,CAAS,EAAmBtB,EAAkD,CACxF,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,4DAA4D,EAO9E,IAAMV,EAAmB,CACvB,OAAQ,MACR,KANkB,wBAAwB,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAO5F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOlC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,QAAQ,CAAE,OAAAuB,CAAO,EAAiBvB,EAAgD,CAChF,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAeA,UAAU,CAAE,OAAAuB,CAAO,EAAmBvB,EAAkD,CACtF,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,0DAA0D,EAO5E,IAAMX,EAAmB,CACvB,OAAQ,MACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,kBACE,CAAE,iBAAAwB,CAAiB,EACnBxB,EACyB,CACzB,GAAI,CAACwB,EACH,MAAM,IAAI,MAAM,4EAA4E,EAU9F,IAAMZ,EAAmB,CACvB,OAAQ,MACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBY,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOpC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAkBA,oBACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,KAAAC,EAAM,SAAAC,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAA8B,CAAC,EACjFhC,EAA6C,OACP,CACtC,IAAMiC,EAAc,qBACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCC,IAAS,SACXM,EAAgB,KAAUN,EAAK,SAAS,GAGtCC,IAAa,SACfK,EAAgB,SAAcL,EAAS,SAAS,GAG9CC,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAmBA,iBACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,KAAAC,EAAM,iBAAAT,EAAkB,iBAAAI,EAAkB,KAAAO,EAAM,MAAAC,CAAM,EAA2B,CAAC,EACxGhC,EAA6C,OACV,CACnC,IAAMiC,EAAc,kBACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCC,IAAS,SACXM,EAAgB,KAAUN,EAAK,SAAS,GAGtCT,IAAqB,SACvBe,EAAgB,iBAAsBf,EAAiB,SAAS,GAG9DI,IAAqB,SACvBW,EAAgB,iBAAsBX,EAAiB,SAAS,GAG9DO,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAqBA,WACE,CAAE,MAAAyB,EAAO,aAAAE,EAAc,KAAAC,EAAM,OAAAQ,EAAQ,KAAAP,EAAM,KAAAE,EAAM,MAAAC,EAAO,UAAAK,EAAW,QAAAC,CAAQ,EAC3EtC,EAC6B,CAC7B,GAAI,CAACyB,EACH,MAAM,IAAI,MAAM,0DAA0D,EAG5E,IAAMQ,EAAc,yBAAyB,QAAQ,UAAW,mBAAmBR,CAAK,CAAC,EACnFS,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCQ,IAAW,SACbD,EAAgB,OAAYC,EAAO,SAAS,GAG1CP,IAAS,SACXM,EAAgB,KAAUN,EAAK,SAAS,GAGtCE,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAGxCK,IAAc,SAChBF,EAAgB,UAAeE,EAAU,SAAS,GAGhDC,IAAY,SACdH,EAAgB,QAAaG,EAAQ,SAAS,GAGhD,IAAM1B,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAqBA,SACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,OAAAQ,EAAQ,KAAAP,EAAM,OAAAN,EAAQ,KAAAQ,EAAM,MAAAC,EAAO,UAAAK,EAAW,QAAAC,CAAQ,EAAmB,CAAC,EAChGtC,EAA6C,OACnB,CAC1B,IAAMiC,EAAc,UACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCQ,IAAW,SACbD,EAAgB,OAAYC,EAAO,SAAS,GAG1CP,IAAS,SACXM,EAAgB,KAAUN,EAAK,SAAS,GAGtCN,IAAW,SACbY,EAAgB,OAAYZ,EAAO,SAAS,GAG1CQ,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAGxCK,IAAc,SAChBF,EAAgB,UAAeE,EAAU,SAAS,GAGhDC,IAAY,SACdH,EAAgB,QAAaG,EAAQ,SAAS,GAGhD,IAAM1B,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAkBA,YACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,KAAAC,EAAM,iBAAAT,EAAkB,KAAAW,EAAM,MAAAC,CAAM,EAAsB,CAAC,EACjFhC,EAA6C,OACf,CAC9B,IAAMiC,EAAc,aACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCC,IAAS,SACXM,EAAgB,KAAUN,EAAK,SAAS,GAGtCT,IAAqB,SACvBe,EAAgB,iBAAsBf,EAAiB,SAAS,GAG9DW,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAuBA,UACE,CACE,aAAA2B,EACA,KAAAC,EACA,OAAAhC,EACA,QAAA2C,EACA,SAAAjB,EACA,WAAAkB,EACA,cAAAnB,EACA,YAAAoB,EACA,uBAAAC,EACA,KAAAX,EACA,MAAAC,CACF,EAAoB,CAAC,EACrBhC,EAA6C,OACjB,CAC5B,IAAMiC,EAAc,WACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtChC,IAAW,SACbuC,EAAgB,OAAYvC,EAAO,SAAS,GAG1C2C,IAAY,SACdJ,EAAgB,QAAaI,EAAQ,SAAS,GAG5CjB,IAAa,SACfa,EAAgB,SAAcb,EAAS,SAAS,GAG9CkB,IAAe,SACjBL,EAAgB,WAAgBK,EAAW,SAAS,GAGlDnB,IAAkB,SACpBc,EAAgB,cAAmBd,EAAc,SAAS,GAGxDoB,IAAgB,SAClBN,EAAgB,YAAiBM,EAAY,SAAS,GAGpDC,IAA2B,SAC7BP,EAAgB,uBAA4BO,EAAuB,SAAS,GAG1EX,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAuBA,YACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,OAAAhC,EAAQ,QAAA2C,EAAS,SAAAjB,EAAU,cAAAD,EAAe,YAAAoB,EAAa,KAAAV,EAAM,MAAAC,CAAM,EAAsB,CAAC,EAChHhC,EAA6C,OACf,CAC9B,IAAMiC,EAAc,WACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtChC,IAAW,SACbuC,EAAgB,OAAYvC,EAAO,SAAS,GAG1C2C,IAAY,SACdJ,EAAgB,QAAaI,EAAQ,SAAS,GAG5CjB,IAAa,SACfa,EAAgB,SAAcb,EAAS,SAAS,GAG9CD,IAAkB,SACpBc,EAAgB,cAAmBd,EAAc,SAAS,GAGxDoB,IAAgB,SAClBN,EAAgB,YAAiBM,EAAY,SAAS,GAGpDV,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAgBA,oBACE,CAAE,aAAA2B,EAAc,KAAAC,EAAM,KAAAG,EAAM,MAAAC,CAAM,EAA8B,CAAC,EACjEhC,EAA6C,OACP,CACtC,IAAMiC,EAAc,qBACdC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCR,IAAiB,SACnBQ,EAAgB,aAAkBR,EAAa,SAAS,GAGtDC,IAAS,SACXO,EAAgB,KAAUP,EAAK,SAAS,GAGtCG,IAAS,SACXI,EAAgB,KAAUJ,EAAK,SAAS,GAGtCC,IAAU,SACZG,EAAgB,MAAWH,EAAM,SAAS,GAG5C,IAAMpB,EAAmB,CACvB,OAAQ,MACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,CACF,EAEA,OAAO9C,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAgBA,KACE,CAAE,UAAAN,EAAW,gBAAAiD,EAAiB,MAAAC,EAAO,mBAAA7C,CAAmB,EACxDC,EACwB,CACxB,GAAI,CAACN,EACH,MAAM,IAAI,MAAM,wDAAwD,EAG1E,GAAI,CAACiD,EACH,MAAM,IAAI,MAAM,8DAA8D,EAGhF,GAAI,CAACA,EAAgB,OACnB,MAAM,IAAI,MAAM,qEAAqE,EAEvF,GAAI,CAACA,EAAgB,QACnB,MAAM,IAAI,MAAM,sEAAsE,EAGxF,IAAMV,EAAc,sBAAsB,QAAQ,cAAe,mBAAmBvC,CAAS,CAAC,EACxFwC,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCS,IAAU,SACZT,EAAgB,MAAWS,EAAM,SAAS,GAGxC7C,IAAuB,SACzBoC,EAAgB,mBAAwBpC,EAAmB,SAAS,GAGtE,IAAMa,EAAmB,CACvB,OAAQ,OACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMS,CACR,EAEA,OAAA3C,EAAiB,CACf,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,KACP,GAAGA,GAAgB,QACrB,CACF,EAEOZ,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAeA,SACE,CAAE,OAAAuB,EAAQ,gBAAAoB,EAAiB,MAAAC,CAAM,EACjC5C,EACwB,CACxB,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,yDAAyD,EAG3E,GAAI,CAACoB,EACH,MAAM,IAAI,MAAM,kEAAkE,EAGpF,GAAI,CAACA,EAAgB,OACnB,MAAM,IAAI,MAAM,yEAAyE,EAE3F,GAAI,CAACA,EAAgB,QACnB,MAAM,IAAI,MAAM,0EAA0E,EAG5F,IAAMV,EAAc,yBAAyB,QAAQ,WAAY,mBAAmBV,CAAM,CAAC,EACrFW,EAAmB,CAAC,EACpBC,EAAmC,CAAC,EAEtCS,IAAU,SACZT,EAAgB,MAAWS,EAAM,SAAS,GAG5C,IAAMhC,EAAmB,CACvB,OAAQ,OACR,KAAMqB,EACN,gBAAAE,EACA,QAAAD,EACA,KAAMS,CACR,EAEA,OAAA3C,EAAiB,CACf,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,KACP,GAAGA,GAAgB,QACrB,CACF,EAEOZ,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,UACE,CAAE,SAAAsB,EAAU,iBAAAuB,CAAiB,EAC7B7C,EAC4B,CAC5B,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,4DAA4D,EAO9E,IAAMV,EAAmB,CACvB,OAAQ,OACR,KANkB,4BAA4B,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAOhG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMuB,GAAsC,CAAC,CAC/C,EAEA,OAAOzD,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,QAAQ,CAAE,OAAAuB,CAAO,EAAiBvB,EAAuD,CACvF,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,wDAAwD,EAO1E,IAAMX,EAAmB,CACvB,OAAQ,OACR,KANkB,wBAAwB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOxF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAeA,UAAU,CAAE,OAAAuB,CAAO,EAAmBvB,EAAuD,CAC3F,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,0DAA0D,EAO5E,IAAMX,EAAmB,CACvB,OAAQ,OACR,KANkB,wBAAwB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOxF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAOnC,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,sBACE8C,EACA9C,EACgC,CAChC,GAAI,CAAC8C,EACH,MAAM,IAAI,MAAM,oFAAoF,EAGtG,GAAI,CAACA,EAAqB,kBACxB,MAAM,IAAI,MACR,sGACF,EAOF,IAAMlC,EAAmB,CACvB,OAAQ,OACR,KANkB,4BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMkC,CACR,EAEA,OAAO1D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,mBACE+C,EACA/C,EAC6B,CAC7B,GAAI,CAAC+C,EACH,MAAM,IAAI,MAAM,8EAA8E,EAGhG,GAAI,CAACA,EAAkB,eACrB,MAAM,IAAI,MAAM,6FAA6F,EAO/G,IAAMnC,EAAmB,CACvB,OAAQ,OACR,KANkB,yBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMmC,CACR,EAEA,OAAO3D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,cAAcgD,EAA4BhD,EAAyD,CACjG,GAAI,CAACgD,EACH,MAAM,IAAI,MAAM,oEAAoE,EAGtF,GAAI,CAACA,EAAa,UAChB,MAAM,IAAI,MAAM,8EAA8E,EAOhG,IAAMpC,EAAmB,CACvB,OAAQ,OACR,KANkB,oBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMoC,CACR,EAEA,OAAO5D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,YAAYiD,EAAwBjD,EAAuD,CACzF,GAAI,CAACiD,EACH,MAAM,IAAI,MAAM,gEAAgE,EAGlF,GAAI,CAACA,EAAW,QACd,MAAM,IAAI,MAAM,wEAAwE,EAO1F,IAAMrC,EAAmB,CACvB,OAAQ,OACR,KANkB,kBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMqC,CACR,EAEA,OAAO7D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,cAAciD,EAAwBjD,EAAyD,CAC7F,GAAI,CAACiD,EACH,MAAM,IAAI,MAAM,kEAAkE,EAGpF,GAAI,CAACA,EAAW,QACd,MAAM,IAAI,MAAM,0EAA0E,EAO5F,IAAMrC,EAAmB,CACvB,OAAQ,OACR,KANkB,kBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMqC,CACR,EAEA,OAAO7D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,sBACEkD,EACAlD,EACgC,CAChC,GAAI,CAACkD,EACH,MAAM,IAAI,MAAM,oFAAoF,EAGtG,GAAI,CAACA,EAAqB,kBACxB,MAAM,IAAI,MACR,sGACF,EAOF,IAAMtC,EAAmB,CACvB,OAAQ,OACR,KANkB,4BAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMsC,CACR,EAEA,OAAO9D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAaA,4BACE,CAAE,SAAAsB,CAAS,EACXtB,EACwB,CACxB,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,8EAA8E,EAOhG,IAAMV,EAAmB,CACvB,OAAQ,OACR,KANkB,iCAAiC,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAOrG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,CAQ1B,EAEA,OAAAtB,EAAiB,CACf,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,KACP,GAAGA,GAAgB,QACrB,CACF,EAEOZ,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,kBACEmD,EACAnD,EACoC,CACpC,GAAI,CAACmD,EACH,MAAM,IAAI,MAAM,6EAA6E,EAG/F,GAAI,CAACA,EAAkB,aACrB,MAAM,IAAI,MAAM,0FAA0F,EAO5G,IAAMvC,EAAmB,CACvB,OAAQ,OACR,KANkB,yBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMuC,CACR,EAEA,OAAO/D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,8BACE,CAAE,iBAAAwB,EAAkB,kBAAA2B,CAAkB,EACtCnD,EACoC,CACpC,GAAI,CAACwB,EACH,MAAM,IAAI,MAAM,wFAAwF,EAG1G,GAAI,CAAC2B,EACH,MAAM,IAAI,MAAM,yFAAyF,EAG3G,GAAI,CAACA,EAAkB,aACrB,MAAM,IAAI,MACR,sGACF,EAUF,IAAMvC,EAAmB,CACvB,OAAQ,OACR,KATkB,4CAA4C,QAC9D,qBACA,mBAAmBY,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAM2B,CACR,EAEA,OAAO/D,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,qBACE,CAAE,iBAAAoB,EAAkB,qBAAAgC,CAAqB,EACzCpD,EACuC,CACvC,GAAI,CAACoB,EACH,MAAM,IAAI,MAAM,+EAA+E,EAGjG,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,mFAAmF,EAUrG,IAAMxC,EAAmB,CACvB,OAAQ,QACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBQ,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAOhE,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,kBACE,CAAE,cAAAqB,EAAe,kBAAAgC,CAAkB,EACnCrD,EACoC,CACpC,GAAI,CAACqB,EACH,MAAM,IAAI,MAAM,yEAAyE,EAG3F,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,6EAA6E,EAU/F,IAAMzC,EAAmB,CACvB,OAAQ,QACR,KATkB,kCAAkC,QACpD,kBACA,mBAAmBS,CAAa,CAClC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAOjE,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,aACE,CAAE,SAAAsB,EAAU,aAAAgC,CAAa,EACzBtD,EAC+B,CAC/B,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,+DAA+D,EAGjF,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,mEAAmE,EAOrF,IAAM1C,EAAmB,CACvB,OAAQ,QACR,KANkB,wBAAwB,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAO5F,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAOlE,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EASA,WAAW,CAAE,OAAAuB,EAAQ,WAAAgC,CAAW,EAAoBvD,EAA8D,CAChH,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,2DAA2D,EAG7E,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,+DAA+D,EAOjF,IAAM3C,EAAmB,CACvB,OAAQ,QACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAOnE,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAWA,aACE,CAAE,OAAAuB,EAAQ,WAAAgC,CAAW,EACrBvD,EAC6B,CAC7B,GAAI,CAACuB,EACH,MAAM,IAAI,MAAM,6DAA6D,EAG/E,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,iEAAiE,EAOnF,IAAM3C,EAAmB,CACvB,OAAQ,QACR,KANkB,oBAAoB,QAAQ,WAAY,mBAAmBW,CAAM,CAAC,EAOpF,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAOnE,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EASA,qBACE,CAAE,iBAAAwB,EAAkB,qBAAAR,CAAqB,EACzChB,EACuC,CACvC,GAAI,CAACwB,EACH,MAAM,IAAI,MAAM,+EAA+E,EAGjG,GAAI,CAACR,EACH,MAAM,IAAI,MAAM,mFAAmF,EAGrG,GAAI,CAACA,EAAqB,KACxB,MAAM,IAAI,MAAM,wFAAwF,EAU1G,IAAMJ,EAAmB,CACvB,OAAQ,MACR,KATkB,wCAAwC,QAC1D,qBACA,mBAAmBY,CAAgB,CACrC,EAOE,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMR,CACR,EAEA,OAAO5B,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAYA,eACEc,EACAd,EAA6C,OACrB,CAKxB,IAAMY,EAAmB,CACvB,OAAQ,OACR,KANkB,sBAOlB,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAME,GAA8B,CAAC,CACvC,EAEA,OAAAd,EAAiB,CACf,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,KACP,GAAGA,GAAgB,QACrB,CACF,EAEOZ,EAAY,QAAQwB,EAASZ,CAAc,CACpD,EAcA,2BACE,CAAE,SAAAsB,EAAU,aAAAgC,CAAa,EACzBtD,EACwB,CACxB,GAAI,CAACsB,EACH,MAAM,IAAI,MAAM,6EAA6E,EAG/F,GAAI,CAACgC,EACH,MAAM,IAAI,MAAM,iFAAiF,EAOnG,IAAM1C,EAAmB,CACvB,OAAQ,OACR,KANkB,iCAAiC,QAAQ,aAAc,mBAAmBU,CAAQ,CAAC,EAOrG,gBALuC,CAAC,EAMxC,QAPuB,CAAC,EAQxB,KAAMgC,CACR,EAEA,OAAAtD,EAAiB,CACf,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,KACP,GAAGA,GAAgB,QACrB,CACF,EAEOZ,EAAY,QAAQwB,EAASZ,CAAc,CACpD,CACF,CACF,CCnlFO,SAASwD,GACdC,EACAC,EACAC,EACAC,EACiB,CACjB,GAAI,CAACH,GAAS,OAAOA,GAAU,SAC7B,MAAM,IAAI,MAAM,qBAAqB,EAGvC,GAAI,CAACC,GAAU,OAAOA,GAAW,SAC/B,MAAM,IAAI,MAAM,sBAAsB,EAGxC,GAAI,CAACC,GAAWA,IAAW,OAAOA,GAAW,UAAY,CAACE,EAAQ,SAASF,CAAM,GAC/E,MAAM,IAAI,MAAM,4DAA4DE,EAAQ,KAAK,IAAI,CAAC,EAAE,EAGlG,OAAOC,EAAsB,CAC3B,MAAAL,EACA,OAAAC,EACA,OAAAC,EACA,SAAU,CACR,QAAS,KACT,KAAM,KACN,MAAO,IACT,EACA,OAAQI,EAAiB,EACzB,UAAWC,EAAmB,EAC9B,cAAe,CAAC,CAAE,QAAS,SAAU,CAAC,EACtC,SAAU,wBACV,eAAgBC,EAAkB,EAClC,cAAeA,EAAkB,CAAE,aAAc,EAAM,CAAC,EACxD,WAAYC,EAAwB,CAClC,OAAQ,CAACC,EAA+B,CAAE,IAAK,GAAGC,CAAgB,IAAIX,CAAK,EAAG,CAAC,EAAGQ,EAAkB,CAAC,CACvG,CAAC,EACD,GAAGL,CACL,CAAC,CACH", "names": ["createXhrRequester", "send", "request", "resolve", "baseRequester", "key", "createTimeout", "timeout", "content", "connectTimeout", "responseTimeout", "createBrowserLocalStorageCache", "options", "storage", "namespaceKey", "getStorage", "getNamespace", "setNamespace", "namespace", "removeOutdatedCacheItems", "timeToLive", "filteredNamespaceWithoutOldFormattedCacheItems", "cacheItem", "filteredNamespaceWithoutExpiredItems", "currentTimestamp", "key", "defaultValue", "events", "value", "exists", "createNullCache", "_key", "result", "createFallbackableCache", "caches", "current", "createMemoryCache", "cache", "keyAsString", "promise", "createAlgoliaAgent", "version", "algoliaAgent", "options", "addedAlgoliaAgent", "createAuth", "appId", "<PERSON><PERSON><PERSON><PERSON>", "authMode", "credentials", "createIterablePromise", "func", "validate", "aggregator", "error", "timeout", "retry", "previousResponse", "resolve", "reject", "response", "err", "getAlgoliaAgent", "algoliaAgents", "client", "defaultAlgoliaAgent", "createNullLogger", "_message", "_args", "EXPIRATION_DELAY", "createStatefulHost", "host", "status", "lastUpdate", "isUp", "isTimedOut", "AlgoliaError", "message", "name", "ErrorWithStackTrace", "AlgoliaError", "message", "stackTrace", "name", "RetryError", "ApiError", "status", "DeserializationError", "response", "DetailedApiError", "error", "serializeUrl", "host", "path", "queryParameters", "queryParametersAsString", "serializeQueryParameters", "url", "parameters", "key", "serializeData", "request", "requestOptions", "data", "serializeHeaders", "baseHeaders", "requestHeaders", "requestOptionsHeaders", "headers", "serializedHeaders", "header", "value", "deserializeSuccess", "response", "e", "DeserializationError", "deserializeFailure", "content", "status", "stackFrame", "parsed", "DetailedApiError", "ApiError", "isNetworkError", "isTimedOut", "isRetryable", "isSuccess", "stackTraceWithoutCredentials", "stackTrace", "stackFrameWithoutCredentials", "modifiedHeaders", "createTransporter", "hosts", "hostsCache", "logger", "baseQueryParameters", "algoliaAgent", "timeouts", "requester", "requestsCache", "responsesCache", "createRetryableOptions", "compatibleHosts", "statefulHosts", "compatibleHost", "createStatefulHost", "hostsUp", "hostsTimedOut", "hostsAvailable", "timeoutsCount", "baseTimeout", "retryableRequest", "isRead", "dataQueryParameters", "retry", "retryableHosts", "getTimeout", "RetryError", "timeout", "payload", "pushToStackTrace", "options", "createRequest", "createRetryableRequest", "err", "_", "apiClientVersion", "REGIONS", "getDefaultHosts", "region", "isOnDemandTrigger", "trigger", "isScheduleTrigger", "isSubscriptionTrigger", "createIngestionClient", "appIdOption", "apiKeyOption", "authMode", "algoliaAgents", "regionOption", "options", "auth", "createAuth", "transporter", "createTransporter", "getAlgoliaAgent", "segment", "version", "<PERSON><PERSON><PERSON><PERSON>", "indexName", "objects", "action", "waitForTasks", "batchSize", "referenceIndexName", "requestOptions", "records", "responses", "objectEntries", "i", "obj", "retryCount", "resp", "createIterablePromise", "error", "response", "authenticationCreate", "request", "destinationCreate", "sourceCreate", "taskCreate", "transformationCreate", "path", "parameters", "body", "authenticationID", "destinationID", "sourceID", "taskID", "transformationID", "runID", "eventID", "itemsPerPage", "page", "type", "platform", "sort", "order", "requestPath", "headers", "queryParameters", "status", "startDate", "endDate", "enabled", "sourceType", "triggerType", "withEmailNotifications", "pushTaskPayload", "watch", "runSourcePayload", "authenticationSearch", "destinationSearch", "sourceSearch", "taskSearch", "transformationSearch", "transformationTry", "authenticationUpdate", "destinationUpdate", "sourceUpdate", "taskUpdate", "ingestionClient", "appId", "<PERSON><PERSON><PERSON><PERSON>", "region", "options", "REGIONS", "createIngestionClient", "createNullLogger", "m", "createMemoryCache", "createFallbackableCache", "createBrowserLocalStorageCache", "apiClientVersion"]}